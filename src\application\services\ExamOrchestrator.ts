// Application Service: Exam Orchestrator
import { ExamSession } from '../../domain/entities/ExamSession.js';
import { StartExamUseCase, StartExamRequest } from '../use-cases/StartExamUseCase.js';
import { ProcessFacialDataUseCase, ProcessFacialDataRequest } from '../use-cases/ProcessFacialDataUseCase.js';
import { ExportResultsUseCase, ExportResultsRequest } from '../use-cases/ExportResultsUseCase.js';

export class ExamOrchestrator {
  private currentSession: ExamSession | null = null;

  constructor(
    private startExamUseCase: StartExamUseCase,
    private processFacialDataUseCase: ProcessFacialDataUseCase,
    private exportResultsUseCase: ExportResultsUseCase
  ) {}

  async startExam(request: StartExamRequest): Promise<ExamSession> {
    this.currentSession = await this.startExamUseCase.execute(request);
    return this.currentSession;
  }

  async processFacialData(landmarks: any[]): Promise<void> {
    if (!this.currentSession) {
      throw new Error('No active exam session');
    }

    await this.processFacialDataUseCase.execute({
      session: this.currentSession,
      landmarks
    });
  }

  async nextAction(): Promise<boolean> {
    if (!this.currentSession) {
      return false;
    }

    return this.currentSession.nextAction();
  }

  getCurrentAction(): any {
    return this.currentSession?.getCurrentAction() || null;
  }

  isExamCompleted(): boolean {
    return this.currentSession?.isCompleted() || false;
  }

  async exportResults(format: 'csv' | 'markdown'): Promise<string> {
    if (!this.currentSession) {
      throw new Error('No active exam session');
    }

    return await this.exportResultsUseCase.execute({
      session: this.currentSession,
      format
    });
  }

  getCurrentSession(): ExamSession | null {
    return this.currentSession;
  }
}
