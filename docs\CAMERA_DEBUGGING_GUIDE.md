# Camera Debugging Guide

This guide helps troubleshoot camera issues in the Facial Symmetry Analysis application.

## 🔍 Current Issue

**Problem**: Camera interface shows (buttons, instructions) but video element is not visible.

**Symptoms**:
- ✅ Form submission works
- ✅ Interface switches to exam mode
- ✅ Buttons and instructions appear
- ❌ Video element not showing camera feed
- ❌ Black/empty video area

## 🛠️ Debugging Steps

### 1. Check Browser Console

Open browser Developer Tools (F12) and look for these logs:

#### **Expected Logs (Success)**:
```
Page loaded
FaceMesh available: function
Camera available: function
getUserMedia is available
ExamController: Starting exam for patient: {id: "1", name: "<PERSON>", age: "29"}
ExamController: Video element found: <video id="video"...>
Starting camera...
Video element: <video id="video"...>
Video stream set successfully
Stream: MediaStream {...}
Video metadata loaded
Video playing
MediaPipe FaceMesh found, initializing...
Camera started successfully
```

#### **Error Logs to Look For**:
```
❌ FaceMesh available: undefined
❌ Camera available: undefined
❌ getUserMedia is not available
❌ ExamController: Video element not found!
❌ MediaPipe FaceMesh not found
❌ Failed to start camera: [error details]
```

### 2. Check Camera Permissions

**Chrome**: 
- Click the camera icon in address bar
- Ensure camera is "Allow"
- Try refreshing page

**Firefox**:
- Click shield icon in address bar
- Check camera permissions
- Reload page if needed

**Edge**:
- Click lock/camera icon in address bar
- Verify camera access is allowed

### 3. Check MediaPipe Loading

In browser console, run:
```javascript
console.log('FaceMesh:', window.FaceMesh);
console.log('Camera:', window.Camera);
```

**Expected**: Both should show `function`
**Problem**: If `undefined`, MediaPipe scripts didn't load

### 4. Check Video Element

In browser console, run:
```javascript
const video = document.getElementById('video');
console.log('Video element:', video);
console.log('Video stream:', video.srcObject);
console.log('Video dimensions:', video.videoWidth, 'x', video.videoHeight);
```

### 5. Manual Camera Test

In browser console, run:
```javascript
navigator.mediaDevices.getUserMedia({ video: true })
  .then(stream => {
    const video = document.getElementById('video');
    video.srcObject = stream;
    video.play();
    console.log('Manual camera test successful');
  })
  .catch(err => console.error('Manual camera test failed:', err));
```

## 🔧 Common Fixes

### Fix 1: MediaPipe Loading Issues
```html
<!-- Ensure these scripts load before main app -->
<script src="https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/face_mesh.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js"></script>
```

### Fix 2: Video Element Styling
```css
#video {
  display: block !important;
  width: 640px !important;
  height: 480px !important;
  background: #333;
}
```

### Fix 3: Camera Permissions
1. Clear browser data for localhost:3000
2. Refresh page
3. Allow camera when prompted
4. Check browser settings for camera permissions

### Fix 4: HTTPS Requirement
Some browsers require HTTPS for camera access:
```bash
# Use HTTPS in production
# For local development, use localhost (usually works)
```

### Fix 5: Browser Compatibility
**Supported Browsers**:
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+

**Unsupported**:
- ❌ Internet Explorer
- ❌ Very old browser versions

## 🐛 Advanced Debugging

### Check Network Tab
1. Open DevTools → Network tab
2. Refresh page
3. Look for failed requests to:
   - `@mediapipe/face_mesh/face_mesh.js`
   - `@mediapipe/camera_utils/camera_utils.js`

### Check Security Tab
1. Open DevTools → Security tab
2. Ensure page is "Secure" or "Localhost"
3. Mixed content can block camera access

### Check Application Tab
1. Open DevTools → Application tab
2. Check "Permissions" section
3. Verify camera permission status

## 📋 Troubleshooting Checklist

- [ ] Browser console shows no JavaScript errors
- [ ] MediaPipe scripts loaded successfully
- [ ] Camera permission granted
- [ ] Video element exists in DOM
- [ ] Video element has proper dimensions
- [ ] getUserMedia is available
- [ ] Page served over HTTPS or localhost
- [ ] Browser supports required features

## 🔄 Reset Steps

If camera still not working:

1. **Clear browser data**:
   ```
   Settings → Privacy → Clear browsing data
   Select: Cookies, Cache, Site data
   ```

2. **Restart browser completely**

3. **Try different browser**

4. **Check system camera**:
   - Test camera in other applications
   - Ensure camera is not used by other apps

5. **Restart development server**:
   ```bash
   npm run clean
   npm run build
   npm start
   ```

## 📞 Getting Help

When reporting camera issues, include:

1. **Browser and version**
2. **Operating system**
3. **Console error messages**
4. **Network tab errors**
5. **Camera permission status**
6. **Results of manual tests above**

## 🎯 Quick Test Script

Run this in browser console for comprehensive test:

```javascript
// Comprehensive camera test
async function testCamera() {
  console.log('=== Camera Test Start ===');
  
  // Test 1: MediaPipe availability
  console.log('1. FaceMesh:', typeof window.FaceMesh);
  console.log('1. Camera:', typeof window.Camera);
  
  // Test 2: getUserMedia availability
  console.log('2. getUserMedia available:', !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia));
  
  // Test 3: Video element
  const video = document.getElementById('video');
  console.log('3. Video element found:', !!video);
  
  // Test 4: Camera access
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
    console.log('4. Camera access: SUCCESS');
    
    // Test 5: Video stream
    if (video) {
      video.srcObject = stream;
      await video.play();
      console.log('5. Video playing: SUCCESS');
    }
  } catch (err) {
    console.error('4. Camera access: FAILED', err);
  }
  
  console.log('=== Camera Test End ===');
}

testCamera();
```

This script will help identify exactly where the camera setup is failing.
