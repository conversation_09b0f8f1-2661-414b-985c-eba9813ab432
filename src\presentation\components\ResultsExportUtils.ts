// ResultsExportUtils.ts
// Utility functions for generating CSV and Markdown content from exam results

export function generateCSVContent(results: any): string {
  const patient = results.patientInfo;
  const metrics = results.symmetryMetrics;

  let csv = 'Field,Value\n';
  csv += `Patient ID,${patient.id || 'N/A'}\n`;
  csv += `Patient Name,${patient.name || 'N/A'}\n`;
  csv += `Age,${patient.age || 'N/A'}\n`;
  csv += `Exam Date,${new Date(results.timestamp).toLocaleDateString()}\n`;
  csv += `Exam Time,${new Date(results.timestamp).toLocaleTimeString()}\n`;
  csv += `Overall Score,${results.overallScore}%\n`;
  csv += `Eyebrow Symmetry,${metrics.eyebrowSymmetry}%\n`;
  csv += `Eye Symmetry,${metrics.eyeSymmetry}%\n`;
  csv += `Mouth Symmetry,${metrics.mouthSymmetry}%\n`;

  // Add asymmetries
  csv += '\nAsymmetries\n';
  csv += 'Feature,Severity,Measurement,Recommendation\n';
  results.asymmetries.forEach((asymmetry: any) => {
    csv += `${asymmetry.feature},${asymmetry.severity},${asymmetry.measurement},${asymmetry.recommendation}\n`;
  });

  return csv;
}

export function generateMarkdownContent(results: any): string {
  const patient = results.patientInfo;
  const metrics = results.symmetryMetrics;

  let markdown = '# Facial Symmetry Analysis Results\n\n';
  markdown += '## Patient Information\n\n';
  markdown += `- **Patient ID:** ${patient.id || 'N/A'}\n`;
  markdown += `- **Name:** ${patient.name || 'N/A'}\n`;
  markdown += `- **Age:** ${patient.age || 'N/A'} years\n`;
  markdown += `- **Exam Date:** ${new Date(results.timestamp).toLocaleDateString()}\n`;
  markdown += `- **Exam Time:** ${new Date(results.timestamp).toLocaleTimeString()}\n`;
  markdown += `- **Overall Score:** ${results.overallScore}%\n\n`;

  markdown += '## Symmetry Measurements\n\n';
  markdown += `| Feature | Score | Status |\n`;
  markdown += `|---------|-------|--------|\n`;
  markdown += `| Eyebrow Symmetry | ${metrics.eyebrowSymmetry}% | ${metrics.eyebrowSymmetry >= 85 ? 'Excellent' : metrics.eyebrowSymmetry >= 70 ? 'Good' : 'Needs Attention'} |\n`;
  markdown += `| Eye Symmetry | ${metrics.eyeSymmetry}% | ${metrics.eyeSymmetry >= 70 ? 'Good' : 'Needs Attention'} |\n`;
  markdown += `| Mouth Symmetry | ${metrics.mouthSymmetry}% | ${metrics.mouthSymmetry >= 85 ? 'Excellent' : metrics.mouthSymmetry >= 70 ? 'Good' : 'Needs Attention'} |\n\n`;

  if (results.asymmetries.length > 0) {
    markdown += '## Detected Asymmetries\n\n';
    markdown += `| Feature | Severity | Measurement | Recommendation |\n`;
    markdown += `|---------|----------|-------------|----------------|\n`;
    results.asymmetries.forEach((asymmetry: any) => {
      markdown += `| ${asymmetry.feature} | ${asymmetry.severity} | ${asymmetry.measurement} | ${asymmetry.recommendation} |\n`;
    });
  } else {
    markdown += '## Asymmetries\n\n';
    markdown += '✓ No significant asymmetries detected. Facial symmetry appears to be within normal ranges.\n';
  }

  return markdown;
}
