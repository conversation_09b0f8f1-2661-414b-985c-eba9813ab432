<!-- Dedicated Results Page -->
<style>
  /* Responsive results page styles */
  .results-container {
    background: #f6f8fa;
    min-height: 100vh;
    padding: 20px;
    font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
  }

  .results-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(44,62,80,0.12);
    padding: 30px;
    margin: 0 auto;
    max-width: 600px;
  }

  .results-loading {
    text-align: center;
    padding: 50px 20px;
  }

  .results-error {
    text-align: center;
    padding: 50px 20px;
  }

  .error-card {
    border-left: 4px solid #e74c3c;
    max-width: 500px;
    margin: 0 auto;
  }

  .error-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 25px;
  }

  .results-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 140px;
  }

  .results-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }

  .results-btn.primary {
    background: #3498db;
    color: white;
  }

  .results-btn.primary:hover {
    background: #2980b9;
  }

  .results-btn.secondary {
    background: #95a5a6;
    color: white;
  }

  .results-btn.secondary:hover {
    background: #7f8c8d;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e1e8ed;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px auto;
  }

  .error-list {
    text-align: left;
    color: #7f8c8d;
    margin: 0 0 25px 0;
    padding-left: 20px;
    line-height: 1.6;
  }

  /* Media queries for results page */
  @media (max-width: 768px) {
    .results-container {
      padding: 10px;
    }

    .results-card {
      padding: 20px;
      margin: 0;
      width: 100%;
      box-sizing: border-box;
    }

    .results-loading,
    .results-error {
      padding: 20px 10px;
    }

    .error-actions {
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }

    .results-btn {
      min-width: 120px;
      padding: 12px 20px;
      font-size: 0.9em;
      width: 100%;
      max-width: 250px;
    }

    /* Fix overall symmetry score layout on mobile */
    .overall-symmetry-container {
      display: flex !important;
      flex-direction: column !important;
      gap: 20px !important;
    }

    .score-display-card,
    .clinical-interpretation-card {
      width: 100% !important;
      margin: 0 !important;
      padding: 20px !important;
    }

    .score-display-card h3 {
      font-size: 1.2em !important;
    }

    .score-display-card div[style*="font-size: 4em"] {
      font-size: 3em !important;
    }
  }

  @media (max-width: 480px) {
    .results-container {
      padding: 10px;
    }

    .results-card {
      padding: 15px;
      border-radius: 8px;
    }

    .results-loading,
    .results-error {
      padding: 20px 10px;
    }

    .results-btn {
      min-width: 100px;
      padding: 10px 16px;
      font-size: 0.85em;
      width: 100%;
      max-width: 200px;
    }

    .spinner {
      width: 30px;
      height: 30px;
      border-width: 3px;
    }
  }

  /* Results Footer Styles */
  .results-footer {
    background: rgba(44, 62, 80, 0.05);
    border-top: 1px solid rgba(44, 62, 80, 0.1);
    text-align: center;
    padding: 1.5rem;
    margin-top: 2rem;
  }

  .results-footer .footer-content {
    max-width: 1200px;
    margin: 0 auto;
  }

  .results-footer .version-info {
    font-size: 0.85rem;
    margin: 0;
    color: #7f8c8d;
    font-weight: 500;
  }
</style>

<div id="resultsContainer" class="results-container">
  <!-- Loading State -->
  <div id="resultsLoading" class="results-loading" style="display: block;">
    <div class="results-card">
      <div class="spinner"></div>
      <h3 style="color: #2c3e50; margin: 0 0 10px 0;">Loading Results...</h3>
      <p style="color: #7f8c8d; margin: 0;">Please wait while we prepare your facial symmetry analysis.</p>
      <div id="mobileDebugInfo" style="
        margin-top: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        font-size: 12px;
        color: #6c757d;
        display: none;
      ">
        <strong>Debug Info:</strong><br>
        <span id="debugUserAgent"></span><br>
        <span id="debugTimestamp"></span>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div id="resultsError" class="results-error" style="display: none;">
    <div class="results-card error-card">
      <h3 style="color: #e74c3c; margin: 0 0 15px 0;">
        <i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>
        No Results Found
      </h3>
      <p style="color: #7f8c8d; margin: 0 0 20px 0; line-height: 1.6;">
        We couldn't find any examination results to display. This might happen if:
      </p>
      <ul class="error-list">
        <li>You navigated directly to this page without completing an examination</li>
        <li>Your examination data has expired or been cleared</li>
        <li>There was an error during the examination process</li>
      </ul>
      <div class="error-actions">
        <button id="startNewExam" class="results-btn primary" onclick="window.location.hash = ''">
          Start New Examination
        </button>
        <button id="goHome" class="results-btn secondary" onclick="window.location.hash = ''">
          Go to Home
        </button>
      </div>
    </div>
  </div>

  <!-- Results Content (will be populated by ResultsController) -->
  <div id="resultsContent" style="display: none;">
    <!-- Results will be dynamically inserted here -->
  </div>

  <!-- Footer with Version Information -->
  <footer class="results-footer">
    <div class="footer-content">
      <p class="version-info">
        Facial Symmetry Analysis v1.2.0 | Released: December 2024
      </p>
    </div>
  </footer>
</div>

<style>
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

<script>
// Mobile-specific results page debugging
(function() {
  console.log('Results page loaded, checking mobile compatibility...');

  const isMobile = /Mobi|Android/i.test(navigator.userAgent);
  console.log('Is mobile device:', isMobile);

  if (isMobile) {
    // Show debug info on mobile
    const debugInfo = document.getElementById('mobileDebugInfo');
    const userAgentSpan = document.getElementById('debugUserAgent');
    const timestampSpan = document.getElementById('debugTimestamp');

    if (debugInfo && userAgentSpan && timestampSpan) {
      userAgentSpan.textContent = `User Agent: ${navigator.userAgent.substring(0, 50)}...`;
      timestampSpan.textContent = `Loaded at: ${new Date().toLocaleTimeString()}`;
      debugInfo.style.display = 'block';
    }

    // Add mobile-specific error handling
    window.addEventListener('error', (e) => {
      console.error('Mobile results page error:', e);
    });

    // Check for localStorage availability
    try {
      const testKey = 'mobile_test';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
      console.log('localStorage is available on mobile');
    } catch (e) {
      console.error('localStorage not available on mobile:', e);
    }
  }

  // Enhanced results container detection
  const container = document.getElementById('resultsContainer');
  if (container) {
    console.log('Results container found successfully');
    container.setAttribute('data-mobile-ready', 'true');
  } else {
    console.error('Results container not found!');
  }
})();
</script>
