// Domain Entity: Exam Action
export class ExamAction {
  constructor(
    public readonly name: string,
    public readonly instruction: string,
    public readonly duration: number = 5000,
    public readonly targetMetric?: string
  ) {}

  static createFromConfig(config: {
    action: string;
    instruction: string;
    duration?: number;
    targetMetric?: string;
  }): ExamAction {
    return new ExamAction(
      config.action,
      config.instruction,
      config.duration || 5000,
      config.targetMetric
    );
  }
}
