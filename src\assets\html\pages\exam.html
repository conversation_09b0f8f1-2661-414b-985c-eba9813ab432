
<!-- This is a partial file for the exam view. Do not include <html>, <head>, or <body> tags. -->
<style>
/* Modern Medical Platform Design - Exam View */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.exam-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: #1a202c;
  line-height: 1.6;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-icon {
  color: #3b82f6;
  display: flex;
  align-items: center;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
}

.nav-btn {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

/* Main Content */
.exam-container > *:not(.navbar) {
  padding-left: 2rem;
  padding-right: 2rem;
}

/* Header Section */
.exam-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1.5rem 2rem;
  margin: 2rem 0;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.8);
  text-align: center;
}

.exam-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.exam-subtitle {
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
}

/* Camera Views Section */
.camera-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.camera-views {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  justify-items: center;
  align-items: start;
  margin: 1.5rem 0;
}

/* Force mobile stacking by default */
@media (max-width: 900px) {
  .camera-views {
    grid-template-columns: 1fr !important;
    gap: 1.5rem !important;
  }

  .camera-view {
    width: 100% !important;
    max-width: 500px !important;
  }
}

.camera-view {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: #000;
  width: 100%;
  max-width: 480px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.camera-view.live {
  border-color: #10b981;
  box-shadow: 0 10px 30px rgba(16, 185, 129, 0.2);
}

.camera-view.analysis {
  border-color: #3b82f6;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
}

.camera-header {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8));
  padding: 1rem 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.camera-header.live {
  color: #10b981;
}

.camera-header.analysis {
  color: #3b82f6;
}

.camera-header::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.camera-header.live::before {
  background: #10b981;
}

.camera-header.analysis::before {
  background: #3b82f6;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.camera-content {
  position: relative;
  width: 100%;
  aspect-ratio: 4/3;
}

.camera-content video,
.camera-content canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.camera-content canvas {
  z-index: 2;
  pointer-events: none;
  opacity: 0.9;
}

/* Controls Section */
.controls-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.controls-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1.5rem;
  text-align: center;
}

.button-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.exam-btn {
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.exam-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.exam-btn:active {
  transform: translateY(0);
}

.exam-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.exam-btn.success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.exam-btn.purple {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.exam-btn.orange {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.exam-btn.teal {
  background: linear-gradient(135deg, #14b8a6, #0d9488);
  color: white;
}

/* Instruction Section */
.instruction-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.instruction-container {
  max-width: 800px;
  margin: 0 auto;
}

.instruction-card {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: 16px;
  font-size: 1.125rem;
  font-weight: 600;
  text-align: center;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  line-height: 1.5;
  position: relative;
  overflow: hidden;
}

.instruction-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Mobile Instructions */
.mobile-instructions {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.mobile-instruction-card {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  border: 2px solid #3b82f6;
  border-radius: 16px;
  padding: 1.5rem 2rem;
  color: #1e40af;
}

.mobile-instruction-card h4 {
  margin: 0 0 1rem 0;
  color: #1d4ed8;
  font-size: 1.25rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mobile-instruction-card ul {
  margin: 0;
  padding-left: 1.5rem;
  line-height: 1.6;
}

.mobile-instruction-card li {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .exam-container > *:not(.navbar) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .camera-section,
  .controls-section,
  .instruction-section,
  .mobile-instructions {
    padding: 1.5rem;
  }

  .exam-btn {
    min-width: 120px;
    padding: 0.75rem 1.25rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .exam-container > *:not(.navbar) {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .nav-container {
    padding: 0 1rem;
  }

  .exam-header {
    padding: 1rem 1.5rem;
  }

  .exam-title {
    font-size: 1.5rem;
  }

  .camera-section,
  .controls-section,
  .instruction-section,
  .mobile-instructions {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .button-container {
    gap: 0.75rem;
  }

  .exam-btn {
    min-width: 100px;
    padding: 0.75rem 1rem;
    font-size: 0.75rem;
    flex: 1 1 calc(50% - 0.375rem);
    max-width: none;
  }

  .instruction-card {
    padding: 1.25rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .exam-container > *:not(.navbar) {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .nav-container {
    padding: 0 0.75rem;
  }

  .brand-text {
    font-size: 1rem;
  }

  .nav-btn {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
  }

  .exam-header {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .exam-title {
    font-size: 1.25rem;
  }

  .exam-subtitle {
    font-size: 0.875rem;
  }

  .camera-section,
  .controls-section,
  .instruction-section,
  .mobile-instructions {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 16px;
  }

  .camera-view {
    border-radius: 12px;
  }

  .camera-header {
    font-size: 0.75rem;
    padding: 0.75rem 1rem;
  }

  .button-container {
    gap: 0.5rem;
  }

  .exam-btn {
    min-width: 80px;
    padding: 0.75rem 0.5rem;
    font-size: 0.7rem;
    flex: 1 1 calc(50% - 0.25rem);
    border-radius: 8px;
  }

  .instruction-card {
    padding: 1rem 1.25rem;
    font-size: 0.875rem;
    border-radius: 12px;
    line-height: 1.4;
  }

  .mobile-instruction-card {
    padding: 1rem 1.25rem;
    border-radius: 12px;
  }

  .mobile-instruction-card h4 {
    font-size: 1rem;
  }

  .mobile-instruction-card li {
    font-size: 0.8rem;
  }
}
</style>

<div id="examView" class="exam-container">
  <!-- Navigation -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <div class="brand-icon">
          <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="currentColor"/>
            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <span class="brand-text">Live Camera Assessment</span>
      </div>
      <div class="nav-actions">
        <button class="nav-btn" onclick="goBackToHome()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" fill="currentColor"/>
          </svg>
          Back to Home
        </button>
      </div>
    </div>
  </nav>

  <!-- Header Section -->
  <div class="exam-header">
    <h1 class="exam-title">Facial Symmetry Examination</h1>
    <p class="exam-subtitle">Clinical-grade assessment using advanced computer vision</p>
  </div>

  <!-- Camera Views Section -->
  <div class="camera-section">
    <div class="camera-views">
      <!-- Live Camera Feed -->
      <div class="camera-view live">
        <div class="camera-header live">Live Camera</div>
        <div class="camera-content">
          <video id="video" autoplay muted playsinline style="display: block; background: #333;"></video>
          <canvas id="canvas"></canvas>
        </div>
      </div>

      <!-- Analysis View -->
      <div class="camera-view analysis">
        <div class="camera-header analysis">Analysis View</div>
        <div class="camera-content">
          <canvas id="analysisCanvas" style="display: block; background: #222;"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Controls Section -->
  <div class="controls-section">
    <h3 class="controls-title">Examination Controls</h3>
    <div class="button-container">
      <button id="nextBtn" class="exam-btn primary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
        </svg>
        Next Action
      </button>
      <button id="finishBtn" class="exam-btn success" style="display: none;">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
        </svg>
        Finish Test
      </button>
      <button id="cycleAnalysisBtn" class="exam-btn purple">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z" fill="currentColor"/>
        </svg>
        Analysis Mode
      </button>
      <!-- <button id="speechToggleBtn" class="exam-btn orange" title="Toggle speech instructions">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M3 9V15H7L12 20V4L7 9H3ZM16.5 12C16.5 10.23 15.5 8.71 14 7.97V16.02C15.5 15.29 16.5 13.77 16.5 12ZM14 3.23V5.29C16.89 6.15 19 8.83 19 12S16.89 17.85 14 18.71V20.77C18.01 19.86 21 16.28 21 12S18.01 4.14 14 3.23Z" fill="currentColor"/>
        </svg>
        Speech ON
      </button> -->
      <button id="switchCameraBtn" class="exam-btn teal" style="display: none;" title="Switch between front and back cameras">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 4H16.83L15 2H9L7.17 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM12 17C9.24 17 7 14.76 7 12S9.24 7 12 7S17 9.24 17 12S14.76 17 12 17Z" fill="currentColor"/>
        </svg>
        Switch Camera
      </button>
    </div>
  </div>

  <!-- Instruction Section -->
  <div id="instructionArea" class="instruction-section" style="display: none;">
    <div class="instruction-container">
      <div id="instruction" class="instruction-card">
        Ready to begin facial symmetry examination
      </div>
    </div>
  </div>

  <!-- Mobile Camera Positioning Instructions -->
  <div id="mobileInstructions" class="mobile-instructions" style="display: none;">
    <div class="mobile-instruction-card">
      <h4>
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M17,19H7V5H17M17,1H7C5.89,1 5,1.89 5,3V21C5,22.11 5.89,23 7,23H17C18.11,23 19,22.11 19,21V3C19,1.89 18.11,1 17,1Z" fill="currentColor"/>
        </svg>
        Mobile Camera Setup
      </h4>
      <ul>
        <li>Hold device at arm's length (about 18-24 inches away)</li>
        <li>Ensure your entire face is visible in the camera view</li>
        <li>Include forehead, chin, and both ears if possible</li>
        <li>Use good lighting - face the light source</li>
        <li>Keep device steady during examination</li>
      </ul>
    </div>
  </div>

  <!-- Exam Interface -->
  <div id="examInterface" style="display: none; text-align: center; margin: 20px auto; max-width: 600px;"></div>
</div>

<script>
// Navigation function
function goBackToHome() {
  window.location.href = '/';
}

// Show mobile instructions on mobile devices
(function() {
  const isMobile = /Mobi|Android/i.test(navigator.userAgent);
  console.log('Mobile device detected in exam view:', isMobile);

  if (isMobile) {
    const mobileInstructions = document.getElementById('mobileInstructions');
    if (mobileInstructions) {
      mobileInstructions.style.display = 'block';
      console.log('Mobile camera instructions displayed');
    }
  }
})();
</script>
