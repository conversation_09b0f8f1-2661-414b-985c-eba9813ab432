<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF Export Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 10px;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 PDF Export Functionality Test</h1>
        <p>This page tests the new PDF export functionality that replaces CSV and Markdown exports.</p>

        <div class="test-section">
            <h2>🧪 Test Controls</h2>
            <button id="testPdfExport" class="button">
                📄 Test PDF Export
            </button>
            <button id="testVersionMetadata" class="button">
                🔍 Test Version Metadata
            </button>
            <button id="clearLog" class="button" style="background: #6c757d;">
                🗑️ Clear Log
            </button>
        </div>

        <div class="test-section">
            <h2>📊 Test Status</h2>
            <div id="testStatus" class="status info">Ready to test PDF export functionality</div>
        </div>

        <div class="test-section">
            <h2>📝 Test Log</h2>
            <div id="testLog" class="log">Waiting for test execution...</div>
        </div>

        <div class="test-section">
            <h2>📋 Expected Features</h2>
            <ul>
                <li>✅ Professional clinical report formatting</li>
                <li>✅ Complete version metadata inclusion</li>
                <li>✅ Patient information section</li>
                <li>✅ Clinical measurements with color coding</li>
                <li>✅ Detailed measurements table</li>
                <li>✅ Synkinesis detection results</li>
                <li>✅ Data integrity and regulatory compliance info</li>
                <li>✅ Clinical validation warnings</li>
                <li>✅ Professional medical report footer</li>
                <li>✅ CDN-based jsPDF loading (browser compatible)</li>
                <li>✅ Version information display on results page</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔧 Fixed Issues</h2>
            <ul>
                <li>🔧 <strong>Module Import Error</strong>: jsPDF now loads from CDN instead of ES modules</li>
                <li>🔧 <strong>Missing Version Info</strong>: Version metadata now displayed prominently on results page</li>
                <li>🔧 <strong>Browser Compatibility</strong>: Dynamic script loading for jsPDF libraries</li>
                <li>🔧 <strong>Clinical Warnings</strong>: Validation status warnings now visible to users</li>
            </ul>
        </div>
    </div>

    <script type="module">
        // Mock examination results data for testing
        const mockResults = {
            timestamp: Date.now(),
            patientInfo: {
                id: 'TEST-PDF-001',
                name: 'PDF Test Patient',
                age: 42
            },
            overallScore: 78.5,
            symmetryMetrics: {
                eyebrowSymmetry: 85.2,
                eyeSymmetry: 82.0,
                mouthSymmetry: 68.5,
                leftEyebrowElevation: 18.35,
                rightEyebrowElevation: 16.59,
                eyebrowAsymmetry: 12.8,
                leftEyeClosure: 75.9,
                rightEyeClosure: 68.0,
                eyeAsymmetry: 15.0,
                leftMouthMovement: 21.63,
                rightMouthMovement: 18.46,
                mouthAsymmetry: 18.5,
                synkinesisDetected: true,
                synkinesisResults: [
                    { type: 'eye-mouth', severity: 'moderate', percentage: 22.5 }
                ]
            }
        };

        let logElement, statusElement;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;

            console.log(message);
        }

        function setStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        async function testPdfExport() {
            try {
                log('🚀 Starting PDF export test...');
                setStatus('Testing PDF export functionality...', 'info');

                // Import ResultsView
                const { ResultsView } = await import('./dist/presentation/components/ResultsView.js');
                log('✅ ResultsView imported successfully');

                // Create ResultsView instance
                const resultsView = new ResultsView(mockResults);
                log('✅ ResultsView instance created');

                // Test PDF export
                log('📄 Attempting PDF export...');
                await resultsView.exportToPDF();

                log('✅ PDF export completed successfully!');
                setStatus('PDF export test completed successfully! Check your downloads folder.', 'success');

            } catch (error) {
                log(`❌ PDF export test failed: ${error.message}`, 'error');
                setStatus(`PDF export test failed: ${error.message}`, 'error');
                console.error('PDF export error:', error);
            }
        }

        async function testVersionMetadata() {
            try {
                log('🔍 Testing version metadata...');
                setStatus('Testing version metadata...', 'info');

                // Import VersionManager
                const { VersionManager } = await import('./dist/core/version/VersionManager.js');
                log('✅ VersionManager imported successfully');

                // Test version information
                const version = VersionManager.getVersionString();
                const metadata = VersionManager.getExportMetadata();
                const warnings = VersionManager.getClinicalValidationWarnings();

                log(`📋 Application Version: ${version}`);
                log(`📋 Clinical Status: ${metadata.clinicalValidationStatus}`);
                log(`📋 FDA Status: ${metadata.regulatoryStatus.fdaStatus}`);
                log(`📋 HIPAA Compliant: ${metadata.regulatoryStatus.hipaaCompliant}`);
                log(`📋 Clinical Warnings: ${warnings.length > 0 ? warnings.join(', ') : 'None'}`);

                setStatus('Version metadata test completed successfully!', 'success');

            } catch (error) {
                log(`❌ Version metadata test failed: ${error.message}`, 'error');
                setStatus(`Version metadata test failed: ${error.message}`, 'error');
                console.error('Version metadata error:', error);
            }
        }

        function clearLog() {
            logElement.textContent = 'Log cleared...\n';
            setStatus('Ready for testing', 'info');
        }

        // Initialize when page loads
        window.addEventListener('load', () => {
            logElement = document.getElementById('testLog');
            statusElement = document.getElementById('testStatus');

            // Attach event listeners
            document.getElementById('testPdfExport').addEventListener('click', testPdfExport);
            document.getElementById('testVersionMetadata').addEventListener('click', testVersionMetadata);
            document.getElementById('clearLog').addEventListener('click', clearLog);

            log('🎯 PDF Export Test Page Loaded');
            log('📋 Mock patient data prepared');
            log('🔧 Test controls ready');
            setStatus('Ready to test PDF export functionality', 'info');
        });
    </script>
</body>
</html>
