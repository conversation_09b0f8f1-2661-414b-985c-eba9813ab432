<div id="homeView" class="home-container">
  <!-- Navigation Header -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <div class="brand-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z" fill="currentColor"/>
          </svg>
        </div>
        <span class="brand-text">Facial Symmetry Analysis</span>
      </div>
      <div class="nav-links">
        <!-- <a href="#" class="nav-link">About</a>
        <a href="#" class="nav-link">Documentation</a>
        <a href="#" class="nav-link">Support</a> -->
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-container">
      <div class="hero-content">
        <div class="hero-badge">
          <span class="badge-text">Clinical Grade Analysis</span>
        </div>
        <h1 class="hero-title">
          Advanced Facial Symmetry
          <span class="title-highlight">Assessment Platform</span>
        </h1>
        <p class="hero-description">
          Professional-grade facial symmetry analysis for Bell's palsy assessment using cutting-edge computer vision technology and validated clinical grading standards.
        </p>
        <div class="hero-stats">
          <div class="stat-item">
            <div class="stat-number">468+</div>
            <div class="stat-label">Facial Landmarks</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">90%</div>
            <div class="stat-label">Accuracy Rate</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">1min</div>
            <div class="stat-label">Assessment Time</div>
          </div>
        </div>
      </div>
      <div class="hero-visual">
        <div class="visual-card">
          <div class="visual-header">
            <h3 style="font-size:1rem;">Start Assessment</h3>
            <div class="status-indicator">
              <span class="status-dot"></span>
              Ready
            </div>
          </div>

          <!-- Assessment Mode Selection -->
          <div id="modeSelection" class="mode-selection">
            <div class="mode-header">
              <h4 class="section-title">Choose Assessment Method</h4>
              <p class="mode-description">Select how you'd like to perform the facial symmetry analysis</p>
            </div>

            <div class="mode-options">
              <div class="mode-card" data-mode="live" onclick="selectAssessmentMode('live')">
                <div class="mode-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z" fill="currentColor"/>
                  </svg>
                </div>
                <h3 class="mode-title">📹 Live Camera Assessment</h3>
                <p class="mode-subtitle">Real-time facial analysis using your camera</p>
                <ul class="mode-features">
                  <li>✓ Immediate results</li>
                  <li>✓ Guided instructions</li>
                  <li>✓ Real-time feedback</li>
                  <li>✓ Quick 1-minute assessment</li>
                </ul>
                <div class="mode-badge">Recommended</div>
              </div>

              <div class="mode-card" data-mode="images" onclick="selectAssessmentMode('images')">
                <div class="mode-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9,2V7.38L10.5,8.88L12,7.38V2H9M20,2H17V9L15,7L13,9V2H4A2,2 0 0,0 2,4V20A2,2 0 0,0 4,22H20A2,2 0 0,0 22,20V4A2,2 0 0,0 20,2M8.5,12.5L11,16.5L14.5,11L19,18H5L8.5,12.5Z" fill="currentColor"/>
                  </svg>
                </div>
                <h3 class="mode-title">📸 Image-Based Assessment</h3>
                <p class="mode-subtitle">Upload photos for detailed analysis</p>
                <ul class="mode-features">
                  <li>✓ High-quality images</li>
                  <li>✓ Controlled conditions</li>
                  <li>✓ Better accuracy</li>
                  <li>✓ Professional documentation</li>
                </ul>
                <div class="mode-badge mode-badge-new">Enhanced</div>
              </div>
            </div>
          </div>

          <!-- Patient Form (Hidden initially) -->
          <form id="patientForm" class="assessment-form" style="display: none;">
            <div class="form-section">
              <h4 class="section-title" style="font-size:0.95rem;">Patient Information</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label for="pid" class="form-label">Patient ID</label>
                  <input type="text" id="pid" class="form-input" required placeholder="e.g., P001" value="1">
                </div>
                <div class="form-group">
                  <label for="page" class="form-label">Age</label>
                  <input type="number" id="page" class="form-input" required min="0" max="120" placeholder="Age" value="29">
                </div>
              </div>
              <div class="form-group">
                <label for="pname" class="form-label">Full Name</label>
                <input type="text" id="pname" class="form-input" required placeholder="Enter patient name" value="Ali">
              </div>
            </div>

            <div class="form-section" id="liveAssessmentChecklist">
              <h4 class="section-title" style="font-size:0.95rem;">Before you begin (Live Assessment)</h4>
              <div class="checklist">
                <div class="checklist-item">
                  <div class="check-icon">✓</div>
                  <span>Good lighting conditions</span>
                </div>
                <div class="checklist-item">
                  <div class="check-icon">✓</div>
                  <span>Camera at eye level</span>
                </div>
                <div class="checklist-item">
                  <div class="check-icon">✓</div>
                  <span>Remove glasses/masks</span>
                </div>
                <div class="checklist-item">
                  <div class="check-icon">✓</div>
                  <span>Quiet environment</span>
                </div>
              </div>
            </div>

            <div class="form-section" id="imageAssessmentChecklist" style="display: none;">
              <h4 class="section-title" style="font-size:0.95rem;">Before you begin (Image Assessment)</h4>
              <div class="checklist">
                <div class="checklist-item">
                  <div class="check-icon">✓</div>
                  <span>High-resolution images (min 1080p)</span>
                </div>
                <div class="checklist-item">
                  <div class="check-icon">✓</div>
                  <span>Good lighting, no shadows</span>
                </div>
                <div class="checklist-item">
                  <div class="check-icon">✓</div>
                  <span>Face centered and upright</span>
                </div>
                <div class="checklist-item">
                  <div class="check-icon">✓</div>
                  <span>Clear facial expressions</span>
                </div>
              </div>
            </div>

            <div class="form-actions" style="flex-direction: column; gap: 0.75rem;">
              <button type="button" class="back-btn" style="width: 100%; justify-content: center;" onclick="goBackToModeSelection()">
                <span class="btn-icon" style="display: flex; align-items: center; justify-content: center;">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" fill="currentColor"/>
                  </svg>
                </span>
                Back to Mode Selection
              </button>

              <button type="submit" class="start-assessment-btn" style="width: 100%;">
              <span class="btn-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                </svg>
              </span>
              <span id="startBtnText">Begin Facial Assessment</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="features-section">
    <div class="features-container">
      <div class="section-header">
        <h2 class="section-title">Clinical-Grade Technology</h2>
        <p class="section-description">
          Powered by advanced AI and validated against clinical standards for reliable Bell's palsy assessment.
        </p>
      </div>

      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">Precise Landmark Detection</h3>
          <p class="feature-description">
            468-point facial landmark detection using MediaPipe technology for comprehensive facial analysis.
          </p>
        </div>

        <!-- <div class="feature-card">
          <div class="feature-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17,12C17,14.42 15.28,16.44 13,16.9V21H11V16.9C8.72,16.44 7,14.42 7,12C7,9.58 8.72,7.56 11,7.1V2H13V7.1C15.28,7.56 17,9.58 17,12M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">House-Brackmann Grading</h3>
          <p class="feature-description">
            Automated clinical grading system following established medical standards for Bell's palsy assessment.
          </p>
        </div> -->

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">Clinical Documentation</h3>
          <p class="feature-description">
            Professional PDF reports with detailed measurements, analysis results, and clinical recommendations.
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17,19H7V5H17M17,1H7C5.89,1 5,1.89 5,3V21C5,22.11 5.89,23 7,23H17C18.11,23 19,22.11 19,21V3C19,1.89 18.11,1 17,1Z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">Mobile Compatible</h3>
          <p class="feature-description">
            Responsive design works seamlessly across desktop, tablet, and mobile devices for flexible usage.
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C14.8,12.6 13.9,13.5 12.8,13.5H11.2C10.1,13.5 9.2,12.6 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,9.5V10.8C10.5,11.1 10.8,11.3 11.1,11.3H12.9C13.2,11.3 13.5,11.1 13.5,10.8V9.5C13.5,8.7 12.8,8.2 12,8.2Z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">Privacy & Security</h3>
          <p class="feature-description">
            All processing happens locally in your browser. No patient data is transmitted or stored externally.
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">Real-time Analysis</h3>
          <p class="feature-description">
            Instant feedback and live visualization during assessment with guided instructions for optimal results.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <div class="footer-brand">
          <div class="brand-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z" fill="currentColor"/>
            </svg>
          </div>
          <span class="brand-text">FacialSymmetry</span>
        </div>
        <div class="footer-info">
          <p class="version-text">Version 1.0.0 • Released May 2025</p>
          <p class="copyright">© 2025 Facial Symmetry Analysis Platform</p>
        </div>
      </div>
    </div>
  </footer>
</div>

<style>
/* Modern Medical Platform Design */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: #1a202c;
  line-height: 1.6;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-icon {
  color: #3b82f6;
  display: flex;
  align-items: center;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #64748b;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: #3b82f6;
}

/* Hero Section */
.hero-section {
  padding: 4rem 0 6rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  width: fit-content;
}

.badge-text {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  color: #1a202c;
}

.title-highlight {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.125rem;
  color: #64748b;
  line-height: 1.7;
  max-width: 500px;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: #3b82f6;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  margin-top: 0.25rem;
}

/* Visual Card */
.hero-visual {
  display: flex;
  justify-content: center;
}

.visual-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.8);
  width: 100%;
  max-width: 480px;
}

.visual-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.visual-header h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #059669;
  font-weight: 600;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #059669;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Assessment Form */
.assessment-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.form-input {
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Checklist */
.checklist {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.checklist-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.check-icon {
  width: 20px;
  height: 20px;
  background: #059669;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  flex-shrink: 0;
}

.checklist-item span {
  font-size: 0.875rem;
  color: #4b5563;
}

/* Start Assessment Button */
.start-assessment-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.start-assessment-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.4);
}

.btn-icon {
  display: flex;
  align-items: center;
}

/* Features Section */
.features-section {
  padding: 6rem 0;
  background: white;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.125rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.feature-icon {
  color: #3b82f6;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1rem;
}

.feature-description {
  color: #64748b;
  line-height: 1.6;
}

/* Footer */
.footer {
  background: #1a202c;
  color: white;
  padding: 3rem 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.footer-brand .brand-icon {
  color: #3b82f6;
}

.footer-brand .brand-text {
  color: white;
  font-size: 1.125rem;
  font-weight: 700;
}

.footer-info {
  text-align: right;
}

.version-text {
  color: #94a3b8;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.copyright {
  color: #64748b;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-title {
    font-size: 3rem;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }

  .nav-links {
    display: none;
  }

  .hero-section {
    padding: 2rem 0 4rem 0;
  }

  .hero-container {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-stats {
    justify-content: center;
  }

  .visual-card {
    padding: 1.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .features-container {
    padding: 0 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-info {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .visual-card {
    padding: 1rem;
  }

  .feature-card {
    padding: 1.5rem;
  }
}

/* Mode Selection Styles */
.mode-selection {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.mode-header {
  text-align: center;
  margin-bottom: 1rem;
}

.mode-header .section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.mode-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
}

.mode-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.mode-card {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mode-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.mode-card.selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.mode-icon {
  color: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.mode-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  text-align: center;
}

.mode-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  text-align: center;
  margin: 0;
  line-height: 1.4;
}

.mode-features {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mode-features li {
  font-size: 0.875rem;
  color: #4b5563;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mode-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.mode-badge-new {
  background: linear-gradient(135deg, #7c3aed, #5b21b6);
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.back-btn {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 0 0 auto;
}

.back-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.start-assessment-btn {
  flex: 1;
}

/* Responsive Mode Selection */
@media (max-width: 768px) {
  .mode-options {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .mode-card {
    padding: 1.25rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .back-btn {
    order: 2;
  }

  .start-assessment-btn {
    order: 1;
  }
}

@media (max-width: 480px) {
  .mode-card {
    padding: 1rem;
  }

  .mode-title {
    font-size: 1rem;
  }

  .mode-features li {
    font-size: 0.8rem;
  }
}
</style>
