// Handles all speech synthesis logic for the exam UI
export class SpeechService {
  public setLastSpokenText(text: string): void {
    this.lastSpokenText = text;
  }
  public getLastSpokenText(): string {
    return this.lastSpokenText;
  }
  private speechSynthesis: SpeechSynthesis | null = null;
  private speechEnabled: boolean = true;
  private speechInitialized: boolean = false;
  private lastSpokenText: string = '';
  private speechInProgress: boolean = false;

  constructor() {
    this.initializeSpeechSynthesis();
  }

  private initializeSpeechSynthesis(): void {
    if ('speechSynthesis' in window) {
      this.speechSynthesis = window.speechSynthesis;
      if (this.speechSynthesis.getVoices().length === 0) {
        this.speechSynthesis.addEventListener('voiceschanged', () => {
          this.speechSynthesis!.getVoices();
        });
      }
      this.speechInitialized = true;
    } else {
      this.speechEnabled = false;
    }
  }

  public speakInstruction(text: string): void {
    if (!this.speechEnabled || !this.speechSynthesis) return;
    if (text === this.lastSpokenText && this.speechInProgress) return;
    if (!this.speechInitialized) this.initializeSpeechSynthesis();
    if (this.speechInProgress && this.speechSynthesis.speaking) {
      this.speechSynthesis.cancel();
      this.speechInProgress = false;
    }
    this.lastSpokenText = text;
    setTimeout(() => {
      if (!this.speechSynthesis) return;
      this.performSpeech(text);
    }, this.speechInProgress ? 300 : 100);
  }

  private performSpeech(text: string): void {
    if (!this.speechSynthesis) return;
    this.speechInProgress = true;
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 1.0;
    utterance.volume = 1.0;
    utterance.pitch = 1.0;
    utterance.lang = 'en-US';
    utterance.onstart = () => { this.speechInProgress = true; };
    utterance.onend = () => { this.speechInProgress = false; };
    utterance.onerror = () => { this.speechInProgress = false; };
    this.speechSynthesis.speak(utterance);
  }

  public stopCurrentSpeech(): void {
    if (this.speechSynthesis && this.speechSynthesis.speaking) {
      this.speechSynthesis.cancel();
      this.speechInProgress = false;
    }
  }

  public resetSpeechState(): void {
    this.lastSpokenText = '';
    this.speechInProgress = false;
  }
}
