// Infrastructure: Exam Actions Configuration
import { ExamAction } from '../../domain/entities/ExamAction.js';

export class ExamActionsConfig {
  static getDefaultActions(): ExamAction[] {
    return [
      ExamAction.createFromConfig({
        action: 'Neutral / Resting Face',
        instruction: 'Please maintain a neutral expression and look directly at the camera.',
        duration: 5000
      }),
      ExamAction.createFromConfig({
        action: 'Raise Eyebrows',
        instruction: 'Raise your eyebrows as high as possible.',
        duration: 5000,
        targetMetric: 'forehead'
      }),
      ExamAction.createFromConfig({
        action: 'Close Eyes Tightly',
        instruction: 'Close your eyes as tightly as possible.',
        duration: 5000,
        targetMetric: 'eye_gap'
      }),
      ExamAction.createFromConfig({
        action: 'Smile Wide',
        instruction: 'Smile as wide as possible, showing your teeth.',
        duration: 5000,
        targetMetric: 'smile'
      })
    ];
  }
}
