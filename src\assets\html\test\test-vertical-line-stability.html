<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vertical Line Stability Test - 30 Second Expression Analysis</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .video-container {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        .video-section {
            flex: 1;
            text-align: center;
        }
        video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        canvas {
            width: 100%;
            max-width: 400px;
            height: 300px;
            border: 2px solid #007bff;
            border-radius: 8px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: bold;
        }
        .status.waiting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.recording {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.analyzing {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #99d6ff;
        }
        .timer {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
        }
        .metric-label {
            font-weight: bold;
        }
        .metric-value {
            color: #007bff;
        }
        .chart-container {
            margin: 20px 0;
            height: 200px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            position: relative;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Vertical Line Stability Test</h1>
            <p>30-Second Facial Expression Analysis</p>
        </div>

        <div class="instructions">
            <h3>📋 Test Instructions for Smile Analysis:</h3>
            <ol>
                <li><strong>Position yourself:</strong> Sit comfortably in front of the camera</li>
                <li><strong>Start recording:</strong> Click "Start 30-Second Test"</li>
                <li><strong>Expression sequence (try different smile types):</strong>
                    <ul style="margin-top: 5px;">
                        <li>🙂 <strong>Neutral</strong> - Relaxed, no expression (5 seconds)</li>
                        <li>😊 <strong>Bilateral smile</strong> - Both corners up (5 seconds)</li>
                        <li>😏 <strong>Left-sided smile</strong> - Only left corner up (5 seconds)</li>
                        <li>😏 <strong>Right-sided smile</strong> - Only right corner up (5 seconds)</li>
                        <li>🙂 <strong>Return to neutral</strong> - Repeat sequence</li>
                    </ul>
                </li>
                <li><strong>Stay still:</strong> Keep your head as stable as possible</li>
                <li><strong>Watch the visualization:</strong> Green = left corner, Blue = right corner, Yellow = mouth center</li>
            </ol>
        </div>

        <div class="video-container">
            <div class="video-section">
                <h3>📹 Live Camera</h3>
                <video id="videoElement" autoplay muted></video>
            </div>
            <div class="video-section">
                <h3>🔍 Landmark Analysis</h3>
                <canvas id="analysisCanvas"></canvas>
            </div>
        </div>

        <div class="controls">
            <button id="startBtn" class="btn">Start 30-Second Test</button>
            <button id="stopBtn" class="btn" disabled>Stop Test</button>
            <button id="resetBtn" class="btn" disabled>Reset</button>
        </div>

        <div id="statusDisplay" class="status waiting">
            Ready to start test. Click "Start 30-Second Test" to begin.
        </div>

        <div id="timerDisplay" class="timer">00:00</div>

        <div id="resultsSection" class="results" style="display: none;">
            <h3>📊 Vertical Line Stability Analysis Results</h3>
            
            <div class="metric">
                <span class="metric-label">Test Duration:</span>
                <span class="metric-value" id="testDuration">--</span>
            </div>
            
            <div class="metric">
                <span class="metric-label">Total Frames Analyzed:</span>
                <span class="metric-value" id="totalFrames">--</span>
            </div>
            
            <div class="metric">
                <span class="metric-label">Reference Line Stability:</span>
                <span class="metric-value" id="lineStability">--</span>
            </div>
            
            <div class="metric">
                <span class="metric-label">Maximum Line Deviation:</span>
                <span class="metric-value" id="maxDeviation">--</span>
            </div>
            
            <div class="metric">
                <span class="metric-label">Average Line Deviation:</span>
                <span class="metric-value" id="avgDeviation">--</span>
            </div>
            
            <div class="metric">
                <span class="metric-label">Smile Detection Events:</span>
                <span class="metric-value" id="smileEvents">--</span>
            </div>

            <div class="metric">
                <span class="metric-label">Bilateral Smiles:</span>
                <span class="metric-value" id="bilateralSmiles">--</span>
            </div>

            <div class="metric">
                <span class="metric-label">Unilateral Smiles:</span>
                <span class="metric-value" id="unilateralSmiles">--</span>
            </div>

            <div class="metric">
                <span class="metric-label">Average Smile Asymmetry:</span>
                <span class="metric-value" id="smileAsymmetry">--</span>
            </div>

            <div class="metric">
                <span class="metric-label">Line Stability During Smiles:</span>
                <span class="metric-value" id="smileStability">--</span>
            </div>

            <div class="metric">
                <span class="metric-label">Left Corner Max Movement:</span>
                <span class="metric-value" id="leftCornerMovement">--</span>
            </div>

            <div class="metric">
                <span class="metric-label">Right Corner Max Movement:</span>
                <span class="metric-value" id="rightCornerMovement">--</span>
            </div>

            <div class="metric">
                <span class="metric-label">Corner Movement Asymmetry:</span>
                <span class="metric-value" id="cornerAsymmetry">--</span>
            </div>

            <div class="chart-container" id="stabilityChart">
                <canvas id="chartCanvas" width="100%" height="200"></canvas>
            </div>

            <div id="detailedResults">
                <h4>📈 Detailed Analysis:</h4>
                <div id="frameByFrameData"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/control_utils/control_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils/drawing_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/face_mesh.js"></script>

    <script>
        class VerticalLineStabilityTest {
            constructor() {
                this.videoElement = document.getElementById('videoElement');
                this.canvas = document.getElementById('analysisCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.startBtn = document.getElementById('startBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.resetBtn = document.getElementById('resetBtn');
                this.statusDisplay = document.getElementById('statusDisplay');
                this.timerDisplay = document.getElementById('timerDisplay');
                this.resultsSection = document.getElementById('resultsSection');

                this.faceMesh = null;
                this.camera = null;
                this.isRecording = false;
                this.startTime = null;
                this.testDuration = 30000; // 30 seconds
                this.frameData = [];
                this.currentFrame = 0;

                this.initializeMediaPipe();
                this.setupEventListeners();
            }

            async initializeMediaPipe() {
                this.faceMesh = new FaceMesh({
                    locateFile: (file) => `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${file}`
                });

                this.faceMesh.setOptions({
                    maxNumFaces: 1,
                    refineLandmarks: true,
                    minDetectionConfidence: 0.5,
                    minTrackingConfidence: 0.5
                });

                this.faceMesh.onResults(this.onResults.bind(this));
            }

            setupEventListeners() {
                this.startBtn.addEventListener('click', () => this.startTest());
                this.stopBtn.addEventListener('click', () => this.stopTest());
                this.resetBtn.addEventListener('click', () => this.resetTest());
            }

            async startTest() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ 
                        video: { width: 640, height: 480 } 
                    });
                    this.videoElement.srcObject = stream;

                    this.camera = new Camera(this.videoElement, {
                        onFrame: async () => {
                            if (this.isRecording) {
                                await this.faceMesh.send({ image: this.videoElement });
                            }
                        },
                        width: 640,
                        height: 480
                    });

                    await this.camera.start();

                    this.isRecording = true;
                    this.startTime = Date.now();
                    this.frameData = [];
                    this.currentFrame = 0;

                    this.startBtn.disabled = true;
                    this.stopBtn.disabled = false;
                    this.resetBtn.disabled = true;

                    this.updateStatus('recording', 'Recording 30-second test... Make natural expressions!');
                    this.startTimer();

                    // Auto-stop after 30 seconds
                    setTimeout(() => {
                        if (this.isRecording) {
                            this.stopTest();
                        }
                    }, this.testDuration);

                } catch (error) {
                    console.error('Error starting test:', error);
                    this.updateStatus('waiting', 'Error accessing camera. Please check permissions.');
                }
            }

            stopTest() {
                this.isRecording = false;
                this.startBtn.disabled = false;
                this.stopBtn.disabled = true;
                this.resetBtn.disabled = false;

                if (this.camera) {
                    this.camera.stop();
                }

                this.updateStatus('analyzing', 'Analyzing vertical line stability...');
                setTimeout(() => this.analyzeResults(), 1000);
            }

            resetTest() {
                this.frameData = [];
                this.currentFrame = 0;
                this.resultsSection.style.display = 'none';
                this.updateStatus('waiting', 'Ready to start test. Click "Start 30-Second Test" to begin.');
                this.timerDisplay.textContent = '00:00';
                this.resetBtn.disabled = true;
            }

            startTimer() {
                const timer = setInterval(() => {
                    if (!this.isRecording) {
                        clearInterval(timer);
                        return;
                    }

                    const elapsed = Date.now() - this.startTime;
                    const remaining = Math.max(0, this.testDuration - elapsed);
                    const seconds = Math.floor(remaining / 1000);
                    const minutes = Math.floor(seconds / 60);
                    const displaySeconds = seconds % 60;

                    this.timerDisplay.textContent = 
                        `${minutes.toString().padStart(2, '0')}:${displaySeconds.toString().padStart(2, '0')}`;
                }, 100);
            }

            onResults(results) {
                if (!this.isRecording || !results.multiFaceLandmarks || results.multiFaceLandmarks.length === 0) {
                    return;
                }

                const landmarks = results.multiFaceLandmarks[0];
                this.analyzeFrame(landmarks);
                this.drawAnalysis(landmarks);
            }

            analyzeFrame(landmarks) {
                // Define key landmarks for vertical line analysis
                const leftEyeInner = landmarks[133];   // Left eye inner corner
                const rightEyeInner = landmarks[362];  // Right eye inner corner
                const noseTip = landmarks[1];          // Nose tip
                const noseBase = landmarks[2];         // Nose base
                const chinTip = landmarks[152];        // Chin tip
                const foreheadCenter = landmarks[9];   // Forehead center

                // Calculate eye midpoint (top of vertical line)
                const eyeMidpoint = {
                    x: (leftEyeInner.x + rightEyeInner.x) / 2,
                    y: (leftEyeInner.y + rightEyeInner.y) / 2
                };

                // Test different stable points for bottom of vertical line
                const candidatePoints = {
                    noseTip: noseTip,
                    noseBase: noseBase,
                    chinTip: chinTip,
                    foreheadCenter: foreheadCenter
                };

                // Enhanced smile detection - handles one-sided smiles
                const leftMouthCorner = landmarks[61];
                const rightMouthCorner = landmarks[291];
                const mouthCenter = landmarks[13];

                // Calculate individual corner elevations
                const leftElevation = mouthCenter.y - leftMouthCorner.y;
                const rightElevation = mouthCenter.y - rightMouthCorner.y;
                const avgElevation = (leftElevation + rightElevation) / 2;

                // Detect different types of smiles
                const SMILE_THRESHOLD = 0.01;
                const isLeftSmiling = leftElevation > SMILE_THRESHOLD;
                const isRightSmiling = rightElevation > SMILE_THRESHOLD;
                const isBilateralSmile = isLeftSmiling && isRightSmiling;
                const isUnilateralSmile = isLeftSmiling !== isRightSmiling;
                const isSmiling = isLeftSmiling || isRightSmiling; // Any smile activity

                // Calculate smile asymmetry
                const smileAsymmetry = Math.abs(leftElevation - rightElevation);

                // Track corner positions for movement analysis
                const leftCornerPos = { x: leftMouthCorner.x, y: leftMouthCorner.y };
                const rightCornerPos = { x: rightMouthCorner.x, y: rightMouthCorner.y };

                // Store enhanced frame data
                const frameData = {
                    timestamp: Date.now() - this.startTime,
                    frameNumber: this.currentFrame++,
                    eyeMidpoint: eyeMidpoint,
                    candidatePoints: candidatePoints,
                    isSmiling: isSmiling,
                    smileData: {
                        leftElevation: leftElevation,
                        rightElevation: rightElevation,
                        avgElevation: avgElevation,
                        isLeftSmiling: isLeftSmiling,
                        isRightSmiling: isRightSmiling,
                        isBilateralSmile: isBilateralSmile,
                        isUnilateralSmile: isUnilateralSmile,
                        smileAsymmetry: smileAsymmetry,
                        leftCornerPos: leftCornerPos,
                        rightCornerPos: rightCornerPos
                    },
                    verticalLines: {}
                };

                // Calculate vertical line for each candidate point with proper angle handling
                Object.keys(candidatePoints).forEach(pointName => {
                    const point = candidatePoints[pointName];

                    // Special handling for forehead center (above eye midpoint)
                    let angle;
                    if (pointName === 'foreheadCenter') {
                        // For forehead: calculate angle from vertical (0° = straight up)
                        // Use atan2 with swapped coordinates for proper vertical reference
                        angle = Math.atan2(point.x - eyeMidpoint.x, eyeMidpoint.y - point.y) * (180 / Math.PI);
                    } else {
                        // For points below eye midpoint: calculate angle from vertical (0° = straight down)
                        angle = Math.atan2(point.x - eyeMidpoint.x, point.y - eyeMidpoint.y) * (180 / Math.PI);
                    }

                    // Normalize angle to prevent 360° wrapping issues
                    while (angle > 180) angle -= 360;
                    while (angle < -180) angle += 360;

                    frameData.verticalLines[pointName] = {
                        topPoint: eyeMidpoint,
                        bottomPoint: point,
                        angle: angle,
                        length: Math.sqrt(Math.pow(point.x - eyeMidpoint.x, 2) + Math.pow(point.y - eyeMidpoint.y, 2))
                    };
                });

                this.frameData.push(frameData);
            }

            drawAnalysis(landmarks) {
                this.canvas.width = this.videoElement.videoWidth;
                this.canvas.height = this.videoElement.videoHeight;

                // Draw video frame
                this.ctx.drawImage(this.videoElement, 0, 0, this.canvas.width, this.canvas.height);

                // Draw all landmarks (smaller)
                this.ctx.fillStyle = '#FF0000';
                landmarks.forEach((landmark, index) => {
                    const x = landmark.x * this.canvas.width;
                    const y = landmark.y * this.canvas.height;
                    this.ctx.beginPath();
                    this.ctx.arc(x, y, 0.5, 0, 2 * Math.PI);
                    this.ctx.fill();
                });

                // Highlight key smile landmarks
                const leftMouthCorner = landmarks[61];
                const rightMouthCorner = landmarks[291];
                const mouthCenter = landmarks[13];

                // Draw mouth corners with larger markers
                this.ctx.fillStyle = '#00FF00';
                this.ctx.beginPath();
                this.ctx.arc(leftMouthCorner.x * this.canvas.width, leftMouthCorner.y * this.canvas.height, 4, 0, 2 * Math.PI);
                this.ctx.fill();

                this.ctx.fillStyle = '#0000FF';
                this.ctx.beginPath();
                this.ctx.arc(rightMouthCorner.x * this.canvas.width, rightMouthCorner.y * this.canvas.height, 4, 0, 2 * Math.PI);
                this.ctx.fill();

                // Draw mouth center
                this.ctx.fillStyle = '#FFFF00';
                this.ctx.beginPath();
                this.ctx.arc(mouthCenter.x * this.canvas.width, mouthCenter.y * this.canvas.height, 3, 0, 2 * Math.PI);
                this.ctx.fill();

                // Draw smile elevation indicators
                const leftElevation = mouthCenter.y - leftMouthCorner.y;
                const rightElevation = mouthCenter.y - rightMouthCorner.y;

                // Left corner elevation line
                if (leftElevation > 0.005) {
                    this.ctx.strokeStyle = '#00FF00';
                    this.ctx.lineWidth = 3;
                    this.ctx.beginPath();
                    this.ctx.moveTo(leftMouthCorner.x * this.canvas.width, leftMouthCorner.y * this.canvas.height);
                    this.ctx.lineTo(leftMouthCorner.x * this.canvas.width, mouthCenter.y * this.canvas.height);
                    this.ctx.stroke();
                }

                // Right corner elevation line
                if (rightElevation > 0.005) {
                    this.ctx.strokeStyle = '#0000FF';
                    this.ctx.lineWidth = 3;
                    this.ctx.beginPath();
                    this.ctx.moveTo(rightMouthCorner.x * this.canvas.width, rightMouthCorner.y * this.canvas.height);
                    this.ctx.lineTo(rightMouthCorner.x * this.canvas.width, mouthCenter.y * this.canvas.height);
                    this.ctx.stroke();
                }

                // Draw vertical reference lines
                const leftEyeInner = landmarks[133];
                const rightEyeInner = landmarks[362];
                const eyeMidpoint = {
                    x: (leftEyeInner.x + rightEyeInner.x) / 2 * this.canvas.width,
                    y: (leftEyeInner.y + rightEyeInner.y) / 2 * this.canvas.height
                };

                // Draw lines to different candidate points
                const candidates = [
                    { point: landmarks[1], color: '#00FF00', label: 'Nose Tip' },
                    { point: landmarks[2], color: '#0000FF', label: 'Nose Base' },
                    { point: landmarks[152], color: '#FF00FF', label: 'Chin Tip' },
                    { point: landmarks[9], color: '#FFFF00', label: 'Forehead' }
                ];

                candidates.forEach((candidate, index) => {
                    const bottomPoint = {
                        x: candidate.point.x * this.canvas.width,
                        y: candidate.point.y * this.canvas.height
                    };

                    this.ctx.strokeStyle = candidate.color;
                    this.ctx.lineWidth = 2;
                    this.ctx.beginPath();
                    this.ctx.moveTo(eyeMidpoint.x, eyeMidpoint.y);
                    this.ctx.lineTo(bottomPoint.x, bottomPoint.y);
                    this.ctx.stroke();

                    // Draw label
                    this.ctx.fillStyle = candidate.color;
                    this.ctx.font = '12px Arial';
                    this.ctx.fillText(candidate.label, bottomPoint.x + 5, bottomPoint.y);
                });

                // Draw eye midpoint
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.beginPath();
                this.ctx.arc(eyeMidpoint.x, eyeMidpoint.y, 4, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.strokeStyle = '#000000';
                this.ctx.lineWidth = 1;
                this.ctx.stroke();
            }

            analyzeResults() {
                if (this.frameData.length === 0) {
                    this.updateStatus('waiting', 'No data collected. Please try again.');
                    return;
                }

                const analysis = this.calculateStabilityMetrics();
                this.displayResults(analysis);
                this.updateStatus('waiting', 'Analysis complete! Results displayed below.');
            }

            calculateStabilityMetrics() {
                const pointNames = ['noseTip', 'noseBase', 'chinTip', 'foreheadCenter'];
                const stability = {};

                pointNames.forEach(pointName => {
                    const angles = this.frameData.map(frame => frame.verticalLines[pointName].angle);
                    const lengths = this.frameData.map(frame => frame.verticalLines[pointName].length);

                    // Calculate stability metrics
                    const angleStd = this.calculateStandardDeviation(angles);
                    const lengthStd = this.calculateStandardDeviation(lengths);
                    const maxAngleDeviation = Math.max(...angles) - Math.min(...angles);
                    const avgAngle = angles.reduce((a, b) => a + b, 0) / angles.length;

                    // Analyze stability during smiles vs neutral
                    const smileFrames = this.frameData.filter(frame => frame.isSmiling);
                    const neutralFrames = this.frameData.filter(frame => !frame.isSmiling);

                    const smileAngles = smileFrames.map(frame => frame.verticalLines[pointName].angle);
                    const neutralAngles = neutralFrames.map(frame => frame.verticalLines[pointName].angle);

                    const smileAngleStd = smileAngles.length > 0 ? this.calculateStandardDeviation(smileAngles) : 0;
                    const neutralAngleStd = neutralAngles.length > 0 ? this.calculateStandardDeviation(neutralAngles) : 0;

                    stability[pointName] = {
                        angleStd: angleStd,
                        lengthStd: lengthStd,
                        maxAngleDeviation: maxAngleDeviation,
                        avgAngle: avgAngle,
                        smileAngleStd: smileAngleStd,
                        neutralAngleStd: neutralAngleStd,
                        stabilityScore: 100 - (angleStd * 10) // Simple stability score
                    };
                });

                // Enhanced smile analysis
                const smileFrames = this.frameData.filter(frame => frame.isSmiling);
                const bilateralSmiles = this.frameData.filter(frame => frame.smileData.isBilateralSmile);
                const unilateralSmiles = this.frameData.filter(frame => frame.smileData.isUnilateralSmile);
                const smileAsymmetries = smileFrames.map(frame => frame.smileData.smileAsymmetry);
                const avgSmileAsymmetry = smileAsymmetries.length > 0 ?
                    smileAsymmetries.reduce((a, b) => a + b, 0) / smileAsymmetries.length : 0;

                // Corner movement analysis
                const leftCornerMovement = this.calculateCornerMovement('left');
                const rightCornerMovement = this.calculateCornerMovement('right');
                const cornerAsymmetry = Math.abs(leftCornerMovement.maxMovement - rightCornerMovement.maxMovement);

                // Find most stable point
                const mostStablePoint = pointNames.reduce((best, current) =>
                    stability[current].stabilityScore > stability[best].stabilityScore ? current : best
                );

                return {
                    totalFrames: this.frameData.length,
                    testDuration: this.frameData[this.frameData.length - 1].timestamp,
                    smileEvents: smileFrames.length,
                    bilateralSmiles: bilateralSmiles.length,
                    unilateralSmiles: unilateralSmiles.length,
                    avgSmileAsymmetry: avgSmileAsymmetry,
                    leftCornerMovement: leftCornerMovement,
                    rightCornerMovement: rightCornerMovement,
                    cornerAsymmetry: cornerAsymmetry,
                    stability: stability,
                    mostStablePoint: mostStablePoint,
                    recommendation: this.generateRecommendation(stability, mostStablePoint)
                };
            }

            calculateStandardDeviation(values) {
                const avg = values.reduce((a, b) => a + b, 0) / values.length;
                const squareDiffs = values.map(value => Math.pow(value - avg, 2));
                const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;
                return Math.sqrt(avgSquareDiff);
            }

            calculateCornerMovement(side) {
                if (this.frameData.length === 0) return { maxMovement: 0, avgMovement: 0, movements: [] };

                const cornerKey = side === 'left' ? 'leftCornerPos' : 'rightCornerPos';
                const positions = this.frameData.map(frame => frame.smileData[cornerKey]);

                // Calculate baseline position (average of first 10 frames or neutral frames)
                const neutralFrames = this.frameData.filter(frame => !frame.isSmiling);
                const baselineFrames = neutralFrames.length > 5 ? neutralFrames.slice(0, 10) : this.frameData.slice(0, 10);
                const baselinePos = {
                    x: baselineFrames.reduce((sum, frame) => sum + frame.smileData[cornerKey].x, 0) / baselineFrames.length,
                    y: baselineFrames.reduce((sum, frame) => sum + frame.smileData[cornerKey].y, 0) / baselineFrames.length
                };

                // Calculate movement distances from baseline
                const movements = positions.map(pos => {
                    const dx = pos.x - baselinePos.x;
                    const dy = pos.y - baselinePos.y;
                    return Math.sqrt(dx * dx + dy * dy);
                });

                const maxMovement = Math.max(...movements);
                const avgMovement = movements.reduce((a, b) => a + b, 0) / movements.length;

                return {
                    maxMovement: maxMovement,
                    avgMovement: avgMovement,
                    movements: movements,
                    baselinePos: baselinePos
                };
            }

            generateRecommendation(stability, mostStablePoint) {
                const bestStability = stability[mostStablePoint];
                let recommendation = `The most stable vertical reference line is from eye midpoint to ${mostStablePoint.replace(/([A-Z])/g, ' $1').toLowerCase()}.`;
                
                if (bestStability.stabilityScore > 90) {
                    recommendation += " This reference line shows excellent stability during facial expressions.";
                } else if (bestStability.stabilityScore > 80) {
                    recommendation += " This reference line shows good stability with minor variations.";
                } else if (bestStability.stabilityScore > 70) {
                    recommendation += " This reference line shows moderate stability with some movement during expressions.";
                } else {
                    recommendation += " This reference line shows significant movement during expressions. Consider head stabilization.";
                }

                return recommendation;
            }

            displayResults(analysis) {
                document.getElementById('testDuration').textContent = `${(analysis.testDuration / 1000).toFixed(1)} seconds`;
                document.getElementById('totalFrames').textContent = analysis.totalFrames;
                document.getElementById('smileEvents').textContent = `${analysis.smileEvents} frames (${(analysis.smileEvents / analysis.totalFrames * 100).toFixed(1)}%)`;
                document.getElementById('bilateralSmiles').textContent = `${analysis.bilateralSmiles} frames (${(analysis.bilateralSmiles / analysis.totalFrames * 100).toFixed(1)}%)`;
                document.getElementById('unilateralSmiles').textContent = `${analysis.unilateralSmiles} frames (${(analysis.unilateralSmiles / analysis.totalFrames * 100).toFixed(1)}%)`;
                document.getElementById('smileAsymmetry').textContent = `${(analysis.avgSmileAsymmetry * 100).toFixed(2)}%`;

                // Corner movement data
                document.getElementById('leftCornerMovement').textContent = `${(analysis.leftCornerMovement.maxMovement * 100).toFixed(2)}mm max`;
                document.getElementById('rightCornerMovement').textContent = `${(analysis.rightCornerMovement.maxMovement * 100).toFixed(2)}mm max`;
                document.getElementById('cornerAsymmetry').textContent = `${(analysis.cornerAsymmetry * 100).toFixed(2)}mm difference`;

                const bestStability = analysis.stability[analysis.mostStablePoint];
                document.getElementById('lineStability').textContent = `${bestStability.stabilityScore.toFixed(1)}% (${analysis.mostStablePoint})`;
                document.getElementById('maxDeviation').textContent = `${bestStability.maxAngleDeviation.toFixed(2)}°`;
                document.getElementById('avgDeviation').textContent = `${bestStability.angleStd.toFixed(3)}°`;
                document.getElementById('smileStability').textContent = `${bestStability.smileAngleStd.toFixed(3)}° std during smiles`;

                // Display detailed results
                let detailedHtml = `<p><strong>Recommendation:</strong> ${analysis.recommendation}</p>`;

                // Smile Movement Analysis
                detailedHtml += '<h5>🎯 Smile Corner Movement Analysis:</h5>';
                detailedHtml += `
                    <div style="margin: 10px 0; padding: 15px; background: #e7f3ff; border-radius: 4px; border-left: 4px solid #007bff;">
                        <strong>Left Corner Movement:</strong><br>
                        Max Movement: ${(analysis.leftCornerMovement.maxMovement * 100).toFixed(2)}mm<br>
                        Avg Movement: ${(analysis.leftCornerMovement.avgMovement * 100).toFixed(2)}mm<br><br>
                        <strong>Right Corner Movement:</strong><br>
                        Max Movement: ${(analysis.rightCornerMovement.maxMovement * 100).toFixed(2)}mm<br>
                        Avg Movement: ${(analysis.rightCornerMovement.avgMovement * 100).toFixed(2)}mm<br><br>
                        <strong>Movement Asymmetry:</strong> ${(analysis.cornerAsymmetry * 100).toFixed(2)}mm<br>
                        <strong>Asymmetry Assessment:</strong> ${this.assessMovementAsymmetry(analysis.cornerAsymmetry)}
                    </div>
                `;

                // Smile Type Distribution
                const totalFrames = analysis.totalFrames;
                const neutralFrames = totalFrames - analysis.smileEvents;
                detailedHtml += '<h5>😊 Smile Type Distribution:</h5>';
                detailedHtml += `
                    <div style="margin: 10px 0; padding: 15px; background: #f0f8f0; border-radius: 4px; border-left: 4px solid #28a745;">
                        <strong>Neutral Expression:</strong> ${neutralFrames} frames (${(neutralFrames / totalFrames * 100).toFixed(1)}%)<br>
                        <strong>Bilateral Smiles:</strong> ${analysis.bilateralSmiles} frames (${(analysis.bilateralSmiles / totalFrames * 100).toFixed(1)}%)<br>
                        <strong>Unilateral Smiles:</strong> ${analysis.unilateralSmiles} frames (${(analysis.unilateralSmiles / totalFrames * 100).toFixed(1)}%)<br>
                        <strong>Total Smile Activity:</strong> ${analysis.smileEvents} frames (${(analysis.smileEvents / totalFrames * 100).toFixed(1)}%)<br>
                        <strong>Smile Symmetry:</strong> ${this.assessSmileSymmetry(analysis.bilateralSmiles, analysis.unilateralSmiles)}
                    </div>
                `;

                detailedHtml += '<h5>📏 Stability Comparison by Reference Point:</h5>';

                Object.keys(analysis.stability).forEach(pointName => {
                    const data = analysis.stability[pointName];
                    const statusColor = data.stabilityScore > 85 ? '#28a745' : data.stabilityScore > 70 ? '#ffc107' : '#dc3545';
                    detailedHtml += `
                        <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 4px; border-left: 4px solid ${statusColor};">
                            <strong>${pointName.replace(/([A-Z])/g, ' $1')}:</strong><br>
                            Stability Score: ${data.stabilityScore.toFixed(1)}%<br>
                            Angle Std Dev: ${data.angleStd.toFixed(3)}°<br>
                            Max Deviation: ${data.maxAngleDeviation.toFixed(2)}°<br>
                            Smile Std Dev: ${data.smileAngleStd.toFixed(3)}°<br>
                            Neutral Std Dev: ${data.neutralAngleStd.toFixed(3)}°
                        </div>
                    `;
                });

                document.getElementById('detailedResults').innerHTML = detailedHtml;
                this.resultsSection.style.display = 'block';
            }

            assessMovementAsymmetry(asymmetry) {
                const asymmetryMm = asymmetry * 100;
                if (asymmetryMm < 1.0) return "Excellent symmetry";
                if (asymmetryMm < 2.0) return "Good symmetry";
                if (asymmetryMm < 3.0) return "Mild asymmetry";
                if (asymmetryMm < 5.0) return "Moderate asymmetry";
                return "Significant asymmetry";
            }

            assessSmileSymmetry(bilateral, unilateral) {
                const total = bilateral + unilateral;
                if (total === 0) return "No smile activity detected";
                const symmetryRatio = bilateral / total;
                if (symmetryRatio > 0.8) return "Predominantly symmetric";
                if (symmetryRatio > 0.6) return "Mostly symmetric";
                if (symmetryRatio > 0.4) return "Mixed symmetric/asymmetric";
                if (symmetryRatio > 0.2) return "Predominantly asymmetric";
                return "Highly asymmetric";
            }

            updateStatus(type, message) {
                this.statusDisplay.className = `status ${type}`;
                this.statusDisplay.textContent = message;
            }
        }

        // Initialize the test when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new VerticalLineStabilityTest();
        });
    </script>
</body>
</html>
