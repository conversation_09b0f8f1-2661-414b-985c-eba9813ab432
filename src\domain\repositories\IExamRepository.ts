// Domain Repository Interface
import { ExamSession } from '../entities/ExamSession.js';
import { MetricResult } from '../value-objects/MetricResult.js';

export interface IExamRepository {
  saveSession(session: ExamSession): Promise<void>;
  getSession(patientId: string): Promise<ExamSession | null>;
  saveResults(results: MetricResult[]): Promise<void>;
  exportResults(format: 'csv' | 'markdown'): Promise<string>;
}
