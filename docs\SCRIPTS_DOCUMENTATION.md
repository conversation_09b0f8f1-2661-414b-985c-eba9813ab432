# NPM Scripts Documentation

This document describes all available NPM scripts for the Facial Symmetry Analysis application.

## 🚀 Quick Start Scripts

### `npm run serve`
**Build and run the production server in one command**
```bash
npm run serve
```
- ✅ Compiles TypeScript to JavaScript
- ✅ Starts production server using compiled files
- ✅ Best for production deployment

### `npm run build-and-run`
**Alternative to serve (same functionality)**
```bash
npm run build-and-run
```
- ✅ Identical to `npm run serve`
- ✅ Builds and runs production server

## 🔧 Development Scripts

### `npm start`
**Start development server with TypeScript**
```bash
npm start
```
- ✅ Uses `ts-node` for direct TypeScript execution
- ✅ No build step required
- ✅ Best for development and debugging
- ✅ Automatic TypeScript compilation on-the-fly

### `npm run dev`
**Alternative development server**
```bash
npm run dev
```
- ✅ Identical to `npm start`
- ✅ Uses `ts-node` for development

## 🏗️ Build Scripts

### `npm run build`
**Build both client and server**
```bash
npm run build
```
- ✅ Compiles client TypeScript (`src/` → `dist/`)
- ✅ Compiles server TypeScript (`server.ts` → `dist-server/`)
- ✅ Creates production-ready JavaScript files

### `npm run build:client`
**Build only client-side code**
```bash
npm run build:client
```
- ✅ Compiles `src/` directory to `dist/`
- ✅ Uses main `tsconfig.json`

### `npm run build:server`
**Build only server code**
```bash
npm run build:server
```
- ✅ Compiles `server.ts` to `dist-server/`
- ✅ Uses `tsconfig.server.json`

## 🏃‍♂️ Production Scripts

### `npm run start:prod`
**Start production server (compiled)**
```bash
npm run start:prod
```
- ✅ Runs compiled JavaScript from `dist-server/`
- ✅ Faster startup (no TypeScript compilation)
- ✅ Production-optimized

## 🧹 Cleanup Scripts

### `npm run clean`
**Remove build directories (cross-platform)**
```bash
npm run clean
```
- ✅ Removes `dist/` and `dist-server/` directories
- ✅ Uses `rimraf` for cross-platform compatibility

### `npm run clean:win`
**Remove build directories (Windows-specific)**
```bash
npm run clean:win
```
- ✅ Windows-specific cleanup using `rmdir`
- ✅ Fallback if `rimraf` has issues

## 🔄 Rebuild Scripts

### `npm run rebuild`
**Clean and build**
```bash
npm run rebuild
```
- ✅ Runs `npm run clean`
- ✅ Runs `npm run build`
- ✅ Fresh build from scratch

### `npm run rebuild-and-run`
**Clean, build, and run production server**
```bash
npm run rebuild-and-run
```
- ✅ Runs `npm run rebuild`
- ✅ Runs `npm run start:prod`
- ✅ Complete fresh deployment

## 📋 Script Comparison

| Script | Build | Server | Use Case |
|--------|-------|--------|----------|
| `npm start` | No | Development | Development & debugging |
| `npm run serve` | Yes | Production | Production deployment |
| `npm run build-and-run` | Yes | Production | Production deployment |
| `npm run start:prod` | No* | Production | Run pre-built production |
| `npm run rebuild-and-run` | Yes (clean) | Production | Fresh production build |

*Requires prior build

## 🎯 Recommended Workflows

### **Development Workflow**
```bash
# Start development server
npm start

# Make changes to TypeScript files
# Server automatically recompiles and serves
```

### **Production Deployment**
```bash
# Option 1: Build and run in one command
npm run serve

# Option 2: Separate build and run
npm run build
npm run start:prod
```

### **Fresh Start (Clean Build)**
```bash
# Clean everything and rebuild
npm run rebuild-and-run
```

### **Troubleshooting Build Issues**
```bash
# Clean build directories
npm run clean

# Rebuild everything
npm run build

# Test production server
npm run start:prod
```

## 🔍 Script Details

### File Locations
- **Source TypeScript**: `src/`, `main.ts`, `server.ts`, `types.ts`
- **Client Build Output**: `dist/`
- **Server Build Output**: `dist-server/`
- **Static Files**: `index.html`, `package.json` (served from root)

### TypeScript Configurations
- **Client**: Uses `tsconfig.json`
- **Server**: Uses `tsconfig.server.json`

### Server Behavior
- **Development**: `ts-node server.ts` (serves from root directory)
- **Production**: `node dist-server/server.js` (serves from root directory)
- **Port**: 3000 (configurable via `PORT` environment variable)

## 🚨 Troubleshooting

### Common Issues

#### "Cannot find module" errors
```bash
# Clean and rebuild
npm run rebuild
```

#### Port already in use
```bash
# Kill existing processes or change port
PORT=3001 npm start
```

#### Build failures
```bash
# Check TypeScript errors
npm run build:client
npm run build:server
```

#### Missing dependencies
```bash
# Reinstall dependencies
npm install
```

### Environment Requirements
- **Node.js**: 18+ (some packages prefer 20+)
- **NPM**: 9+
- **TypeScript**: 5.8+

## 📝 Notes

- All scripts work on Windows, macOS, and Linux
- Development server uses `ts-node` for hot compilation
- Production server uses pre-compiled JavaScript for better performance
- Static files (HTML, CSS, images) are served from the root directory
- MediaPipe libraries are loaded from CDN in the browser
