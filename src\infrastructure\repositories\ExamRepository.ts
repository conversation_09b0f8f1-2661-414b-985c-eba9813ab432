// Infrastructure: Exam Repository Implementation
import { IExamRepository } from '../../domain/repositories/IExamRepository.js';
import { ExamSession } from '../../domain/entities/ExamSession.js';
import { MetricResult } from '../../domain/value-objects/MetricResult.js';

export class ExamRepository implements IExamRepository {
  private sessions: Map<string, ExamSession> = new Map();
  private results: MetricResult[] = [];

  async saveSession(session: ExamSession): Promise<void> {
    this.sessions.set(session.patient.id, session);
  }

  async getSession(patientId: string): Promise<ExamSession | null> {
    return this.sessions.get(patientId) || null;
  }

  async saveResults(results: MetricResult[]): Promise<void> {
    this.results.push(...results);
  }

  async exportResults(format: 'csv' | 'markdown'): Promise<string> {
    if (format === 'csv') {
      return this.exportAsCSV();
    } else {
      return this.exportAsMarkdown();
    }
  }

  private exportAsCSV(): string {
    const headers = ['Action', 'Metric', 'Value', 'Score', 'Label', 'Timestamp'];
    const rows = this.results.map(result => [
      result.actionName,
      result.metricName,
      result.value.toFixed(4),
      result.score.toString(),
      result.label,
      result.timestamp.toISOString()
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  private exportAsMarkdown(): string {
    let markdown = '# Facial Symmetry Analysis Results\n\n';
    markdown += '| Action | Metric | Value | Score | Label | Timestamp |\n';
    markdown += '|--------|--------|-------|-------|-------|----------|\n';

    for (const result of this.results) {
      markdown += `| ${result.actionName} | ${result.metricName} | ${result.value.toFixed(4)} | ${result.score} | ${result.label} | ${result.timestamp.toISOString()} |\n`;
    }

    markdown += '\n**Score Legend:** 0=Normal, 1=Mild, 2=Moderate, 3=Moderately Severe, 4=Severe\n';
    
    return markdown;
  }
}
