// Type definitions for the facial symmetry application

export interface PatientInfo {
  id: string;
  name: string;
  age: string;
  date: string;
}

export interface ExamAction {
  action: string;
  instruction: string;
}

export interface Landmark {
  x: number;
  y: number;
  z?: number;
}

export interface DetectionResult {
  glassesDetected: boolean;
  maskDetected: boolean;
}

export interface MetricValue {
  value: number;
  score: number;
  label: string;
}

export interface Metrics {
  forehead: number;
  eye_gap: number;
  smile: number;
  lip: number;
  nose: number;
}

export interface ScoreResult {
  score: number | null;
  label: string;
}

export interface FaceMeshResults {
  multiFaceLandmarks?: Landmark[][];
}

export interface SessionLogEntry {
  id: string;
  name: string;
  age: string;
  date: string;
  action: string;
  [key: string]: any; // For dynamic metric properties
}

// Movement Detection Types
export interface MovementDetectionResult {
  isDetected: boolean;
  confidence: number;
  landmarkCount: number;
  movementIntensity: number;
  validationErrors: string[];
}

export interface MovementValidationCriteria {
  minLandmarkCount: number;
  minConfidence: number;
  minMovementIntensity: number;
  requiredLandmarks: number[];
}

export type ExamActionType = 'baseline' | 'eyebrowRaise' | 'eyeClose' | 'smile';

// MediaPipe Camera types
export interface CameraConfig {
  onFrame: () => Promise<void>;
  width: number;
  height: number;
}

export interface FaceMeshConfig {
  maxNumFaces: number;
  refineLandmarks: boolean;
  minDetectionConfidence: number;
  minTrackingConfidence: number;
}

// Global MediaPipe types (these would normally come from @mediapipe/face_mesh types)
declare global {
  class FaceMesh {
    constructor(config: { locateFile: (file: string) => string });
    setOptions(options: FaceMeshConfig): void;
    onResults(callback: (results: FaceMeshResults) => void): void;
    send(data: { image: HTMLVideoElement }): Promise<void>;
  }

  class Camera {
    constructor(video: HTMLVideoElement, config: CameraConfig);
    start(): void;
  }
}
