export interface EyebrowMetrics {
  leftEyebrowElevation: number;
  rightEyebrowElevation: number;
}

export interface EyeMetrics {
  leftEyeClosure: number;
  rightEyeClosure: number;
}

export interface MouthMetrics {
  leftMouthMovement: number;
  rightMouthMovement: number;
  
  // Detailed mouth distance measurements
  leftHorizontalDistance: number;
  rightHorizontalDistance: number;
  leftVerticalDistance: number;
  rightVerticalDistance: number;

}

export interface FacialMetrics {
  // Organized metric objects
  eyebrowMetrics: EyebrowMetrics;
  eyeMetrics: EyeMetrics;
  mouthMetrics: MouthMetrics;
}
