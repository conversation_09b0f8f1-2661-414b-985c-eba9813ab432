// Domain Repository Interface for Camera
import { Landmark } from '../../shared/types/index.js';

export interface FaceMeshResults {
  multiFaceLandmarks?: Landmark[][];
}

export interface ICameraRepository {
  startCamera(videoElement: HTMLVideoElement): Promise<void>;
  stopCamera(): void;
  onResults(callback: (results: FaceMeshResults) => void): void;

  // Camera switching functionality
  enumerateCameras(): Promise<MediaDeviceInfo[]>;
  getAvailableCameras(): MediaDeviceInfo[];
  hasMultipleCameras(): boolean;
  getCurrentFacingMode(): 'user' | 'environment';
  switchCamera(): Promise<void>;
}
