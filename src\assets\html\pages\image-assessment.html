<div id="imageAssessmentView" class="image-assessment-container">
  <!-- Navigation Header -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <div class="brand-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z" fill="currentColor"/>
          </svg>
        </div>
        <span class="brand-text">Image-Based Assessment</span>
      </div>
      <div class="nav-actions">
        <button class="nav-btn" onclick="goBackToHome()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" fill="currentColor"/>
          </svg>
          Back to Home
        </button>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="main-content">
    <div class="content-container">
      <!-- Progress Header -->
      <div class="progress-header">
        <h1 class="page-title">📸 Image-Based Facial Symmetry Assessment</h1>
        <p class="page-description">Upload high-quality images for each facial expression to perform detailed analysis</p>
        
        <div class="progress-bar">
          <div class="progress-step completed" data-step="1">
            <div class="step-number">1</div>
            <span class="step-label">Patient Info</span>
          </div>
          <div class="progress-step active" data-step="2">
            <div class="step-number">2</div>
            <span class="step-label">Upload Images</span>
          </div>
          <div class="progress-step" data-step="3">
            <div class="step-number">3</div>
            <span class="step-label">Analysis</span>
          </div>
          <div class="progress-step" data-step="4">
            <div class="step-number">4</div>
            <span class="step-label">Results</span>
          </div>
        </div>
      </div>

      <!-- Patient Info Summary -->
      <div class="patient-summary">
        <h3>Patient Information</h3>
        <div class="patient-details">
          <span id="patientName">Loading...</span> •
          <span id="patientAge">Loading...</span> years •
          ID: <span id="patientId">Loading...</span>
        </div>
      </div>

      <!-- Image Upload Section -->
      <div class="upload-section">
        <h2 class="section-title">Required Images</h2>
        <p class="section-description">Please upload clear, high-resolution images for each facial expression. Ensure good lighting and face the camera directly.</p>
        
        <div class="upload-grid">
          <!-- Neutral Face -->
          <div class="upload-card" data-action="baseline">
            <div class="upload-header">
              <div class="action-icon">😐</div>
              <h3 class="action-title">Neutral Face</h3>
              <p class="action-description">Relaxed, natural expression</p>
            </div>
            
            <div class="upload-area" id="uploadArea_baseline">
              <input type="file" id="fileInput_baseline" accept="image/*" onchange="handleImageUpload('baseline', this)">
              <div class="upload-placeholder">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                </svg>
                <span class="upload-text">Click to upload or drag image here</span>
                <span class="upload-hint">JPG, PNG, WebP (max 10MB)</span>
              </div>
              <div class="image-preview" id="preview_baseline" style="display: none;">
                <img id="previewImg_baseline" src="" alt="Preview">
                <div class="preview-overlay">
                  <button class="retake-btn" onclick="retakeImage('baseline')">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" fill="currentColor"/>
                    </svg>
                    Retake
                  </button>
                </div>
              </div>
            </div>
            
            <div class="upload-status" id="status_baseline">
              <div class="status-indicator pending">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6Z" fill="currentColor"/>
                </svg>
                <span>Waiting for upload</span>
              </div>
            </div>
          </div>

          <!-- Eyebrow Elevation -->
          <div class="upload-card" data-action="eyebrow_raise">
            <div class="upload-header">
              <div class="action-icon">🤨</div>
              <h3 class="action-title">Eyebrow Elevation</h3>
              <p class="action-description">Raise eyebrows as high as possible</p>
            </div>
            
            <div class="upload-area" id="uploadArea_eyebrow_raise">
              <input type="file" id="fileInput_eyebrow_raise" accept="image/*" onchange="handleImageUpload('eyebrow_raise', this)">
              <div class="upload-placeholder">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                </svg>
                <span class="upload-text">Click to upload or drag image here</span>
                <span class="upload-hint">JPG, PNG, WebP (max 10MB)</span>
              </div>
              <div class="image-preview" id="preview_eyebrow_raise" style="display: none;">
                <img id="previewImg_eyebrow_raise" src="" alt="Preview">
                <div class="preview-overlay">
                  <button class="retake-btn" onclick="retakeImage('eyebrow_raise')">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" fill="currentColor"/>
                    </svg>
                    Retake
                  </button>
                </div>
              </div>
            </div>
            
            <div class="upload-status" id="status_eyebrow_raise">
              <div class="status-indicator pending">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6Z" fill="currentColor"/>
                </svg>
                <span>Waiting for upload</span>
              </div>
            </div>
          </div>

          <!-- Eye Closure -->
          <div class="upload-card" data-action="eye_close">
            <div class="upload-header">
              <div class="action-icon">😑</div>
              <h3 class="action-title">Eye Closure</h3>
              <p class="action-description">Close eyes as tightly as possible</p>
            </div>
            
            <div class="upload-area" id="uploadArea_eye_close">
              <input type="file" id="fileInput_eye_close" accept="image/*" onchange="handleImageUpload('eye_close', this)">
              <div class="upload-placeholder">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                </svg>
                <span class="upload-text">Click to upload or drag image here</span>
                <span class="upload-hint">JPG, PNG, WebP (max 10MB)</span>
              </div>
              <div class="image-preview" id="preview_eye_close" style="display: none;">
                <img id="previewImg_eye_close" src="" alt="Preview">
                <div class="preview-overlay">
                  <button class="retake-btn" onclick="retakeImage('eye_close')">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" fill="currentColor"/>
                    </svg>
                    Retake
                  </button>
                </div>
              </div>
            </div>
            
            <div class="upload-status" id="status_eye_close">
              <div class="status-indicator pending">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6Z" fill="currentColor"/>
                </svg>
                <span>Waiting for upload</span>
              </div>
            </div>
          </div>

          <!-- Smile -->
          <div class="upload-card" data-action="smile">
            <div class="upload-header">
              <div class="action-icon">😊</div>
              <h3 class="action-title">Smile</h3>
              <p class="action-description">Show your biggest smile</p>
            </div>
            
            <div class="upload-area" id="uploadArea_smile">
              <input type="file" id="fileInput_smile" accept="image/*" onchange="handleImageUpload('smile', this)">
              <div class="upload-placeholder">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                </svg>
                <span class="upload-text">Click to upload or drag image here</span>
                <span class="upload-hint">JPG, PNG, WebP (max 10MB)</span>
              </div>
              <div class="image-preview" id="preview_smile" style="display: none;">
                <img id="previewImg_smile" src="" alt="Preview">
                <div class="preview-overlay">
                  <button class="retake-btn" onclick="retakeImage('smile')">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" fill="currentColor"/>
                    </svg>
                    Retake
                  </button>
                </div>
              </div>
            </div>
            
            <div class="upload-status" id="status_smile">
              <div class="status-indicator pending">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6Z" fill="currentColor"/>
                </svg>
                <span>Waiting for upload</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-section">
        <div class="upload-summary">
          <div class="summary-item">
            <span class="summary-label">Images uploaded:</span>
            <span class="summary-value" id="uploadCount">0 / 4</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Total size:</span>
            <span class="summary-value" id="totalSize">0 MB</span>
          </div>
        </div>
        
        <div class="action-buttons">
          <button class="secondary-btn" onclick="goBackToHome()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" fill="currentColor"/>
            </svg>
            Back to Home
          </button>
          
          <button class="primary-btn" id="analyzeBtn" disabled onclick="startImageAnalysis()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9,2V7.38L10.5,8.88L12,7.38V2H9M20,2H17V9L15,7L13,9V2H4A2,2 0 0,0 2,4V20A2,2 0 0,0 4,22H20A2,2 0 0,0 22,20V4A2,2 0 0,0 20,2M8.5,12.5L11,16.5L14.5,11L19,18H5L8.5,12.5Z" fill="currentColor"/>
            </svg>
            <span id="analyzeBtnText">Analyze Images</span>
          </button>
        </div>
      </div>
    </div>
  </main>
</div>

<style>
/* Image Assessment Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.image-assessment-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: #1a202c;
  line-height: 1.6;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-icon {
  color: #3b82f6;
  display: flex;
  align-items: center;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
}

.nav-btn {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

/* Main Content */
.main-content {
  padding: 2rem 0;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Progress Header */
.progress-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 1rem;
}

.page-description {
  font-size: 1.125rem;
  color: #64748b;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.progress-bar {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.progress-step::after {
  content: '';
  position: absolute;
  top: 15px;
  left: 100%;
  width: 2rem;
  height: 2px;
  background: #e2e8f0;
  z-index: -1;
}

.progress-step:last-child::after {
  display: none;
}

.progress-step.completed::after {
  background: #059669;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.progress-step.completed .step-number {
  background: #059669;
  color: white;
}

.progress-step.active .step-number {
  background: #3b82f6;
  color: white;
}

.step-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.progress-step.completed .step-label,
.progress-step.active .step-label {
  color: #1a202c;
  font-weight: 600;
}

/* Patient Summary */
.patient-summary {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.patient-summary h3 {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.patient-details {
  font-size: 0.875rem;
  color: #64748b;
}

/* Upload Section */
.upload-section {
  margin-bottom: 3rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.section-description {
  font-size: 1rem;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.upload-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
}

.upload-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.upload-card.completed {
  border-color: #059669;
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
}

.upload-header {
  text-align: center;
  margin-bottom: 1rem;
}

.action-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.action-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.action-description {
  font-size: 0.875rem;
  color: #64748b;
}

.upload-area {
  position: relative;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 2rem 1rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 1rem;
}

.upload-area:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.upload-area input[type="file"] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
}

.upload-text {
  font-weight: 600;
  font-size: 0.875rem;
}

.upload-hint {
  font-size: 0.75rem;
  color: #94a3b8;
}

.image-preview {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .preview-overlay {
  opacity: 1;
}

.retake-btn {
  background: white;
  color: #374151;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.retake-btn:hover {
  background: #f9fafb;
}

.upload-status {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator.pending {
  color: #64748b;
}

.status-indicator.uploading {
  color: #3b82f6;
}

.status-indicator.completed {
  color: #059669;
}

.status-indicator.error {
  color: #dc2626;
}

/* Action Section */
.action-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.upload-summary {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.summary-item {
  text-align: center;
}

.summary-label {
  display: block;
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.25rem;
}

.summary-value {
  display: block;
  font-size: 1.125rem;
  font-weight: 700;
  color: #1a202c;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
}

.secondary-btn, .primary-btn {
  padding: 1.25rem 3rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border: none;
  min-width: 280px;
  width: 280px;
  justify-content: center;
  text-align: center;
  line-height: 1.4;
}

.secondary-btn {
  background: #f8fafc;
  color: #475569;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.secondary-btn:hover {
  background: #f1f5f9;
  color: #334155;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.primary-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 6px 24px rgba(59, 130, 246, 0.3);
  border: 2px solid transparent;
}

.primary-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 32px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

.primary-btn:disabled {
  background: #94a3b8;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
  .content-container {
    padding: 0 1rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .progress-bar {
    gap: 1rem;
  }

  .upload-grid {
    grid-template-columns: 1fr;
  }

  .upload-summary {
    flex-direction: column;
    gap: 1rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .secondary-btn, .primary-btn {
    min-width: 100%;
    padding: 1.25rem 2rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 1rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .upload-card {
    padding: 1rem;
  }

  .upload-area {
    padding: 1.5rem 0.75rem;
  }
}
</style>

<!-- Image Assessment JavaScript is now handled by the ImageAssessmentModule.js -->
<script>
// Function to populate patient information from localStorage
window.populatePatientInformation = function populatePatientInformation() {
  console.log('[DEBUG] populatePatientInformation called');

  try {
    // Get patient data from localStorage
    const patientDataStr = localStorage.getItem('currentPatientData');
    console.log('[DEBUG] Patient data from localStorage:', patientDataStr);

    if (patientDataStr) {
      const patientData = JSON.parse(patientDataStr);
      console.log('[DEBUG] Parsed patient data:', patientData);

      // Update patient display elements
      const patientNameEl = document.getElementById('patientName');
      const patientAgeEl = document.getElementById('patientAge');
      const patientIdEl = document.getElementById('patientId');

      console.log('[DEBUG] Found elements:', {
        patientNameEl: !!patientNameEl,
        patientAgeEl: !!patientAgeEl,
        patientIdEl: !!patientIdEl
      });

      if (patientNameEl && patientData.name) {
        patientNameEl.textContent = patientData.name;
        console.log('[DEBUG] Updated patient name:', patientData.name);
      } else {
        console.warn('[DEBUG] Could not update patient name:', { element: !!patientNameEl, data: patientData.name });
      }

      if (patientAgeEl && patientData.age) {
        patientAgeEl.textContent = patientData.age;
        console.log('[DEBUG] Updated patient age:', patientData.age);
      } else {
        console.warn('[DEBUG] Could not update patient age:', { element: !!patientAgeEl, data: patientData.age });
      }

      if (patientIdEl && patientData.id) {
        patientIdEl.textContent = patientData.id;
        console.log('[DEBUG] Updated patient ID:', patientData.id);
      } else {
        console.warn('[DEBUG] Could not update patient ID:', { element: !!patientIdEl, data: patientData.id });
      }

      console.log('[DEBUG] Patient information populated successfully');
      return true;
    } else {
      console.warn('[DEBUG] No patient data found in localStorage');
      return false;
    }
  } catch (error) {
    console.error('[DEBUG] Error populating patient information:', error);
    return false;
  }
}

// Try to populate immediately
console.log('[DEBUG] Image assessment HTML loaded - attempting immediate population');
window.populatePatientInformation();

// Also try after DOM is fully ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('[DEBUG] DOMContentLoaded - attempting population');
  window.populatePatientInformation();
});

// Try again after a short delay to handle timing issues
setTimeout(function() {
  console.log('[DEBUG] Delayed population attempt');
  window.populatePatientInformation();
}, 100);

// Placeholder script - actual functionality is loaded via module
console.log('Image assessment functionality will be provided by module');
</script>


