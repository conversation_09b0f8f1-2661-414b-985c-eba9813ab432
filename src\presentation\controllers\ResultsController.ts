import { ResultsView } from '../components/ResultsView.js';
import { ImageAnalysisController } from './ImageAnalysisController.js';

/**
 * ResultsController - Handles the dedicated results route
 * Manages URL state, data persistence, and results display
 */
export class ResultsController {
  private resultsView: ResultsView;
  private imageAnalysisController: ImageAnalysisController;
  private static readonly STORAGE_KEY = 'facialSymmetryResults';
  private static readonly STORAGE_EXPIRY_HOURS = 24; // Results expire after 24 hours

  constructor(clinicalComparisonService: any) {
    this.resultsView = new ResultsView(clinicalComparisonService);
    this.imageAnalysisController = new ImageAnalysisController();
  }

  /**
   * Initialize the results route
   * Called when navigating to #/results
   */
  async initializeResultsRoute(): Promise<void> {
    console.log('🚀 RESULTS CONTROLLER: Initializing results route');
    console.log('🌐 Current URL:', window.location.href);
    console.log('📍 Current hash:', window.location.hash);

    // Show loading state initially
    this.showLoadingState();

    try {
      // Check if this is an image-based analysis
      const analysisMode = localStorage.getItem('analysisMode');
      console.log('🔍 Analysis mode:', analysisMode);

      if (analysisMode === 'images') {
        console.log('🖼️ ResultsController: Processing image-based analysis...');
        await this.processImageAnalysis();
      } else {
        console.log('📹 ResultsController: Processing live camera analysis...');
        // Attempt to load results from storage (live camera analysis)
        const results = this.loadResultsFromStorage();

        if (results) {
          console.log('✅ ResultsController: Found stored results, displaying...');
          await this.displayResults(results);
        } else {
          console.log('❌ ResultsController: No valid results found, showing error state');
          this.showErrorState();
        }
      }
    } catch (error) {
      console.error('❌ ResultsController: Error initializing results route:', error);
      this.showErrorState();
    }
  }

  /**
   * Process image-based analysis using real MediaPipe analysis
   */
  private async processImageAnalysis(): Promise<void> {
    try {
      console.log('ResultsController: Starting real image analysis...');

      // Process uploaded images using real MediaPipe analysis
      const analysisResults = await this.imageAnalysisController.processUploadedImages();

      console.log('ResultsController: Image analysis completed successfully');

      // Store results for persistence
      this.storeResults(analysisResults);

      // Display the results
      await this.displayResults(analysisResults);

      // Clean up localStorage
      localStorage.removeItem('uploadedImages');
      localStorage.removeItem('analysisMode');

    } catch (error) {
      console.error('ResultsController: Image analysis failed:', error);
      this.showErrorState();
    }
  }

  /**
   * Store examination results for the results route
   * Called by ExamController when examination completes
   */
  storeResults(results: any): void {
    console.log('ResultsController: Storing results for results route');

    const storageData = {
      results: results,
      timestamp: new Date().toISOString(),
      expiryTime: new Date(Date.now() + (ResultsController.STORAGE_EXPIRY_HOURS * 60 * 60 * 1000)).toISOString()
    };

    try {
      localStorage.setItem(ResultsController.STORAGE_KEY, JSON.stringify(storageData));
      console.log('ResultsController: Results stored successfully');
    } catch (error) {
      console.error('ResultsController: Failed to store results:', error);
    }
  }

  /**
   * Transform examination results to match the format expected by ResultsView
   */
  private async transformResultsForView(results: any): Promise<any> {
    console.log('🔄 Transforming results for view...');
    console.log('📋 Original results structure:', {
      hasPatientInfo: !!results.patientInfo,
      hasTimestamp: !!results.timestamp,
      hasActions: !!results.actions,
      hasOverallScore: !!results.overallScore,
      hasClinicalAnalysis: !!results.clinicalAnalysis,
      hasComparisonAnalysis: !!results.comparisonAnalysis
    });

    // Create the transformed structure that ResultsView expects
    const transformedResults = {
      patientInfo: results.patientInfo || {},
      timestamp: results.timestamp || new Date().toISOString(),
      actions: results.actions || [],
      overallScore: results.overallScore || 0,
      symmetryMetrics: await this.extractSymmetryMetrics(results)
    };

    console.log('✅ Results transformed successfully');
    return transformedResults;
  }

  /**
   * Extract and transform symmetry metrics from clinical analysis results
   */
  private async extractSymmetryMetrics(results: any): Promise<any> {
    const symmetryMetrics: any = {
      // Default values
      leftEyebrowElevation: 0,
      rightEyebrowElevation: 0,
      eyebrowAsymmetry: 0,
      eyebrowSymmetry: 0,

      leftEyeClosure: 0,
      rightEyeClosure: 0,
      eyeAsymmetry: 0,
      eyeSymmetry: 0,

      leftMouthMovement: 0,
      rightMouthMovement: 0,
      mouthAsymmetry: 0,
      mouthSymmetry: 0,
      commissureDroop: 0,

      // Enhanced measurements
      leftHorizontalDistance: 0,
      rightHorizontalDistance: 0,
      leftVerticalDistance: 0,
      rightVerticalDistance: 0,
      horizontalAsymmetry: 0,
      verticalAsymmetry: 0
    };

    // Try to calculate individual measurements from stored landmark data if available
    if (results.landmarkData) {
      console.log('🔬 Calculating individual measurements from landmark data...');
      const landmarkData = results.landmarkData;

      // Calculate eyebrow elevations if we have baseline and eyebrowRaise data
      if (landmarkData.baseline && landmarkData.eyebrowRaise) {
        const eyebrowAnalysis = this.calculateEyebrowElevations(landmarkData.baseline, landmarkData.eyebrowRaise);
        if (eyebrowAnalysis) {
          symmetryMetrics.leftEyebrowElevation = eyebrowAnalysis.leftElevation;
          symmetryMetrics.rightEyebrowElevation = eyebrowAnalysis.rightElevation;
          symmetryMetrics.eyebrowAsymmetry = eyebrowAnalysis.asymmetryPercentage;
          symmetryMetrics.eyebrowSymmetry = Math.max(0, 100 - eyebrowAnalysis.asymmetryPercentage);
        }
      }

      // Calculate eye closure if we have baseline and eyeClose data
      if (landmarkData.baseline && landmarkData.eyeClose) {
        const eyeAnalysis = this.calculateEyeClosures(landmarkData.baseline, landmarkData.eyeClose);
        if (eyeAnalysis) {
          symmetryMetrics.leftEyeClosure = eyeAnalysis.leftClosurePercentage;
          symmetryMetrics.rightEyeClosure = eyeAnalysis.rightClosurePercentage;
          symmetryMetrics.eyeAsymmetry = eyeAnalysis.asymmetryPercentage;
          symmetryMetrics.eyeSymmetry = Math.max(0, 100 - eyeAnalysis.asymmetryPercentage);
        }
      }

      // Calculate mouth movement if we have baseline and smile data
      if (landmarkData.baseline && landmarkData.smile) {
        const mouthAnalysis = await this.calculateMouthMovements(landmarkData.baseline, landmarkData.smile);
        if (mouthAnalysis) {
          symmetryMetrics.leftMouthMovement = mouthAnalysis.leftMovement;
          symmetryMetrics.rightMouthMovement = mouthAnalysis.rightMovement;
          symmetryMetrics.mouthAsymmetry = mouthAnalysis.asymmetryPercentage;
          symmetryMetrics.mouthSymmetry = Math.max(0, 100 - mouthAnalysis.asymmetryPercentage);

          // Extract distance measurements if available
          if (mouthAnalysis.horizontalDistances) {
            symmetryMetrics.leftHorizontalDistance = mouthAnalysis.horizontalDistances.left;
            symmetryMetrics.rightHorizontalDistance = mouthAnalysis.horizontalDistances.right;
          }
          if (mouthAnalysis.verticalDistances) {
            symmetryMetrics.leftVerticalDistance = mouthAnalysis.verticalDistances.left;
            symmetryMetrics.rightVerticalDistance = mouthAnalysis.verticalDistances.right;
          }
        }
      }
    }

    // Fallback: Extract data from clinical analysis regional analysis if landmark calculations failed
    if (results.clinicalAnalysis && results.clinicalAnalysis.regional_analysis) {
      const regional = results.clinicalAnalysis.regional_analysis;

      // Only use regional analysis if we don't have individual measurements
      if (symmetryMetrics.leftEyebrowElevation === 0 && symmetryMetrics.rightEyebrowElevation === 0 && regional.forehead) {
        symmetryMetrics.eyebrowAsymmetry = regional.forehead.asymmetry_percentage || 0;
        symmetryMetrics.eyebrowSymmetry = Math.max(0, 100 - symmetryMetrics.eyebrowAsymmetry);
      }

      if (symmetryMetrics.leftEyeClosure === 0 && symmetryMetrics.rightEyeClosure === 0 && regional.eye) {
        symmetryMetrics.eyeAsymmetry = regional.eye.asymmetry_percentage || 0;
        symmetryMetrics.eyeSymmetry = Math.max(0, 100 - symmetryMetrics.eyeAsymmetry);

        // Check for lagophthalmos
        if (regional.eye.lagophthalmos_present) {
          symmetryMetrics.leftEyeClosure = 50; // Simulated values
          symmetryMetrics.rightEyeClosure = 80;
        }
      }

      if (symmetryMetrics.leftMouthMovement === 0 && symmetryMetrics.rightMouthMovement === 0 && regional.smile) {
        symmetryMetrics.mouthAsymmetry = regional.smile.asymmetry_percentage || 0;
        symmetryMetrics.mouthSymmetry = Math.max(0, 100 - symmetryMetrics.mouthAsymmetry);
        symmetryMetrics.commissureDroop = regional.smile.commissure_droop || 0;

        // Extract corner movement analysis if available
        if (regional.smile.corner_movement_analysis) {
          const cornerAnalysis = regional.smile.corner_movement_analysis;
          symmetryMetrics.leftMouthMovement = cornerAnalysis.leftMovement || 0;
          symmetryMetrics.rightMouthMovement = cornerAnalysis.rightMovement || 0;
        }
      }
    }

    // Extract additional metrics from symmetryMetrics if available (from ClinicalIntegrationService)
    if (results.clinicalAnalysis && results.clinicalAnalysis.symmetryMetrics) {
      const additionalMetrics = results.clinicalAnalysis.symmetryMetrics;
      Object.assign(symmetryMetrics, additionalMetrics);
    }

    console.log('📊 Extracted symmetry metrics:', symmetryMetrics);
    return symmetryMetrics;
  }

  /**
   * Calculate eyebrow elevations from landmark data in degrees
   * Uses angle calculation for clinically accurate measurements
   */
  private calculateEyebrowElevations(baseline: any[], eyebrowRaise: any[]): any {
    try {
      if (baseline.length < 468 || eyebrowRaise.length < 468) {
        console.warn('Insufficient landmark data for eyebrow analysis');
        return null;
      }

      // Define eyebrow landmark indices
      const leftEyebrowLandmarks = [70, 63, 105, 66, 107];
      const rightEyebrowLandmarks = [300, 293, 334, 296, 336];

      // Extract landmark points
      const baselineLeftEyebrow = leftEyebrowLandmarks.map(i => baseline[i]);
      const baselineRightEyebrow = rightEyebrowLandmarks.map(i => baseline[i]);
      const raisedLeftEyebrow = leftEyebrowLandmarks.map(i => eyebrowRaise[i]);
      const raisedRightEyebrow = rightEyebrowLandmarks.map(i => eyebrowRaise[i]);

      // Validate all landmarks are available
      const missingData = baselineLeftEyebrow.some(p => !p) || baselineRightEyebrow.some(p => !p) ||
                         raisedLeftEyebrow.some(p => !p) || raisedRightEyebrow.some(p => !p);

      if (missingData) {
        console.warn('Missing eyebrow landmark points');
        return null;
      }

      // Calculate face width for normalization (distance between outer canthi in normalized coordinates)
      const faceWidthNormalized = Math.sqrt(
        Math.pow(baseline[454].x - baseline[234].x, 2) +
        Math.pow(baseline[454].y - baseline[234].y, 2)
      );

      // Use face width as reference for normalization (no conversion to mm needed for angle calculation)

      // Calculate eyebrow elevation angles in degrees using normalized coordinates
      const leftAngles = baselineLeftEyebrow.map((baselinePoint, i) => {
        const raisedPoint = raisedLeftEyebrow[i];

        // Calculate vertical displacement in normalized coordinates
        const verticalDisplacement = Math.abs(baselinePoint.y - raisedPoint.y);

        // Calculate horizontal reference distance (to eye center for angle calculation)
        const eyeCenterLeft = baseline[133]; // Left eye center
        const horizontalDistance = Math.abs(baselinePoint.x - eyeCenterLeft.x);

        // Calculate elevation angle using arctangent (normalized coordinates work fine for angles)
        const angleRadians = Math.atan(verticalDisplacement / Math.max(horizontalDistance, 0.01));
        const angleDegrees = angleRadians * (180 / Math.PI);

        return angleDegrees;
      });

      const rightAngles = baselineRightEyebrow.map((baselinePoint, i) => {
        const raisedPoint = raisedRightEyebrow[i];

        // Calculate vertical displacement in normalized coordinates
        const verticalDisplacement = Math.abs(baselinePoint.y - raisedPoint.y);

        // Calculate horizontal reference distance (to eye center for angle calculation)
        const eyeCenterRight = baseline[362]; // Right eye center
        const horizontalDistance = Math.abs(baselinePoint.x - eyeCenterRight.x);

        // Calculate elevation angle using arctangent (normalized coordinates work fine for angles)
        const angleRadians = Math.atan(verticalDisplacement / Math.max(horizontalDistance, 0.01));
        const angleDegrees = angleRadians * (180 / Math.PI);

        return angleDegrees;
      });

      const leftMeanElevation = leftAngles.reduce((a, b) => a + b, 0) / leftAngles.length;
      const rightMeanElevation = rightAngles.reduce((a, b) => a + b, 0) / rightAngles.length;

      // Calculate asymmetry percentage
      const asymmetryPercentage = Math.abs(leftMeanElevation - rightMeanElevation) /
                                  Math.max(leftMeanElevation, rightMeanElevation) * 100;

      console.log(`Eyebrow analysis: Left=${leftMeanElevation.toFixed(2)}°, Right=${rightMeanElevation.toFixed(2)}°, Asymmetry=${asymmetryPercentage.toFixed(1)}%`);

      return {
        leftElevation: leftMeanElevation,
        rightElevation: rightMeanElevation,
        asymmetryPercentage: asymmetryPercentage
      };
    } catch (error) {
      console.error('Error in eyebrow analysis calculation:', error);
      return null;
    }
  }

  /**
   * Calculate eye closures from landmark data
   * Uses the same method as ImageAnalysisController for consistency
   */
  private calculateEyeClosures(baseline: any[], eyeClose: any[]): any {
    try {
      if (baseline.length < 468 || eyeClose.length < 468) {
        console.warn('Insufficient landmark data for eye analysis');
        return null;
      }

      // Define eye landmark pairs for vertical measurements
      const leftEyePairs = [[159, 145], [158, 153], [160, 144]]; // Top-bottom pairs for left eye
      const rightEyePairs = [[386, 374], [385, 380], [387, 373]]; // Top-bottom pairs for right eye

      // Calculate baseline eye opening distances
      const baselineLeftDistances = leftEyePairs.map(([top, bottom]) => {
        if (baseline[top] && baseline[bottom]) {
          return Math.abs(baseline[top].y - baseline[bottom].y);
        }
        return 0;
      });

      const baselineRightDistances = rightEyePairs.map(([top, bottom]) => {
        if (baseline[top] && baseline[bottom]) {
          return Math.abs(baseline[top].y - baseline[bottom].y);
        }
        return 0;
      });

      // Calculate closure eye distances
      const closureLeftDistances = leftEyePairs.map(([top, bottom]) => {
        if (eyeClose[top] && eyeClose[bottom]) {
          return Math.abs(eyeClose[top].y - eyeClose[bottom].y);
        }
        return 0;
      });

      const closureRightDistances = rightEyePairs.map(([top, bottom]) => {
        if (eyeClose[top] && eyeClose[bottom]) {
          return Math.abs(eyeClose[top].y - eyeClose[bottom].y);
        }
        return 0;
      });

      // Calculate average distances
      const avgBaselineLeft = baselineLeftDistances.reduce((a, b) => a + b, 0) / baselineLeftDistances.length;
      const avgBaselineRight = baselineRightDistances.reduce((a, b) => a + b, 0) / baselineRightDistances.length;
      const avgClosureLeft = closureLeftDistances.reduce((a, b) => a + b, 0) / closureLeftDistances.length;
      const avgClosureRight = closureRightDistances.reduce((a, b) => a + b, 0) / closureRightDistances.length;

      // Calculate closure percentages
      const leftClosurePercentage = ((avgBaselineLeft - avgClosureLeft) / avgBaselineLeft) * 100;
      const rightClosurePercentage = ((avgBaselineRight - avgClosureRight) / avgBaselineRight) * 100;

      // Calculate asymmetry
      const asymmetryPercentage = Math.abs(leftClosurePercentage - rightClosurePercentage);

      console.log(`Eye analysis: Left=${leftClosurePercentage.toFixed(1)}%, Right=${rightClosurePercentage.toFixed(1)}%, Asymmetry=${asymmetryPercentage.toFixed(1)}%`);

      return {
        leftClosurePercentage: leftClosurePercentage,
        rightClosurePercentage: rightClosurePercentage,
        asymmetryPercentage: asymmetryPercentage
      };
    } catch (error) {
      console.error('Error in eye closure analysis calculation:', error);
      return null;
    }
  }

  /**
   * Calculate mouth movements from landmark data in millimeters
   * Uses distance estimation for clinically accurate measurements
   */
  private async calculateMouthMovements(baseline: any[], smile: any[]): Promise<any> {
    try {
      if (baseline.length < 468 || smile.length < 468) {
        console.warn('Insufficient landmark data for mouth analysis');
        return null;
      }

      // Define mouth corner landmarks
      const leftCornerIndex = 61;
      const rightCornerIndex = 291;

      const baselineLeftCorner = baseline[leftCornerIndex];
      const baselineRightCorner = baseline[rightCornerIndex];
      const smileLeftCorner = smile[leftCornerIndex];
      const smileRightCorner = smile[rightCornerIndex];

      if (!baselineLeftCorner || !baselineRightCorner || !smileLeftCorner || !smileRightCorner) {
        console.warn('Missing mouth corner landmarks');
        return null;
      }

      // Calculate interpupillary distance (IPD) for normalization (distance between outer canthi in normalized coordinates)
      const ipdNormalized = Math.sqrt(
        Math.pow(baseline[454].x - baseline[234].x, 2) +
        Math.pow(baseline[454].y - baseline[234].y, 2)
      );

      // Calculate movement distances in normalized coordinates
      const leftMovementNormalized = Math.sqrt(
        Math.pow(smileLeftCorner.x - baselineLeftCorner.x, 2) +
        Math.pow(smileLeftCorner.y - baselineLeftCorner.y, 2)
      );

      const rightMovementNormalized = Math.sqrt(
        Math.pow(smileRightCorner.x - baselineRightCorner.x, 2) +
        Math.pow(smileRightCorner.y - baselineRightCorner.y, 2)
      );

      // Convert to millimeters using IPD as reference
      // Average adult IPD is ~63mm, so we normalize by IPD and scale
      const estimatedIPDMm = 63;
      const leftMovementMm = (leftMovementNormalized / ipdNormalized) * estimatedIPDMm;
      const rightMovementMm = (rightMovementNormalized / ipdNormalized) * estimatedIPDMm;

      // Calculate asymmetry percentage
      const asymmetryPercentage = Math.abs(leftMovementMm - rightMovementMm) /
                                  Math.max(leftMovementMm, rightMovementMm) * 100;

      // Use AnalysisVisualizationService to calculate distances using the same reference lines as visualization
      // This ensures consistency between what's shown in the UI and what's calculated in results
      let leftHorizontalMm = 0;
      let rightHorizontalMm = 0;
      let leftVerticalMm = 0;
      let rightVerticalMm = 0;
      let horizontalAsymmetryFromService = 0;
      let verticalAsymmetryFromService = 0;

      try {
        // Import and use AnalysisVisualizationService for consistent calculations
        const { AnalysisVisualizationService } = await import('../services/AnalysisVisualizationService');

        // Create a temporary canvas element and add it to DOM temporarily
        const tempCanvas = document.createElement('canvas');
        tempCanvas.id = 'temp-analysis-canvas';
        tempCanvas.style.display = 'none';
        document.body.appendChild(tempCanvas);

        try {
          // Create a temporary instance to use the calculation method
          const analysisService = new AnalysisVisualizationService('temp-analysis-canvas');

          // Calculate distances using the same reference lines as visualization
          const distanceMeasurements = analysisService.calculateMouthMovementMeasurements(smile);

          if (distanceMeasurements) {
            leftHorizontalMm = distanceMeasurements.leftHorizontalDistance;
            rightHorizontalMm = distanceMeasurements.rightHorizontalDistance;
            leftVerticalMm = distanceMeasurements.leftVerticalDistance;
            rightVerticalMm = distanceMeasurements.rightVerticalDistance;
            horizontalAsymmetryFromService = distanceMeasurements.horizontalAsymmetry;
            verticalAsymmetryFromService = distanceMeasurements.verticalAsymmetry;

            console.log(`✅ Using AnalysisVisualizationService for distance calculations:`);
            console.log(`   Horizontal distances: Left=${leftHorizontalMm.toFixed(2)}mm, Right=${rightHorizontalMm.toFixed(2)}mm`);
            console.log(`   Vertical distances: Left=${leftVerticalMm.toFixed(2)}mm, Right=${rightVerticalMm.toFixed(2)}mm`);
          } else {
            console.warn('⚠️ AnalysisVisualizationService returned null, using fallback calculation');
            // Fallback to simple calculation if service fails
            leftHorizontalMm = leftMovementMm * 0.7; // Approximate horizontal component
            rightHorizontalMm = rightMovementMm * 0.7;
            leftVerticalMm = leftMovementMm * 0.3; // Approximate vertical component
            rightVerticalMm = rightMovementMm * 0.3;
          }
        } finally {
          // Clean up temporary canvas
          document.body.removeChild(tempCanvas);
        }
      } catch (error) {
        console.error('❌ Error using AnalysisVisualizationService:', error);
        // Fallback calculation
        leftHorizontalMm = leftMovementMm * 0.7;
        rightHorizontalMm = rightMovementMm * 0.7;
        leftVerticalMm = leftMovementMm * 0.3;
        rightVerticalMm = rightMovementMm * 0.3;
      }

      console.log(`Horizontal Distances (to tilted reference line): Left=${leftHorizontalMm.toFixed(2)}mm, Right=${rightHorizontalMm.toFixed(2)}mm`);
      console.log(`Vertical Distances (to vertical reference line): Left=${leftVerticalMm.toFixed(2)}mm, Right=${rightVerticalMm.toFixed(2)}mm`);

      console.log(`Mouth analysis: Left=${leftMovementMm.toFixed(2)}mm, Right=${rightMovementMm.toFixed(2)}mm, Asymmetry=${asymmetryPercentage.toFixed(1)}%`);

      return {
        leftMovement: leftMovementMm,
        rightMovement: rightMovementMm,
        asymmetryPercentage: asymmetryPercentage,
        horizontalDistances: {
          left: leftHorizontalMm,
          right: rightHorizontalMm
        },
        verticalDistances: {
          left: leftVerticalMm,
          right: rightVerticalMm
        }
      };
    } catch (error) {
      console.error('Error in mouth movement analysis calculation:', error);
      return null;
    }
  }

  /**
   * Load results from localStorage with expiry check
   */
  private loadResultsFromStorage(): any | null {
    try {
      console.log('🔍 LOADING RESULTS FROM STORAGE...');
      console.log('🔑 Storage key:', ResultsController.STORAGE_KEY);

      const storedData = localStorage.getItem(ResultsController.STORAGE_KEY);
      console.log('📦 Raw stored data:', {
        exists: !!storedData,
        length: storedData?.length || 0,
        preview: storedData?.substring(0, 100) + '...'
      });

      if (!storedData) {
        console.log('❌ ResultsController: No stored results found in localStorage');
        console.log('🔍 All localStorage keys:', Object.keys(localStorage));
        return null;
      }

      const parsedData = JSON.parse(storedData);
      console.log('📋 Parsed data structure:', {
        hasResults: !!parsedData.results,
        hasTimestamp: !!parsedData.timestamp,
        hasExpiryTime: !!parsedData.expiryTime,
        resultKeys: parsedData.results ? Object.keys(parsedData.results) : 'none'
      });

      const now = new Date();
      const expiryTime = new Date(parsedData.expiryTime);
      console.log('⏰ Expiry check:', {
        now: now.toISOString(),
        expiryTime: expiryTime.toISOString(),
        isExpired: now > expiryTime
      });

      if (now > expiryTime) {
        console.log('⏰ ResultsController: Stored results have expired, clearing...');
        localStorage.removeItem(ResultsController.STORAGE_KEY);
        return null;
      }

      console.log('✅ ResultsController: Valid stored results found and loaded');
      return parsedData.results;
    } catch (error) {
      console.error('❌ ResultsController: Error loading results from storage:', error);
      localStorage.removeItem(ResultsController.STORAGE_KEY);
      return null;
    }
  }

  /**
   * Display the examination results
   */
  private async displayResults(results: any): Promise<void> {
    try {
      // Hide loading and error states
      this.hideLoadingState();
      this.hideErrorState();

      // Transform the results to match the expected format
      const transformedResults = await this.transformResultsForView(results);

      // Set results in the ResultsView
      this.resultsView.setResults(transformedResults);

      // Wait for results content container to be available (increased timeout and extra logging)
      const resultsContent = await this.waitForElement('resultsContent', 10000);
      if (!resultsContent) {
        console.error('[ResultsController] ERROR: #resultsContent not found after waiting 10s. Current DOM:', document.body.innerHTML);
        throw new Error('Results content container not found after waiting');
      }

      // Generate and insert the results HTML
      resultsContent.innerHTML = this.resultsView.generateResultsHTML();
      resultsContent.style.display = 'block';

      // Set up event listeners for the results page (with delay to ensure DOM is ready)
      setTimeout(() => {
        this.setupResultsEventListeners();
      }, 100);

      console.log('ResultsController: Results displayed successfully');
    } catch (error) {
      console.error('ResultsController: Error displaying results:', error);
      this.showErrorState();
    }
  }

  /**
   * Show loading state
   */
  private showLoadingState(): void {
    const loading = document.getElementById('resultsLoading');
    const error = document.getElementById('resultsError');
    const content = document.getElementById('resultsContent');

    if (loading) loading.style.display = 'block';
    if (error) error.style.display = 'none';
    if (content) content.style.display = 'none';
  }

  /**
   * Hide loading state
   */
  private hideLoadingState(): void {
    const loading = document.getElementById('resultsLoading');
    if (loading) loading.style.display = 'none';
  }

  /**
   * Show error state when no results are available
   */
  private showErrorState(): void {
    const loading = document.getElementById('resultsLoading');
    const error = document.getElementById('resultsError');
    const content = document.getElementById('resultsContent');

    if (loading) loading.style.display = 'none';
    if (error) error.style.display = 'block';
    if (content) content.style.display = 'none';

    // Set up error state event listeners
    this.setupErrorStateEventListeners();
  }

  /**
   * Hide error state
   */
  private hideErrorState(): void {
    const error = document.getElementById('resultsError');
    if (error) error.style.display = 'none';
  }

  /**
   * Set up event listeners for the results page
   */
  private setupResultsEventListeners(): void {
    console.log('ResultsController: Setting up event listeners...');

    // Use the correct button IDs that match the HTML
    const pdfBtn = document.getElementById('exportPdfBtn');
    const printBtn = document.getElementById('printResultsBtn');
    const backBtn = document.getElementById('backToHome');

    console.log('Button elements found:', {
      pdfBtn: !!pdfBtn,
      printBtn: !!printBtn,
      backBtn: !!backBtn
    });

    if (pdfBtn) {
      pdfBtn.addEventListener('click', () => {
        console.log('PDF export clicked from ResultsController');
        this.resultsView.exportToPDF();
      });
    } else {
      console.warn('PDF button not found with ID: exportPdfBtn');
    }

    if (printBtn) {
      printBtn.addEventListener('click', () => {
        console.log('Print button clicked from ResultsController');
        this.resultsView.printResults();
      });
    } else {
      console.warn('Print button not found with ID: printResultsBtn');
    }

    if (backBtn) {
      backBtn.addEventListener('click', () => {
        console.log('Back to home clicked');
        this.navigateToHome();
      });
    }
  }

  /**
   * Set up event listeners for the error state
   */
  private setupErrorStateEventListeners(): void {
    const startNewExamBtn = document.getElementById('startNewExam');
    const goHomeBtn = document.getElementById('goHome');

    if (startNewExamBtn) {
      startNewExamBtn.addEventListener('click', () => {
        console.log('Start new exam clicked');
        this.navigateToHome();
      });
    }

    if (goHomeBtn) {
      goHomeBtn.addEventListener('click', () => {
        console.log('Go home clicked');
        this.navigateToHome();
      });
    }
  }

  /**
   * Navigate to home page (root route)
   */
  private navigateToHome(): void {
    window.history.pushState({}, '', '/');
  }

  /**
   * Clear stored results (useful for cleanup)
   */
  clearStoredResults(): void {
    localStorage.removeItem(ResultsController.STORAGE_KEY);
    console.log('ResultsController: Stored results cleared');
  }

  /**
   * Check if results are available
   */
  hasStoredResults(): boolean {
    return this.loadResultsFromStorage() !== null;
  }

  /**
   * Get the URL for sharing results
   */
  getResultsURL(): string {
    return `${window.location.origin}${window.location.pathname}#/results`;
  }

  /**
   * Wait for a DOM element to be available
   */
  private waitForElement(elementId: string, timeoutMs: number = 5000): Promise<HTMLElement | null> {
    return new Promise((resolve) => {
      const element = document.getElementById(elementId);
      if (element) {
        resolve(element);
        return;
      }

      let attempts = 0;
      const maxAttempts = timeoutMs / 100; // Check every 100ms

      const checkForElement = () => {
        attempts++;
        const el = document.getElementById(elementId);

        if (el) {
          console.log(`Element '${elementId}' found after ${attempts * 100}ms`);
          resolve(el);
        } else if (attempts < maxAttempts) {
          setTimeout(checkForElement, 100);
        } else {
          console.error(`Timeout waiting for element '${elementId}' after ${timeoutMs}ms`);
          resolve(null);
        }
      };

      setTimeout(checkForElement, 100);
    });
  }
}
