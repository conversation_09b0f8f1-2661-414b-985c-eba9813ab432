import { ResultsView } from '../components/ResultsView.js';
import { ImageAnalysisController } from './ImageAnalysisController.js';

/**
 * ResultsController - Handles the dedicated results route
 * Manages URL state, data persistence, and results display
 */
export class ResultsController {
  private resultsView: ResultsView;
  private imageAnalysisController: ImageAnalysisController;
  private static readonly STORAGE_KEY = 'facialSymmetryResults';
  private static readonly STORAGE_EXPIRY_HOURS = 24; // Results expire after 24 hours

  constructor(clinicalComparisonService: any) {
    this.resultsView = new ResultsView(clinicalComparisonService);
    this.imageAnalysisController = new ImageAnalysisController();
  }

  /**
   * Initialize the results route
   * Called when navigating to #/results
   */
  async initializeResultsRoute(): Promise<void> {
    console.log('🚀 RESULTS CONTROLLER: Initializing results route');
    console.log('🌐 Current URL:', window.location.href);
    console.log('📍 Current hash:', window.location.hash);

    // Show loading state initially
    this.showLoadingState();

    try {
      // Check if this is an image-based analysis
      const analysisMode = localStorage.getItem('analysisMode');
      console.log('🔍 Analysis mode:', analysisMode);

      if (analysisMode === 'images') {
        console.log('🖼️ ResultsController: Processing image-based analysis...');
        await this.processImageAnalysis();
      } else {
        console.log('📹 ResultsController: Processing live camera analysis...');
        // Attempt to load results from storage (live camera analysis)
        const results = this.loadResultsFromStorage();

        if (results) {
          console.log('✅ ResultsController: Found stored results, displaying...');
          await this.displayResults(results);
        } else {
          console.log('❌ ResultsController: No valid results found, showing error state');
          this.showErrorState();
        }
      }
    } catch (error) {
      console.error('❌ ResultsController: Error initializing results route:', error);
      this.showErrorState();
    }
  }

  /**
   * Process image-based analysis using real MediaPipe analysis
   */
  private async processImageAnalysis(): Promise<void> {
    try {
      console.log('ResultsController: Starting real image analysis...');

      // Process uploaded images using real MediaPipe analysis
      const analysisResults = await this.imageAnalysisController.processUploadedImages();

      console.log('ResultsController: Image analysis completed successfully');

      // Store results for persistence
      this.storeResults(analysisResults);

      // Display the results
      await this.displayResults(analysisResults);

      // Clean up localStorage
      localStorage.removeItem('uploadedImages');
      localStorage.removeItem('analysisMode');

    } catch (error) {
      console.error('ResultsController: Image analysis failed:', error);
      this.showErrorState();
    }
  }

  /**
   * Store examination results for the results route
   * Called by ExamController when examination completes
   */
  storeResults(results: any): void {
    console.log('ResultsController: Storing results for results route');

    const storageData = {
      results: results,
      timestamp: new Date().toISOString(),
      expiryTime: new Date(Date.now() + (ResultsController.STORAGE_EXPIRY_HOURS * 60 * 60 * 1000)).toISOString()
    };

    try {
      localStorage.setItem(ResultsController.STORAGE_KEY, JSON.stringify(storageData));
      console.log('ResultsController: Results stored successfully');
    } catch (error) {
      console.error('ResultsController: Failed to store results:', error);
    }
  }

  /**
   * Transform examination results to match the format expected by ResultsView
   */
  private transformResultsForView(results: any): any {
    console.log('🔄 Transforming results for view...');
    console.log('📋 Original results structure:', {
      hasPatientInfo: !!results.patientInfo,
      hasTimestamp: !!results.timestamp,
      hasActions: !!results.actions,
      hasOverallScore: !!results.overallScore,
      hasClinicalAnalysis: !!results.clinicalAnalysis,
      hasComparisonAnalysis: !!results.comparisonAnalysis
    });

    // Create the transformed structure that ResultsView expects
    const transformedResults = {
      patientInfo: results.patientInfo || {},
      timestamp: results.timestamp || new Date().toISOString(),
      actions: results.actions || [],
      overallScore: results.overallScore || 0,
      symmetryMetrics: this.extractSymmetryMetrics(results)
    };

    console.log('✅ Results transformed successfully');
    return transformedResults;
  }

  /**
   * Extract and transform symmetry metrics from clinical analysis results
   */
  private extractSymmetryMetrics(results: any): any {
    const symmetryMetrics: any = {
      // Default values
      leftEyebrowElevation: 0,
      rightEyebrowElevation: 0,
      eyebrowAsymmetry: 0,
      eyebrowSymmetry: 85,

      leftEyeClosure: 0,
      rightEyeClosure: 0,
      eyeAsymmetry: 0,
      eyeSymmetry: 85,

      leftMouthMovement: 0,
      rightMouthMovement: 0,
      mouthAsymmetry: 0,
      mouthSymmetry: 85,
      commissureDroop: 0,

      // Enhanced measurements
      leftHorizontalDistance: 0,
      rightHorizontalDistance: 0,
      leftVerticalDistance: 0,
      rightVerticalDistance: 0,
      horizontalAsymmetry: 0,
      verticalAsymmetry: 0
    };

    // Extract data from clinical analysis if available
    if (results.clinicalAnalysis && results.clinicalAnalysis.regional_analysis) {
      const regional = results.clinicalAnalysis.regional_analysis;

      // Eyebrow/Forehead analysis
      if (regional.forehead) {
        symmetryMetrics.eyebrowAsymmetry = regional.forehead.asymmetry_percentage || 0;
        symmetryMetrics.eyebrowSymmetry = Math.max(0, 100 - symmetryMetrics.eyebrowAsymmetry);
      }

      // Eye analysis
      if (regional.eye) {
        symmetryMetrics.eyeAsymmetry = regional.eye.asymmetry_percentage || 0;
        symmetryMetrics.eyeSymmetry = Math.max(0, 100 - symmetryMetrics.eyeAsymmetry);

        // Check for lagophthalmos
        if (regional.eye.lagophthalmos_present) {
          symmetryMetrics.leftEyeClosure = 50; // Simulated values
          symmetryMetrics.rightEyeClosure = 80;
        }
      }

      // Mouth/Smile analysis
      if (regional.smile) {
        symmetryMetrics.mouthAsymmetry = regional.smile.asymmetry_percentage || 0;
        symmetryMetrics.mouthSymmetry = Math.max(0, 100 - symmetryMetrics.mouthAsymmetry);
        symmetryMetrics.commissureDroop = regional.smile.commissure_droop || 0;

        // Extract corner movement analysis if available
        if (regional.smile.corner_movement_analysis) {
          const cornerAnalysis = regional.smile.corner_movement_analysis;
          symmetryMetrics.leftMouthMovement = cornerAnalysis.leftMovement || 0;
          symmetryMetrics.rightMouthMovement = cornerAnalysis.rightMovement || 0;
        }
      }
    }

    // Extract additional metrics from symmetryMetrics if available (from ClinicalIntegrationService)
    if (results.clinicalAnalysis && results.clinicalAnalysis.symmetryMetrics) {
      const additionalMetrics = results.clinicalAnalysis.symmetryMetrics;
      Object.assign(symmetryMetrics, additionalMetrics);
    }

    console.log('📊 Extracted symmetry metrics:', symmetryMetrics);
    return symmetryMetrics;
  }

  /**
   * Load results from localStorage with expiry check
   */
  private loadResultsFromStorage(): any | null {
    try {
      console.log('🔍 LOADING RESULTS FROM STORAGE...');
      console.log('🔑 Storage key:', ResultsController.STORAGE_KEY);

      const storedData = localStorage.getItem(ResultsController.STORAGE_KEY);
      console.log('📦 Raw stored data:', {
        exists: !!storedData,
        length: storedData?.length || 0,
        preview: storedData?.substring(0, 100) + '...'
      });

      if (!storedData) {
        console.log('❌ ResultsController: No stored results found in localStorage');
        console.log('🔍 All localStorage keys:', Object.keys(localStorage));
        return null;
      }

      const parsedData = JSON.parse(storedData);
      console.log('📋 Parsed data structure:', {
        hasResults: !!parsedData.results,
        hasTimestamp: !!parsedData.timestamp,
        hasExpiryTime: !!parsedData.expiryTime,
        resultKeys: parsedData.results ? Object.keys(parsedData.results) : 'none'
      });

      const now = new Date();
      const expiryTime = new Date(parsedData.expiryTime);
      console.log('⏰ Expiry check:', {
        now: now.toISOString(),
        expiryTime: expiryTime.toISOString(),
        isExpired: now > expiryTime
      });

      if (now > expiryTime) {
        console.log('⏰ ResultsController: Stored results have expired, clearing...');
        localStorage.removeItem(ResultsController.STORAGE_KEY);
        return null;
      }

      console.log('✅ ResultsController: Valid stored results found and loaded');
      return parsedData.results;
    } catch (error) {
      console.error('❌ ResultsController: Error loading results from storage:', error);
      localStorage.removeItem(ResultsController.STORAGE_KEY);
      return null;
    }
  }

  /**
   * Display the examination results
   */
  private async displayResults(results: any): Promise<void> {
    try {
      // Hide loading and error states
      this.hideLoadingState();
      this.hideErrorState();

      // Transform the results to match the expected format
      const transformedResults = this.transformResultsForView(results);

      // Set results in the ResultsView
      this.resultsView.setResults(transformedResults);

      // Wait for results content container to be available (increased timeout and extra logging)
      const resultsContent = await this.waitForElement('resultsContent', 10000);
      if (!resultsContent) {
        console.error('[ResultsController] ERROR: #resultsContent not found after waiting 10s. Current DOM:', document.body.innerHTML);
        throw new Error('Results content container not found after waiting');
      }

      // Generate and insert the results HTML
      resultsContent.innerHTML = this.resultsView.generateResultsHTML();
      resultsContent.style.display = 'block';

      // Set up event listeners for the results page (with delay to ensure DOM is ready)
      setTimeout(() => {
        this.setupResultsEventListeners();
      }, 100);

      console.log('ResultsController: Results displayed successfully');
    } catch (error) {
      console.error('ResultsController: Error displaying results:', error);
      this.showErrorState();
    }
  }

  /**
   * Show loading state
   */
  private showLoadingState(): void {
    const loading = document.getElementById('resultsLoading');
    const error = document.getElementById('resultsError');
    const content = document.getElementById('resultsContent');

    if (loading) loading.style.display = 'block';
    if (error) error.style.display = 'none';
    if (content) content.style.display = 'none';
  }

  /**
   * Hide loading state
   */
  private hideLoadingState(): void {
    const loading = document.getElementById('resultsLoading');
    if (loading) loading.style.display = 'none';
  }

  /**
   * Show error state when no results are available
   */
  private showErrorState(): void {
    const loading = document.getElementById('resultsLoading');
    const error = document.getElementById('resultsError');
    const content = document.getElementById('resultsContent');

    if (loading) loading.style.display = 'none';
    if (error) error.style.display = 'block';
    if (content) content.style.display = 'none';

    // Set up error state event listeners
    this.setupErrorStateEventListeners();
  }

  /**
   * Hide error state
   */
  private hideErrorState(): void {
    const error = document.getElementById('resultsError');
    if (error) error.style.display = 'none';
  }

  /**
   * Set up event listeners for the results page
   */
  private setupResultsEventListeners(): void {
    console.log('ResultsController: Setting up event listeners...');

    // Use the correct button IDs that match the HTML
    const pdfBtn = document.getElementById('exportPdfBtn');
    const printBtn = document.getElementById('printResultsBtn');
    const backBtn = document.getElementById('backToHome');

    console.log('Button elements found:', {
      pdfBtn: !!pdfBtn,
      printBtn: !!printBtn,
      backBtn: !!backBtn
    });

    if (pdfBtn) {
      pdfBtn.addEventListener('click', () => {
        console.log('PDF export clicked from ResultsController');
        this.resultsView.exportToPDF();
      });
    } else {
      console.warn('PDF button not found with ID: exportPdfBtn');
    }

    if (printBtn) {
      printBtn.addEventListener('click', () => {
        console.log('Print button clicked from ResultsController');
        this.resultsView.printResults();
      });
    } else {
      console.warn('Print button not found with ID: printResultsBtn');
    }

    if (backBtn) {
      backBtn.addEventListener('click', () => {
        console.log('Back to home clicked');
        this.navigateToHome();
      });
    }
  }

  /**
   * Set up event listeners for the error state
   */
  private setupErrorStateEventListeners(): void {
    const startNewExamBtn = document.getElementById('startNewExam');
    const goHomeBtn = document.getElementById('goHome');

    if (startNewExamBtn) {
      startNewExamBtn.addEventListener('click', () => {
        console.log('Start new exam clicked');
        this.navigateToHome();
      });
    }

    if (goHomeBtn) {
      goHomeBtn.addEventListener('click', () => {
        console.log('Go home clicked');
        this.navigateToHome();
      });
    }
  }

  /**
   * Navigate to home page (root route)
   */
  private navigateToHome(): void {
    window.history.pushState({}, '', '/');
  }

  /**
   * Clear stored results (useful for cleanup)
   */
  clearStoredResults(): void {
    localStorage.removeItem(ResultsController.STORAGE_KEY);
    console.log('ResultsController: Stored results cleared');
  }

  /**
   * Check if results are available
   */
  hasStoredResults(): boolean {
    return this.loadResultsFromStorage() !== null;
  }

  /**
   * Get the URL for sharing results
   */
  getResultsURL(): string {
    return `${window.location.origin}${window.location.pathname}#/results`;
  }

  /**
   * Wait for a DOM element to be available
   */
  private waitForElement(elementId: string, timeoutMs: number = 5000): Promise<HTMLElement | null> {
    return new Promise((resolve) => {
      const element = document.getElementById(elementId);
      if (element) {
        resolve(element);
        return;
      }

      let attempts = 0;
      const maxAttempts = timeoutMs / 100; // Check every 100ms

      const checkForElement = () => {
        attempts++;
        const el = document.getElementById(elementId);

        if (el) {
          console.log(`Element '${elementId}' found after ${attempts * 100}ms`);
          resolve(el);
        } else if (attempts < maxAttempts) {
          setTimeout(checkForElement, 100);
        } else {
          console.error(`Timeout waiting for element '${elementId}' after ${timeoutMs}ms`);
          resolve(null);
        }
      };

      setTimeout(checkForElement, 100);
    });
  }
}
