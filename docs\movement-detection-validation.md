# Movement Detection and Validation System

## Overview

The facial symmetry examination workflow now includes comprehensive movement detection and validation to ensure all core facial movements are properly captured before allowing progression to the next examination step or completion.

## Key Features

### 1. **Required Movement Detection**
- **Eyebrow elevation** (raise eyebrows action)
- **Eye closure** (close eyes tightly action) 
- **Mouth smiling** (smile wide action)
- **Baseline** (neutral/resting face)

### 2. **Real-time Validation**
- Validates MediaPipe landmark data (468 or 478 landmarks)
- Calculates movement intensity compared to baseline
- Provides confidence scoring for detection quality
- Maintains detection history for stability

### 3. **User Interface Enhancements**
- **Detection Status Indicators** - Visual feedback for each movement
- **Real-time Feedback** - Success, warning, and error messages
- **Button State Management** - Disabled until movements are detected
- **Progress Tracking** - Shows completion status for all movements

## Implementation Architecture

### Core Components

#### **MovementDetectionService**
```typescript
// Location: src/presentation/services/MovementDetectionService.ts
class MovementDetectionService {
  detectMovement(actionType: ExamActionType, landmarks: Landmark[]): MovementDetectionResult
  setBaseline(landmarks: Landmark[]): void
  areAllMovementsDetected(): boolean
  getDetectionStatus(): Map<ExamActionType, boolean>
}
```

#### **DetectionStatusView**
```typescript
// Location: src/presentation/components/DetectionStatusView.ts
class DetectionStatusView {
  updateDetectionStatus(actionType: ExamActionType, result: MovementDetectionResult): void
  updateCurrentAction(actionType: ExamActionType, instruction: string): void
  showCompletionStatus(allDetected: boolean): void
}
```

### Validation Criteria

#### **Baseline (Neutral Face)**
- **Minimum Landmarks**: 468/478
- **Movement Intensity**: 0 (no movement required)
- **Required Landmarks**: Face center points [10, 151, 9, 175]

#### **Eyebrow Raise**
- **Minimum Landmarks**: 468/478
- **Movement Intensity**: ≥ 0.02
- **Required Landmarks**: [70, 63, 105, 66, 107, 300, 293, 334, 296, 336]

#### **Eye Closure**
- **Minimum Landmarks**: 468/478
- **Movement Intensity**: ≥ 0.015
- **Required Landmarks**: [159, 145, 158, 153, 133, 173, 386, 374, 385, 380, 362, 398]

#### **Smile**
- **Minimum Landmarks**: 468/478
- **Movement Intensity**: ≥ 0.02
- **Required Landmarks**: [61, 291, 13, 14, 17, 18]

## User Experience Flow

### 1. **Examination Start**
- Detection status UI appears below camera views
- All movements show "pending" status
- Next/Finish buttons are disabled

### 2. **Movement Detection**
- Real-time feedback as movements are performed
- Status indicators update: pending → detecting → detected
- Audio/visual feedback for successful detection

### 3. **Progression Control**
- "Next Action" button enabled only when current movement detected
- "Finish Examination" button enabled only when all movements detected
- Clear error messages if attempting to proceed without detection

### 4. **Completion Validation**
- Final check ensures all movements captured
- Prevents incomplete examinations
- Maintains clinical data integrity

## Technical Implementation

### Integration Points

#### **ExamController Updates**
```typescript
// Movement detection in camera results handler
const detectionResult = this.movementDetectionService.detectMovement(actionType, landmarks);
this.detectionStatusView.updateDetectionStatus(actionType, detectionResult);

// Validation in nextAction()
if (!detectionStatus.get(actionType)) {
  this.detectionStatusView.showError('Please complete the current movement before proceeding.');
  return;
}

// Validation in finishExam()
const allDetected = this.movementDetectionService.areAllMovementsDetected();
if (!allDetected) {
  this.detectionStatusView.showError('Please complete all facial movements before finishing.');
  return;
}
```

#### **Button State Management**
```typescript
private updateButtonStatesWithDetection(): void {
  const isCurrentDetected = detectionStatus.get(actionType) || false;
  
  if (nextBtn) {
    nextBtn.disabled = !isCurrentDetected;
    nextBtn.style.opacity = isCurrentDetected ? '1' : '0.5';
  }
  
  if (finishBtn) {
    const allDetected = this.movementDetectionService.areAllMovementsDetected();
    finishBtn.disabled = !allDetected;
  }
}
```

### Detection Algorithm

#### **Movement Intensity Calculation**
```typescript
private calculateMovementIntensity(
  currentLandmarks: Landmark[],
  baselineLandmarks: Landmark[],
  requiredLandmarks: number[]
): number {
  let totalMovement = 0;
  let validComparisons = 0;

  for (const landmarkIndex of requiredLandmarks) {
    const current = currentLandmarks[landmarkIndex];
    const baseline = baselineLandmarks[landmarkIndex];
    
    const distance = Math.sqrt(
      Math.pow(current.x - baseline.x, 2) + 
      Math.pow(current.y - baseline.y, 2)
    );
    totalMovement += distance;
    validComparisons++;
  }

  return validComparisons > 0 ? totalMovement / validComparisons : 0;
}
```

#### **Stability Detection**
- Maintains history of last 5 detection results
- Requires 60% detection rate for stability
- Average confidence ≥ 0.7 for reliable detection

## Clinical Benefits

### 1. **Data Quality Assurance**
- Ensures complete landmark data for all required movements
- Validates movement intensity meets clinical thresholds
- Prevents incomplete examinations that could affect diagnosis

### 2. **House-Brackmann Grading Accuracy**
- All core movements required for proper grading
- Baseline comparison enables accurate asymmetry calculation
- Consistent data quality across examinations

### 3. **User Guidance**
- Clear feedback helps users perform movements correctly
- Reduces examination retakes due to incomplete data
- Improves overall examination success rate

## Configuration

### Adjustable Thresholds
```typescript
// Movement intensity thresholds can be adjusted per action type
const criteria = {
  eyebrowRaise: { minMovementIntensity: 0.02 },
  eyeClose: { minMovementIntensity: 0.015 },
  smile: { minMovementIntensity: 0.02 }
};
```

### Detection Sensitivity
```typescript
// Stability requirements for consistent detection
const HISTORY_SIZE = 5; // Number of recent results to consider
const DETECTION_RATE_THRESHOLD = 0.6; // 60% detection rate required
const CONFIDENCE_THRESHOLD = 0.7; // Minimum average confidence
```

## Future Enhancements

### Potential Improvements
1. **Machine Learning Integration** - Train models for better movement classification
2. **Adaptive Thresholds** - Adjust sensitivity based on patient demographics
3. **Quality Scoring** - Provide detailed quality metrics for each movement
4. **Automated Coaching** - Real-time guidance for optimal movement performance

### Clinical Extensions
1. **Movement Timing** - Track duration and consistency of movements
2. **Bilateral Analysis** - Separate validation for left/right side movements
3. **Severity Assessment** - Integrate movement quality into clinical scoring
4. **Progress Tracking** - Monitor improvement over multiple examinations

## Troubleshooting

### Common Issues
1. **False Negatives** - Adjust movement intensity thresholds if valid movements aren't detected
2. **False Positives** - Increase stability requirements if random movements trigger detection
3. **UI Responsiveness** - Ensure detection status updates in real-time during examination

### Debug Information
- Console logs show detailed detection results
- Landmark validation errors are clearly reported
- Movement intensity calculations are logged for analysis
