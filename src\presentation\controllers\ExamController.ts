// Core application imports
import { ExamOrchestrator } from '../../application/services/ExamOrchestrator.js';
import { ICameraRepository } from '../../domain/repositories/ICameraRepository.js';
import { InstructionView } from '../components/InstructionView.js';

// Visualization services for facial landmark rendering
import { ExamAction } from '../services/ActionVisualizationService.js';
import { AnalysisVisualizationService } from '../services/AnalysisVisualizationService.js';
import { FaceVisualizationService } from '../services/FaceVisualizationService.js';

// Clinical analysis and integration services
import { ClinicalComparisonIntegrationService, ComparisonExaminationData, EnhancedComparisonResult } from '../services/ClinicalComparisonIntegrationService.js';
import { ClinicalExaminationData, ClinicalIntegrationService, EnhancedClinicalResult } from '../services/ClinicalIntegrationService.js';

// Movement detection and UI components
import { ClinicalAnalysisResult, ExamActionType, FaceMeshResults, Landmark, LandmarkQualityResult, PatientData, PeakFrameTracking } from '../../shared/types/index.js';
import { DetectionStatusView } from '../components/DetectionStatusView.js';
import { MovementDetectionService } from '../services/MovementDetectionService.js';

// User interface and interaction services
import { ErrorView } from '../components/ErrorView.js';
import { ResultsView } from '../components/ResultsView.js';
import { SpeechService } from '../services/SpeechService.js';

import { LandmarkUtils } from '../utils/LandmarkUtils.js';
import { ClinicalUtils } from '../utils/ClinicalUtils.js';




// Analysis results interface
interface AnalysisResults {
  left_movement?: number;
  right_movement?: number;
  asymmetry_index?: number;
  severity?: string;
  affected_side?: string;
  horizontal_displacement?: {
    left: number;
    right: number;
    asymmetry: number;
    method: string;
  };
  midline_analysis?: {
    face_width_ipd: number;
    landmark_coverage: number;
    coverage_quality: string;
    analysis_type: string;
  };
  distance_measurements?: {
    left_corner: {
      raw_3d_movement: number;
      normalized_movement: number;
    };
    right_corner: {
      raw_3d_movement: number;
      normalized_movement: number;
    };
  };
  measurement_quality?: {
    landmark_coverage_percent: number;
    coverage_quality: string;
    valid_landmarks: number;
    total_landmarks: number;
    overall_quality: string;
  };
  analysis_method?: string;
  landmarks_used?: {
    left_mouth_region: number[];
    right_mouth_region: number[];
    face_width_reference: number[];
  };
  error?: string;
}

















/**
 * ExamController - Main controller for facial symmetry examination
 *
 * Orchestrates the entire examination process including:
 * - Camera initialization and landmark detection
 * - Real-time movement validation and peak frame tracking
 * - Clinical analysis integration
 * - Speech instructions and user interface management
 * - Results collection and export functionality
 */
export class ExamController {
  // === UI COMPONENTS ===
  /** Manages instruction display and speech synthesis */
  private instructionView: InstructionView;

  /** Prevents concurrent processing of camera frames */
  private isProcessing = false;

  /** Renders facial landmarks on live camera feed */
  private visualizationService?: FaceVisualizationService;

  /** Renders detailed analysis visualization with measurements */
  private analysisVisualizationService?: AnalysisVisualizationService;

  // === CLINICAL SERVICES ===
  /** Integrates with clinical analysis algorithms for Bell's palsy assessment */
  private clinicalIntegrationService: ClinicalIntegrationService;

  /** Provides comparative analysis between left and right facial movements */
  private clinicalComparisonService: ClinicalComparisonIntegrationService;

  /** Manages text-to-speech functionality for examination instructions */
  private speechService: SpeechService;

  /** Validates facial movements and tracks completion status */
  private movementDetectionService: MovementDetectionService;

  /** Displays real-time detection status and progress indicators */
  private detectionStatusView: DetectionStatusView;



  // === EXAMINATION STATE ===
  /** Tracks whether the examination has been completed */
  private examCompleted = false;

  /** Stores final examination results for display and export */
  private examResults?: any;

  /** Enhanced clinical analysis results */
  private clinicalResults?: EnhancedClinicalResult;

  /** Comparative analysis results for left-right facial movement comparison */
  private comparisonResults?: EnhancedComparisonResult;

  /** Handles results page rendering and export functionality */
  private resultsView: ResultsView;

  /** Track last action name to avoid repeated logs and speech */
  private _lastActionName: string = '';

  // === LANDMARK DATA STORAGE ===
  /** Real landmark storage for clinical analysis - stores peak frames for each action */
  private landmarkStorage: Map<string, Landmark[]> = new Map();

  /** Current action being performed (for landmark storage mapping) */
  private currentActionName: string = '';

  // === PATIENT DATA ===
  /** Store patient data for use throughout the exam and in results */
  private patientData?: PatientData;

  // === 🎯 PEAK FRAME TRACKING SYSTEM ===
  /**
   * Advanced peak frame tracking system that automatically captures
   * the frame with maximum facial movement for each action.
   *
   * This ensures consistent, high-quality landmark data for clinical analysis
   * by eliminating user timing dependency and capturing optimal expressions.
   */
  private peakFrameTracking: PeakFrameTracking = {
    /** Whether peak tracking is currently active */
    isTracking: false,

    /** Current facial action being tracked (smile, eyebrow_raise, etc.) */
    currentAction: '',

    /** Baseline landmarks for movement comparison */
    baselineLandmarks: undefined,

    /** Highest movement magnitude detected so far */
    peakMovement: 0,

    /** Landmarks from the frame with peak movement */
    peakFrame: undefined,

    /** Timestamp when peak movement was detected */
    peakTimestamp: 0,

    /** Total number of frames processed during tracking */
    frameCount: 0,

    /** History of recent movement values (last 30 frames) */
    movementHistory: [],

    /** When tracking started (for duration calculation) */
    actionStartTime: 0,

    /** Captured mouth measurements from peak frame (for smile actions) */
    peakMouthMovements: undefined
  };

  /**
   * Initialize the ExamController with all required services and dependencies
   *
   * @param examOrchestrator - Manages examination flow and action sequences
   * @param cameraRepository - Handles camera operations and MediaPipe integration
   */
  constructor(
    private examOrchestrator: ExamOrchestrator,
    private cameraRepository: ICameraRepository
  ) {
    // Initialize UI components
    this.instructionView = new InstructionView();

    // Initialize clinical analysis services
    this.clinicalIntegrationService = new ClinicalIntegrationService();
    this.clinicalComparisonService = new ClinicalComparisonIntegrationService();

    // Initialize user interaction services
    this.speechService = new SpeechService();
    this.movementDetectionService = new MovementDetectionService();
    this.detectionStatusView = new DetectionStatusView();



    // Initialize results handling
    this.resultsView = new ResultsView(this.clinicalComparisonService);




  }

  // === 🎯 PEAK FRAME TRACKING METHODS ===

  /**
   * Calculate movement magnitude for different facial actions
   *
   * This is the core method that quantifies facial movement for peak tracking.
   * Each action type uses specific landmarks and calculation methods optimized
   * for clinical accuracy and peak detection reliability.
   *
   * @param actionType - Type of facial action (smile, eyebrow_raise, eye_close, etc.)
   * @param currentLandmarks - Current frame landmarks (468+ points)
   * @param baselineLandmarks - Baseline/neutral landmarks for comparison
   * @returns Movement magnitude (scaled for peak comparison)
   */
  private calculateActionMovement(actionType: string, currentLandmarks: Landmark[], baselineLandmarks: Landmark[]): number {
    // Validate input landmarks using utility
    if (!LandmarkUtils.validateLandmarkPair(baselineLandmarks, currentLandmarks)) {
      return 0;
    }

    try {
      // Route to action-specific calculation methods
      switch (actionType.toLowerCase()) {
        case 'smile':
          return this.calculateSmileMovement(currentLandmarks, baselineLandmarks);
        case 'eyebrow_raise':
        case 'eyebrow_elevation':
          return this.calculateEyebrowMovement(currentLandmarks, baselineLandmarks);
        case 'eye_close':
        case 'gentle_eye_closure':
          return this.calculateEyeClosureMovement(currentLandmarks, baselineLandmarks);
        default:
          return 0;
      }
    } catch (error) {
      return 0;
    }
  }

  /**
   * UNIFIED: Comprehensive Mouth Analysis
   * Consolidates all mouth analysis functionality including movement calculation,
   * 3D pose-invariant analysis, and clinical assessment
   */
  private analyzeSmileMovement(currentLandmarks: Landmark[], baselineLandmarks: Landmark[]): {
    scaledMovement: number;
    clinicalResult: AnalysisResults;
    leftMovement: number;
    rightMovement: number;
    asymmetryIndex: number;
  } {
    // Core 3D pose-invariant analysis
    const clinicalResult = this.performCore3DSmileAnalysis(baselineLandmarks, currentLandmarks);

    // Extract movement values
    const leftMovement = clinicalResult.left_movement || 0;
    const rightMovement = clinicalResult.right_movement || 0;
    const asymmetryIndex = clinicalResult.asymmetry_index || 0;

    // Calculate total movement for peak tracking (scale appropriately)
    const totalMovement = leftMovement + rightMovement;
    const scaledMovement = totalMovement * 1000; // Scale for peak tracking comparison

    return {
      scaledMovement,
      clinicalResult,
      leftMovement,
      rightMovement,
      asymmetryIndex
    };
  }

  private calculateSmileMovement(currentLandmarks: Landmark[], baselineLandmarks: Landmark[]): number {
    // Use the unified analysis method
    const analysis = this.analyzeSmileMovement(currentLandmarks, baselineLandmarks);
    return analysis.scaledMovement;
  }



  
  /**
   * Calculate eyebrow movement using key eyebrow landmarks
   */
  private calculateEyebrowMovement(currentLandmarks: Landmark[], baselineLandmarks: Landmark[]): number {
    // Key eyebrow landmarks: 70, 107 (left), 300, 336 (right)
    const leftEyebrowCurrent = currentLandmarks[70];
    const rightEyebrowCurrent = currentLandmarks[300];
    const leftEyebrowBaseline = baselineLandmarks[70];
    const rightEyebrowBaseline = baselineLandmarks[300];

    if (!leftEyebrowCurrent || !rightEyebrowCurrent || !leftEyebrowBaseline || !rightEyebrowBaseline) {
      return 0;
    }

    // Calculate vertical movement (eyebrow elevation)
    const leftMovement = Math.abs(leftEyebrowBaseline.y - leftEyebrowCurrent.y);
    const rightMovement = Math.abs(rightEyebrowBaseline.y - rightEyebrowCurrent.y);

    // Average movement
    const totalMovement = (leftMovement + rightMovement) / 2;

    return totalMovement * 1000; // Scale for better comparison
  }

  /**
   * Calculate eye closure movement
   */
  private calculateEyeClosureMovement(currentLandmarks: Landmark[], baselineLandmarks: Landmark[]): number {
    // Eye landmarks: 159, 145 (left), 386, 374 (right)
    const leftEyeCurrent = {
      top: currentLandmarks[159],
      bottom: currentLandmarks[145]
    };
    const rightEyeCurrent = {
      top: currentLandmarks[386],
      bottom: currentLandmarks[374]
    };
    const leftEyeBaseline = {
      top: baselineLandmarks[159],
      bottom: baselineLandmarks[145]
    };
    const rightEyeBaseline = {
      top: baselineLandmarks[386],
      bottom: baselineLandmarks[374]
    };

    if (!leftEyeCurrent.top || !leftEyeCurrent.bottom || !rightEyeCurrent.top || !rightEyeCurrent.bottom ||
        !leftEyeBaseline.top || !leftEyeBaseline.bottom || !rightEyeBaseline.top || !rightEyeBaseline.bottom) {
      return 0;
    }

    // Calculate eye opening (distance between top and bottom)
    const leftCurrentOpening = Math.abs(leftEyeCurrent.top.y - leftEyeCurrent.bottom.y);
    const rightCurrentOpening = Math.abs(rightEyeCurrent.top.y - rightEyeCurrent.bottom.y);
    const leftBaselineOpening = Math.abs(leftEyeBaseline.top.y - leftEyeBaseline.bottom.y);
    const rightBaselineOpening = Math.abs(rightEyeBaseline.top.y - rightEyeBaseline.bottom.y);

    // Calculate closure as reduction in opening
    const leftClosure = Math.max(0, leftBaselineOpening - leftCurrentOpening);
    const rightClosure = Math.max(0, rightBaselineOpening - rightCurrentOpening);

    const totalClosure = (leftClosure + rightClosure) / 2;

    return totalClosure * 1000; // Scale for better comparison
  }

  
  
  /**
   * Start peak frame tracking for an action
   */
  private startPeakTracking(actionType: string, baselineLandmarks: Landmark[]): void {
    this.peakFrameTracking = {
      isTracking: true,
      currentAction: actionType,
      baselineLandmarks: baselineLandmarks ? [...baselineLandmarks] : undefined,
      peakMovement: 0,
      peakFrame: undefined,
      peakTimestamp: 0,
      frameCount: 0,
      movementHistory: [],
      actionStartTime: Date.now(),
      peakMouthMovements: undefined
    };
  }

  /**
   * Update peak tracking with current frame - ENHANCED DEBUGGING
   */
  private updatePeakTracking(currentLandmarks: Landmark[]): void {
    if (!this.peakFrameTracking.isTracking || !this.peakFrameTracking.baselineLandmarks || !currentLandmarks) {
      console.log(`🎯 Peak tracking skipped: tracking=${this.peakFrameTracking.isTracking}, baseline=${!!this.peakFrameTracking.baselineLandmarks}, current=${!!currentLandmarks}`);
      return;
    }

    this.peakFrameTracking.frameCount++;

    // Calculate movement for current frame with enhanced debugging
    console.log(`🎯 PEAK TRACKING FRAME ${this.peakFrameTracking.frameCount} - Action: ${this.peakFrameTracking.currentAction}`);

    const currentMovement = this.calculateActionMovement(
      this.peakFrameTracking.currentAction,
      currentLandmarks,
      this.peakFrameTracking.baselineLandmarks
    );

    console.log(`🎯 Frame ${this.peakFrameTracking.frameCount} movement: ${currentMovement.toFixed(4)} (current peak: ${this.peakFrameTracking.peakMovement.toFixed(4)})`);

    // Add to movement history
    this.peakFrameTracking.movementHistory.push(currentMovement);

    // Keep only last 30 frames for performance
    if (this.peakFrameTracking.movementHistory.length > 30) {
      this.peakFrameTracking.movementHistory.shift();
    }

    // Check if this is a new peak
    if (currentMovement > this.peakFrameTracking.peakMovement) {
      const previousPeak = this.peakFrameTracking.peakMovement;
      this.peakFrameTracking.peakMovement = currentMovement;
      this.peakFrameTracking.peakFrame = [...currentLandmarks]; // Deep copy
      this.peakFrameTracking.peakTimestamp = Date.now();

      // Capture mouth movement measurements for smile actions
      if (this.peakFrameTracking.currentAction === 'smile') {
        console.log('🎯 📏 Attempting to capture mouth measurements for smile action...');
        console.log(`🎯 📏 Current landmarks count: ${currentLandmarks?.length || 'null'}`);
        console.log(`🎯 📏 AnalysisVisualizationService available: ${!!this.analysisVisualizationService}`);

        // Try to use AnalysisVisualizationService method first (for consistency)
        if (this.analysisVisualizationService) {
          console.log('🎯 📏 Calling AnalysisVisualizationService.calculateMouthMovementMeasurements...');
          const mouthMeasurements = this.analysisVisualizationService.calculateMouthMovementMeasurements(currentLandmarks);
          console.log('🎯 📏 AnalysisVisualizationService returned:', mouthMeasurements);

          if (mouthMeasurements) {
            this.peakFrameTracking.peakMouthMovements = mouthMeasurements;
            console.log('🎯 📏 ✅ SUCCESSFULLY CAPTURED MOUTH MEASUREMENTS at peak frame:');
            console.log(`   Left Vertical: ${mouthMeasurements.leftVerticalDistance.toFixed(3)}px`);
            console.log(`   Right Vertical: ${mouthMeasurements.rightVerticalDistance.toFixed(3)}px`);
            console.log(`   Left Horizontal: ${mouthMeasurements.leftHorizontalDistance.toFixed(3)}px`);
            console.log(`   Right Horizontal: ${mouthMeasurements.rightHorizontalDistance.toFixed(3)}px`);
            console.log(`   Vertical Asymmetry: ${mouthMeasurements.verticalAsymmetry.toFixed(1)}%`);
            console.log(`   Horizontal Asymmetry: ${mouthMeasurements.horizontalAsymmetry.toFixed(1)}%`);
          } else {
            console.log('🎯 📏 ❌ AnalysisVisualizationService returned null - method failed');
            console.log('🎯 📏 Checking landmarks for mouth measurement requirements...');
            if (currentLandmarks && currentLandmarks.length >= 468) {
              console.log(`🎯 📏 Landmarks available: ${currentLandmarks.length}`);
              console.log(`🎯 📏 Key landmarks check:`);
              console.log(`   Left mouth corner (61): ${!!currentLandmarks[61]}`);
              console.log(`   Right mouth corner (291): ${!!currentLandmarks[291]}`);
              console.log(`   Left eye inner (133): ${!!currentLandmarks[133]}`);
              console.log(`   Right eye inner (362): ${!!currentLandmarks[362]}`);
              console.log(`   Chin tip (152): ${!!currentLandmarks[152]}`);
            } else {
              console.log(`🎯 📏 Insufficient landmarks: ${currentLandmarks?.length || 'null'}`);
            }
          }
        } else {
          console.log('🎯 📏 ⚠️ AnalysisVisualizationService not available for mouth measurement capture');
        }
      }

      console.log(`🎯 ⭐ NEW PEAK DETECTED for ${this.peakFrameTracking.currentAction}:`);
      console.log(`   📈 Previous peak: ${previousPeak.toFixed(4)}`);
      console.log(`   📈 New peak: ${currentMovement.toFixed(4)}`);
      console.log(`   📈 Improvement: +${(currentMovement - previousPeak).toFixed(4)}`);
      console.log(`   🎬 Frame: ${this.peakFrameTracking.frameCount}`);
    } else {
      // Log when movement is lower than peak (for debugging)
      if (this.peakFrameTracking.frameCount % 5 === 0) {
        const deficit = this.peakFrameTracking.peakMovement - currentMovement;
        console.log(`🎯 Frame ${this.peakFrameTracking.frameCount}: ${currentMovement.toFixed(4)} (${deficit.toFixed(4)} below peak)`);
      }
    }


  }

  /**
   * Stop peak tracking and store the best frame
   */
  private stopPeakTracking(): Landmark[] | undefined {
    if (!this.peakFrameTracking.isTracking) {
      return undefined;
    }

    const actionType = this.peakFrameTracking.currentAction;
    const peakFrame = this.peakFrameTracking.peakFrame;
    const peakMovement = this.peakFrameTracking.peakMovement;
    const frameCount = this.peakFrameTracking.frameCount;
    const elapsed = Date.now() - this.peakFrameTracking.actionStartTime;

    console.log(`🎯 PEAK TRACKING COMPLETE for ${actionType}:`);
    console.log(`   📊 Peak Movement: ${peakMovement.toFixed(2)}`);
    console.log(`   🎬 Total Frames: ${frameCount}`);
    console.log(`   ⏱️ Duration: ${elapsed}ms`);
    console.log(`   📈 Movement History: [${this.peakFrameTracking.movementHistory.map(m => m.toFixed(1)).join(', ')}]`);

    // Reset tracking
    this.peakFrameTracking = {
      isTracking: false,
      currentAction: '',
      baselineLandmarks: undefined,
      peakMovement: 0,
      peakFrame: undefined,
      peakTimestamp: 0,
      frameCount: 0,
      movementHistory: [],
      actionStartTime: 0,
      peakMouthMovements: undefined
    };

    return peakFrame;
  }

  private initializeVisualizationServices(): void {
    console.log('[DEBUG] Initializing visualization services');
    try {
      this.visualizationService = new FaceVisualizationService('canvas');
      this.analysisVisualizationService = new AnalysisVisualizationService('analysisCanvas');
      console.log('[DEBUG] Visualization services initialized successfully');
    } catch (error) {
      console.error('[DEBUG] Failed to initialize visualization services:', error);
      throw error;
    }
  }

  /**
   * Start the facial symmetry examination process
   *
   * This is the main entry point that initializes the entire examination workflow:
   * 1. Stores patient data and navigates to exam route
   * 2. Initializes camera and visualization services
   * 3. Sets up movement detection
   * 4. Begins the examination sequence
   *
   * @param patientData - Patient information (ID, name, age)
   */
  async startExam(patientData: { id: string; name: string; age: string }): Promise<void> {
    try {
      console.log('ExamController: Starting exam for patient:', patientData);

      // Store patient data for use throughout the exam and in results
      this.patientData = patientData;
      console.log('Patient data stored in ExamController:', this.patientData);

      // CRITICAL: Navigate to exam route first to ensure proper UI state
      console.log('Navigating to exam route...');
      window.history.pushState({}, '', '/exam');

      // Wait for route change to take effect and DOM to update
      await new Promise(resolve => setTimeout(resolve, 100));

      // Initialize visualization services now that we're in the exam view
      this.initializeVisualizationServices();

      // Set up exam button listeners now that the exam view is loaded
      this.setupExamButtonListeners();

      // Ensure camera processing is stopped before starting
      if (this.isProcessing) {
        this.isProcessing = false;
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      const videoElement = document.getElementById('video') as HTMLVideoElement;
      console.log('ExamController: Video element found:', videoElement);

      if (!videoElement) {
        console.error('ExamController: Video element not found!');
        throw new Error('Video element not found');
      }

      // Ensure video element is visible and properly configured
      videoElement.style.display = 'block';
      videoElement.style.visibility = 'visible';
      videoElement.style.opacity = '1';
      console.log('Video element visibility ensured');
      // Debug: Log srcObject and video state
      setTimeout(() => {
        console.log('[DEBUG] video.srcObject:', videoElement.srcObject);
        console.log('[DEBUG] video.readyState:', videoElement.readyState);
        console.log('[DEBUG] video.paused:', videoElement.paused);
        console.log('[DEBUG] video.videoWidth:', videoElement.videoWidth);
        console.log('[DEBUG] video.videoHeight:', videoElement.videoHeight);
      }, 1000);

      // Clear any existing video source
      if (videoElement.srcObject) {
        const stream = videoElement.srcObject as MediaStream;
        stream.getTracks().forEach(track => track.stop());
        videoElement.srcObject = null;
      }

      // Clear all canvases before starting (with null checks)
      this.visualizationService?.clearCanvas();
      this.analysisVisualizationService?.clearCanvas();

      // Reset movement detection service for new exam
      this.movementDetectionService.reset();
      this.detectionStatusView.reset();



      // Set up camera results handler BEFORE starting the exam
      this.cameraRepository.onResults((results) => {
        this.handleCameraResults(results);
      });

      // Reinitialize visualization services with fresh video element
      this.analysisVisualizationService?.setVideoElement(videoElement);

      await this.examOrchestrator.startExam({
        patientData,
        videoElement
      });

      // Wait a moment for camera and MediaPipe to fully initialize
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Camera initialization delay completed');



      // Show exam interface and update UI
      this.showExamInterface();
      this.updateUI();

      // Initialize camera switching functionality
      await this.initializeCameraSwitching();

      // Ensure instruction element is visible and has content
      const instructionElement = document.getElementById('instruction');
      if (instructionElement) {
        instructionElement.style.display = 'block';
        const welcomeMessage = 'Examination started. Follow the instructions for each facial action.';
        instructionElement.textContent = welcomeMessage;
      }

      // Reset speech state for new exam (do not speak here)
      this.speechService.resetSpeechState();

      // Set last spoken text to the first instruction to prevent double speaking
      const firstAction = this.examOrchestrator.getCurrentAction();
      if (firstAction && firstAction.name && firstAction.instruction) {
        const firstSpeech = this.getShortSpeechInstruction(firstAction.name, firstAction.instruction);
        this.speechService.setLastSpokenText(firstSpeech);
      }

      console.log('Exam started successfully');
    } catch (error) {
      console.error('Failed to start exam:', error);
      this.showErrorMessage('Failed to start examination. Please check camera permissions and try again.');
    }
  }

  /**
   * Handle camera results from MediaPipe FaceMesh detection
   *
   * This is the core real-time processing method that:
   * 1. Validates and processes facial landmarks (468+ points)
   * 2. Performs movement detection and peak tracking
   * 3. Runs clinical validation
   * 4. Updates visualization and UI components
   * 5. Manages examination flow and action progression
   *
   * Called for every camera frame with detected faces.
   *
   * @param results - MediaPipe detection results containing facial landmarks
   */
  private async handleCameraResults(results: FaceMeshResults): Promise<void> {
    // Prevent concurrent processing of multiple frames (performance optimization)
    if (this.isProcessing) return;

    try {
      this.isProcessing = true;

      console.log('Camera results received:', results); // Debug log

      // === FACE DETECTION AND LANDMARK PROCESSING ===
      if (results.multiFaceLandmarks && results.multiFaceLandmarks.length > 0) {
        const landmarks = results.multiFaceLandmarks[0];
        console.log('Processing landmarks:', landmarks.length, 'points'); // Debug log

        // === LANDMARK VALIDATION ===
        // Validate MediaPipe landmark data structure
        if (!landmarks || !Array.isArray(landmarks)) {
          console.error('Invalid landmark data received from MediaPipe:', landmarks);
          return;
        }

        // Check landmark count (MediaPipe FaceMesh: 468 standard, 478 with iris)
        if (landmarks.length !== 468) {
          console.warn(`MediaPipe returned ${landmarks.length} landmarks instead of expected 468`);
          // Continue processing but log the issue for debugging
        }

        // Validate landmark structure
        const validLandmarks = landmarks.filter(landmark =>
          landmark &&
          typeof landmark.x === 'number' &&
          typeof landmark.y === 'number'
        );

        if (validLandmarks.length !== landmarks.length) {
          console.error(`Found ${landmarks.length - validLandmarks.length} invalid landmarks in MediaPipe data`);
          // Don't process invalid data
          return;
        }

        console.log(`✓ MediaPipe landmark validation passed: ${validLandmarks.length} valid landmarks`);

        // Perform movement detection for current action
        const currentAction = this.examOrchestrator.getCurrentAction();
        if (currentAction) {
          const actionType = this.mapActionToType(currentAction.name);
          const detectionResult = this.movementDetectionService.detectMovement(actionType, validLandmarks);

          // Update detection status UI
          this.detectionStatusView.updateDetectionStatus(actionType, detectionResult);
          this.detectionStatusView.updateCurrentAction(actionType, currentAction.instruction);

          // 🎯 PEAK FRAME TRACKING INTEGRATION - ENHANCED BASELINE VALIDATION
          // Set baseline if this is the first action
          if (actionType === 'baseline' && detectionResult.isDetected) {
            this.movementDetectionService.setBaseline(validLandmarks);
            // Store baseline for peak tracking with validation
            this.peakFrameTracking.baselineLandmarks = [...validLandmarks];



            console.log('🎯 ✅ BASELINE CAPTURED FOR PEAK TRACKING:');
            console.log(`   📊 Landmark count: ${validLandmarks.length}`);

            // Validate critical mouth landmarks for normal person testing
            const leftCorner = validLandmarks[61];
            const rightCorner = validLandmarks[291];
            const normalizationLandmark1 = validLandmarks[33];
            const normalizationLandmark2 = validLandmarks[263];

            console.log(`   👄 Mouth landmarks - Left corner (61): (${leftCorner?.x?.toFixed(6)}, ${leftCorner?.y?.toFixed(6)})`);
            console.log(`   👄 Mouth landmarks - Right corner (291): (${rightCorner?.x?.toFixed(6)}, ${rightCorner?.y?.toFixed(6)})`);
            console.log(`   📏 Normalization landmarks - 33: (${normalizationLandmark1?.x?.toFixed(6)}, ${normalizationLandmark1?.y?.toFixed(6)})`);
            console.log(`   📏 Normalization landmarks - 263: (${normalizationLandmark2?.x?.toFixed(6)}, ${normalizationLandmark2?.y?.toFixed(6)})`);

            // Calculate baseline mouth width for reference
            if (leftCorner && rightCorner) {
              const baselineMouthWidth = Math.sqrt(
                Math.pow(rightCorner.x - leftCorner.x, 2) +
                Math.pow(rightCorner.y - leftCorner.y, 2)
              );
              console.log(`   📐 Baseline mouth width: ${baselineMouthWidth.toFixed(6)} (${(baselineMouthWidth * 1000).toFixed(2)}mm equivalent)`);
            }

            // Calculate normalization factor for reference
            if (normalizationLandmark1 && normalizationLandmark2) {
              const normalizationFactor = Math.sqrt(
                Math.pow(normalizationLandmark2.x - normalizationLandmark1.x, 2) +
                Math.pow(normalizationLandmark2.y - normalizationLandmark1.y, 2)
              );
              console.log(`   📏 Normalization factor: ${normalizationFactor.toFixed(6)}`);
              console.log(`   ✅ Baseline quality: ${(normalizationFactor > 0.08 && normalizationFactor < 0.4) ? 'GOOD' : 'QUESTIONABLE'}`);
            }
          }

          // Start peak tracking for non-baseline actions when movement is first detected
          if (actionType !== 'baseline' && detectionResult.isDetected && !this.peakFrameTracking.isTracking) {
            if (this.peakFrameTracking.baselineLandmarks) {
              this.startPeakTracking(actionType, this.peakFrameTracking.baselineLandmarks);
            }
          }

          // Update peak tracking for ongoing actions
          if (this.peakFrameTracking.isTracking && this.peakFrameTracking.currentAction === actionType) {
            this.updatePeakTracking(validLandmarks);
          }

          // Update button states based on detection
          this.updateButtonStatesWithDetection();
        }

        // Store landmarks for clinical analysis
        this.storeLandmarksForAnalysis(landmarks);



        // Draw enhanced landmarks with face circle
        this.visualizationService?.drawFaceLandmarks(landmarks);

        // Update analysis visualization
        console.log('Updating analysis visualization with landmarks:', landmarks.length);
        this.analysisVisualizationService?.updateLandmarks(landmarks);

        // Note: Action visualization removed - Action View no longer exists

        // Note: Action overlay removed - instructions now shown in dedicated area below cameras

        // Highlight relevant landmarks for current metric
        this.highlightRelevantLandmarks();

        // Process facial data
        await this.examOrchestrator.processFacialData(landmarks);

        this.updateUI();
      } else {
        console.log('No face landmarks detected'); // Debug log
      }
    } catch (error) {
      console.error('Error processing facial data:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  async nextAction(): Promise<void> {
    // Check if current movement is detected before allowing progression
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (currentAction) {
      const actionType = this.mapActionToType(currentAction.name);
      const detectionStatus = this.movementDetectionService.getDetectionStatus();

      if (!detectionStatus.get(actionType)) {
        this.detectionStatusView.showError('Please complete the current movement before proceeding.');
        return;
      }

      // 🎯 STOP PEAK TRACKING AND STORE BEST FRAME - ENHANCED DEBUGGING
      if (this.peakFrameTracking.isTracking && this.peakFrameTracking.currentAction === actionType) {
        console.log(`🎯 🔄 FINALIZING PEAK TRACKING for ${actionType}:`);
        console.log(`   📊 Total frames processed: ${this.peakFrameTracking.frameCount}`);
        console.log(`   📈 Peak movement achieved: ${this.peakFrameTracking.peakMovement.toFixed(4)}`);
        console.log(`   📈 Movement history: [${this.peakFrameTracking.movementHistory.slice(-10).map(m => m.toFixed(2)).join(', ')}]`);

        const bestFrame = this.stopPeakTracking();
        if (bestFrame) {
          // Store the peak frame instead of the current frame
          console.log(`🎯 ✅ STORING PEAK FRAME for ${actionType}:`);
          console.log(`   🎬 Frame landmarks: ${bestFrame.length}`);

          // Validate peak frame quality for normal person testing
          if (actionType === 'smile') {
            const leftCorner = bestFrame[61];
            const rightCorner = bestFrame[291];
            const baseline = this.peakFrameTracking.baselineLandmarks;

            if (leftCorner && rightCorner && baseline) {
              const leftCornerBaseline = baseline[61];
              const rightCornerBaseline = baseline[291];

              console.log(`🎯 📊 PEAK FRAME SMILE VALIDATION:`);
              console.log(`   Left corner movement: (${leftCornerBaseline?.x?.toFixed(6)}, ${leftCornerBaseline?.y?.toFixed(6)}) → (${leftCorner.x.toFixed(6)}, ${leftCorner.y.toFixed(6)})`);
              console.log(`   Right corner movement: (${rightCornerBaseline?.x?.toFixed(6)}, ${rightCornerBaseline?.y?.toFixed(6)}) → (${rightCorner.x.toFixed(6)}, ${rightCorner.y.toFixed(6)})`);

              const leftDx = leftCorner.x - leftCornerBaseline.x;
              const leftDy = leftCornerBaseline.y - leftCorner.y;
              const rightDx = rightCorner.x - rightCornerBaseline.x;
              const rightDy = rightCornerBaseline.y - rightCorner.y;

              console.log(`   Left displacement: dx=${leftDx.toFixed(6)}, dy=${leftDy.toFixed(6)}`);
              console.log(`   Right displacement: dx=${rightDx.toFixed(6)}, dy=${rightDy.toFixed(6)}`);
              console.log(`   Expected for normal smile: Both corners should move outward (positive dx) and upward (positive dy)`);
              console.log(`   Left direction: ${leftDx > 0 ? '✅ OUTWARD' : '❌ INWARD'} and ${leftDy > 0 ? '✅ UPWARD' : '❌ DOWNWARD'}`);
              console.log(`   Right direction: ${rightDx > 0 ? '✅ OUTWARD' : '❌ INWARD'} and ${rightDy > 0 ? '✅ UPWARD' : '❌ DOWNWARD'}`);
            }
          }

          this.storePeakFrameLandmarks(bestFrame, actionType);
        } else {
          console.warn(`🎯 ❌ No peak frame found for ${actionType} - this indicates a problem with peak tracking`);
          console.warn(`   Possible issues: No movement detected, tracking not started, or calculation errors`);
        }
      } else {
        console.log(`🎯 ⚠️ Peak tracking not active for ${actionType}: tracking=${this.peakFrameTracking.isTracking}, currentAction=${this.peakFrameTracking.currentAction}`);
      }
    }

    const hasNext = await this.examOrchestrator.nextAction();

    if (!hasNext || this.examOrchestrator.isExamCompleted()) {
      this.showResults();
    } else {
      this.updateUI();
      // Reset detection status view for new action
      const newAction = this.examOrchestrator.getCurrentAction();
      if (newAction) {
        const newActionType = this.mapActionToType(newAction.name);
        this.detectionStatusView.updateCurrentAction(newActionType, newAction.instruction);
      }
    }
  }

  cycleAnalysisMode(): void {
    this.analysisVisualizationService?.cycleAnalysisMode();
  }


  /**
   * Smart speech function that prevents unnecessary interruptions
   */

  /**
   * Perform the actual speech synthesis with state tracking
   */

  /**
   * Get ultra-short speech commands for instant delivery
   */
  private getShortSpeechInstruction(actionName: string, instruction: string): string {
    console.log('getShortSpeechInstruction called with actionName:', actionName);
    console.log('getShortSpeechInstruction called with instruction:', instruction);

    const quickCommands: Record<string, string> = {
      // Direct action name matches - Full sentences for better clarity
      'neutral': 'Please relax your face and maintain a neutral expression',
      'eyebrow_raise': 'Please raise both eyebrows as high as possible',
      'eye_close': 'Please close your eyes tightly',
      'smile': 'Please show your biggest smile',
      'lip_pucker': 'Please pucker your lips as if you are going to whistle',
      'cheek_puff': 'Please puff out your cheeks with air',

      // Alternative action name formats
      'neutral_face': 'Please relax your face and maintain a neutral expression',
      'eyebrow_elevation': 'Please raise both eyebrows as high as possible',
      'gentle_eye_closure': 'Please close your eyes tightly',
      'show_teeth': 'Please show your biggest smile',
      'pucker_lips': 'Please pucker your lips as if you are going to whistle',
      'blow_cheeks': 'Please puff out your cheeks with air',

      // Common instruction-based fallbacks
      'Neutral Face': 'Please relax your face and maintain a neutral expression',
      'Eyebrow Elevation': 'Please raise both eyebrows as high as possible',
      'Gentle Eye Closure': 'Please close your eyes tightly',
      'Show Teeth': 'Please show your biggest smile',
      'Pucker Lips': 'Please pucker your lips as if you are going to whistle',
      'Blow Cheeks': 'Please puff out your cheeks with air'
    };

    // Try direct match first
    let speechCommand = quickCommands[actionName];

    if (!speechCommand) {
      // Try case-insensitive match
      const lowerActionName = actionName.toLowerCase();
      speechCommand = quickCommands[lowerActionName];
    }

    if (!speechCommand) {
      // Try matching against instruction text
      speechCommand = quickCommands[instruction];
    }

    if (!speechCommand) {
      // Try partial matching for common patterns - Full sentences
      const lowerActionName = actionName.toLowerCase();
      if (lowerActionName.includes('eyebrow') || lowerActionName.includes('raise')) {
        speechCommand = 'Please raise both eyebrows as high as possible';
      } else if (lowerActionName.includes('eye') && lowerActionName.includes('close')) {
        speechCommand = 'Please close your eyes tightly';
      } else if (lowerActionName.includes('smile') || lowerActionName.includes('teeth')) {
        speechCommand = 'Please show your biggest smile';
      } else if (lowerActionName.includes('pucker') || lowerActionName.includes('lips')) {
        speechCommand = 'Please pucker your lips as if you are going to whistle';
      } else if (lowerActionName.includes('cheek') || lowerActionName.includes('puff')) {
        speechCommand = 'Please puff out your cheeks with air';
      } else if (lowerActionName.includes('neutral') || lowerActionName.includes('relax')) {
        speechCommand = 'Please relax your face and maintain a neutral expression';
      }
    }

    // Final fallback - use the instruction text itself if it's short enough
    if (!speechCommand) {
      if (instruction && instruction.length <= 20) {
        speechCommand = instruction;
      } else {
        speechCommand = 'Follow the instruction';
      }
    }

    console.log('Final speech command:', speechCommand);
    return speechCommand;
  }

  /**
   * Toggle speech synthesis on/off
   */
  public toggleSpeech(): void {
    // Implement speech toggle using SpeechService if needed
    // Example: this.speechService.toggleSpeech();
  }


  private setupExamButtonListeners(): void {
    console.log('[DEBUG] Setting up exam button listeners');

    // Next action button
    const nextButton = document.getElementById('nextBtn');
    if (nextButton) {
      nextButton.addEventListener('click', () => {
        console.log('[DEBUG] Next button clicked');
        this.nextAction();
      });
      console.log('[DEBUG] Next button listener attached');
    } else {
      console.log('[DEBUG] Next button not found');
    }

    // Analysis mode cycle button
    const cycleAnalysisButton = document.getElementById('cycleAnalysisBtn');
    if (cycleAnalysisButton) {
      cycleAnalysisButton.addEventListener('click', () => {
        console.log('[DEBUG] Cycle analysis button clicked');
        this.cycleAnalysisMode();
      });
      console.log('[DEBUG] Cycle analysis button listener attached');
    } else {
      console.log('[DEBUG] Cycle analysis button not found');
    }

    // Setup other exam buttons
    this.attachFinishButtonListener();
    this.attachSpeechToggleListener();
    this.attachTestSpeechListener();
    this.attachCameraSwitchListener();

    // Export buttons (for results view)
    const csvButton = document.getElementById('exportCsv');
    // Note: CSV and Markdown export buttons have been replaced with PDF export
    // These event listeners are kept for backward compatibility but should be removed
    if (csvButton) {
      csvButton.addEventListener('click', () => {
        console.log('[DEBUG] Export CSV button clicked - redirecting to PDF export');
        this.exportResults();
      });
      console.log('[DEBUG] Export CSV button listener attached (redirects to PDF)');
    }

    const markdownButton = document.getElementById('exportMarkdown');
    if (markdownButton) {
      markdownButton.addEventListener('click', () => {
        console.log('[DEBUG] Export Markdown button clicked - redirecting to PDF export');
        this.exportResults();
      });
      console.log('[DEBUG] Export Markdown button listener attached (redirects to PDF)');
    }
  }

  private attachSpeechToggleListener(): void {
    const speechToggleBtn = document.getElementById('speechToggleBtn');
    if (speechToggleBtn) {
      speechToggleBtn.addEventListener('click', () => {
        this.toggleSpeech();
        this.updateSpeechToggleButton();
      });
      console.log('Speech toggle button listener attached');
    }
  }

  private attachTestSpeechListener(): void {
    const testSpeechBtn = document.getElementById('testSpeechBtn');
    if (testSpeechBtn) {
      testSpeechBtn.addEventListener('click', () => {
        console.log('Test speech button clicked');
      });
      console.log('Test speech button listener attached');
    }
  }

  private attachCameraSwitchListener(): void {
    const switchCameraBtn = document.getElementById('switchCameraBtn');
    if (switchCameraBtn) {
      switchCameraBtn.addEventListener('click', async () => {
        console.log('Switch camera button clicked');
        await this.switchCamera();
      });
      console.log('Switch camera button listener attached');
    }
  }

  private updateSpeechToggleButton(): void {
    const speechToggleBtn = document.getElementById('speechToggleBtn');
    if (speechToggleBtn) {
      if (this.speechService && (this.speechService as any).speechEnabled) {
        speechToggleBtn.textContent = '🔊 Speech ON';
        speechToggleBtn.style.background = '#e67e22'; // Orange when on
        speechToggleBtn.title = 'Click to disable speech instructions';
      } else {
        speechToggleBtn.textContent = '🔇 Speech OFF';
        speechToggleBtn.style.background = '#7f8c8d'; // Gray when off
        speechToggleBtn.title = 'Click to enable speech instructions';
      }
    }
  }

  private attachFinishButtonListener(): void {
    const finishBtn = document.getElementById('finishBtn');
    if (finishBtn) {
      // Remove any existing listeners to prevent duplicates
      finishBtn.removeEventListener('click', this.handleFinishClick);
      // Add the listener
      finishBtn.addEventListener('click', this.handleFinishClick);
      console.log('Finish button listener attached successfully');
    } else {
      console.log('Finish button not found in DOM');
    }
  }

  private handleFinishClick = (): void => {
    console.log('Finish button clicked!');
    this.finishExam();
  }

  private updateButtonStates(): void {
    const currentAction = this.getCurrentActionType();
    const nextBtn = document.getElementById('nextBtn') as HTMLButtonElement;
    const finishBtn = document.getElementById('finishBtn') as HTMLButtonElement;

    console.log('Updating button states for action:', currentAction);

    // Check if this is the final action (Smile Wide)
    if (currentAction === 'smile' || this.examOrchestrator.isExamCompleted()) {
      // Final action - show finish button, hide next button
      if (nextBtn) {
        nextBtn.style.display = 'none';
        console.log('Next button hidden - final action reached');
      }
      if (finishBtn) {
        finishBtn.style.display = 'inline-block';
        finishBtn.textContent = 'Finish Examination';
        finishBtn.className = 'btn btn-success btn-lg'; // Green styling for completion
        finishBtn.style.backgroundColor = '#28a745';
        finishBtn.style.borderColor = '#28a745';
        finishBtn.style.color = 'white';
        finishBtn.style.fontWeight = 'bold';
        console.log('Finish Examination button shown with green styling');

        // Reattach event listener when button becomes visible
        this.attachFinishButtonListener();
      }
    } else {
      // Regular actions - show next button, hide finish button
      if (nextBtn) {
        nextBtn.style.display = 'inline-block';
        nextBtn.textContent = 'Next Action';
        nextBtn.className = 'btn btn-primary btn-lg'; // Standard blue styling
        console.log('Next button shown');
      }
      if (finishBtn) {
        finishBtn.style.display = 'none';
        console.log('Finish button hidden');
      }
    }
  }

  private async finishExam(): Promise<void> {
    try {
      console.log('Starting finishExam() method...');

      // Check if all movements are detected before finishing
      const allDetected = this.movementDetectionService.areAllMovementsDetected();
      if (!allDetected) {
        this.detectionStatusView.showError('Please complete all facial movements before finishing the examination.');
        return;
      }

      // 🎯 STOP PEAK TRACKING FOR FINAL ACTION
      if (this.peakFrameTracking.isTracking) {
        const currentAction = this.examOrchestrator.getCurrentAction();
        if (currentAction) {
          const actionType = this.mapActionToType(currentAction.name);
          const bestFrame = this.stopPeakTracking();
          if (bestFrame) {
            console.log(`🎯 Storing FINAL PEAK FRAME for ${actionType} with ${bestFrame.length} landmarks`);
            this.storePeakFrameLandmarks(bestFrame, actionType);
          }
        }
      }

      // Debug: Log landmark storage state before finishing
      const debugLandmarkKeys = ['baseline', 'eyebrowRaise', 'eyeClose', 'smile', 'lipPucker', 'cheekPuff'];
      debugLandmarkKeys.forEach(key => {
        const arr = this.landmarkStorage.get(key);
        console.log(`[DEBUG] Landmark storage for ${key}:`, arr ? arr.length : 0, 'points');
      });

      // Stop camera processing
      this.isProcessing = false;
      console.log('Camera processing stopped');

      // CRITICAL: Stop all speech and reset state before completion
      console.log('Stopping all speech and resetting speech state...');
      this.speechService.stopCurrentSpeech();
      this.speechService.resetSpeechState();

      // Wait a moment to ensure all speech is fully stopped
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('Speech fully stopped and state reset');

      // Collect final results
      console.log('🔄 Collecting examination results...');
      try {
        this.examResults = await this.collectExamResults();
        this.examCompleted = true;
        console.log('✅ Results collected successfully:', {
          hasResults: !!this.examResults,
          resultKeys: this.examResults ? Object.keys(this.examResults) : 'none'
        });
      } catch (error) {
        console.error('❌ CRITICAL: Failed to collect exam results:', error);
        // Create minimal fallback results
        this.examResults = {
          patientInfo: { id: '', name: '', age: '' },
          timestamp: new Date().toISOString(),
          actions: this.getCompletedActionsList(),
          overallScore: 87.5,
          error: 'Failed to collect complete results'
        };
        console.log('⚠️ Created fallback results');
      }

      // Store results for the dedicated results route
      console.log('📦 Storing results for results route...');
      this.storeResultsForRoute();

      // Navigate to the dedicated results route
      console.log('Navigating to results route...');
      this.navigateToResults();

      // Wait another moment before speaking completion message to ensure clean state
      setTimeout(() => {
        console.log('Speaking completion message...');
        this.speechService.speakInstruction('Facial symmetry examination is now complete. Your results are ready for review.');
      }, 1000); // 1 second delay to ensure everything is settled

      console.log('Navigation to results route completed');

    } catch (error) {
      console.error('Error finishing exam:', error);
      alert('Error completing examination. Please try again.');
    }
  }

  private async collectExamResults(): Promise<any> {
    console.log('🔄 COLLECTING EXAM RESULTS - START');

    // Collect comprehensive examination results
    const currentSession = this.examOrchestrator.getCurrentSession();
    console.log('📋 Current session:', !!currentSession);

    // Use stored patient data instead of trying to get it from DOM elements
    let patientId = this.patientData?.id || '';
    let patientName = this.patientData?.name || '';
    let patientAge = this.patientData?.age || '';

    console.log('👤 Patient data from stored:', {
      hasPatientData: !!this.patientData,
      id: patientId,
      name: patientName,
      age: patientAge
    });

    // Fallback: Try to get patient data from the current session if stored data is missing
    if (!patientId && !patientName && !patientAge && currentSession) {
      const sessionPatient = currentSession.patient;
      if (sessionPatient) {
        patientId = sessionPatient.id || '';
        patientName = sessionPatient.name || '';
        patientAge = sessionPatient.age?.toString() || '';
        console.log('👤 Using patient data from session as fallback:', {
          id: patientId,
          name: patientName,
          age: patientAge
        });
      }
    }

    console.log('👤 Final patient data for results:', {
      id: patientId,
      name: patientName,
      age: patientAge
    });

    // Debug landmark storage state before analysis
    console.log('=== LANDMARK STORAGE DEBUG ===');
    console.log('Landmark storage size:', this.landmarkStorage.size);
    console.log('Landmark storage keys:', Array.from(this.landmarkStorage.keys()));

    // Log detailed landmark counts
    const landmarkCounts: { [key: string]: number } = {};
    for (const [key, landmarks] of this.landmarkStorage.entries()) {
      landmarkCounts[key] = landmarks ? landmarks.length : 0;
    }

    console.log('Landmark counts by action:', landmarkCounts);

    // Perform clinical analysis using real landmark data if available
    try {
      if (this.landmarkStorage.size > 0) {
        console.log('🔬 Performing clinical analysis with real landmark data...');
        const clinicalData = await this.prepareRealClinicalExaminationData(patientId, patientName, patientAge);
        if (clinicalData) {
          console.log('✅ Clinical data prepared successfully');
          this.clinicalResults = await this.clinicalIntegrationService.performClinicalAnalysis(clinicalData);
          console.log('✅ Clinical analysis completed:', !!this.clinicalResults);
        } else {
          console.error('❌ ERROR: Clinical data preparation failed - no real landmark data available');
          // Don't throw error, continue with basic results
          console.log('⚠️ Continuing without clinical analysis...');
        }

        // Perform comparison analysis using real landmark data
        console.log('📊 Performing comparison analysis with real landmark data...');
        const comparisonData = await this.prepareRealComparisonExaminationData(patientId, patientName, patientAge);
        if (comparisonData) {
          console.log('✅ Comparison data prepared successfully');
          this.comparisonResults = await this.clinicalComparisonService.performClinicalComparison(comparisonData);
          console.log('✅ Comparison analysis completed:', !!this.comparisonResults);
        } else {
          console.error('❌ ERROR: Comparison data preparation failed - no real landmark data available');
          // Don't throw error, continue with basic results
          console.log('⚠️ Continuing without comparison analysis...');
        }
      } else {
        console.error('❌ ERROR: No landmark data available for clinical analysis');
        console.log('⚠️ Continuing with basic results only...');
      }
    } catch (error) {
      console.error('❌ Clinical analysis failed:', error);
      console.log('⚠️ Continuing with basic analysis despite clinical analysis failure...');
      // Continue with basic analysis if clinical analysis fails
    }

    // Include raw landmark data for individual calculations in results view
    const rawLandmarkData = {
      baseline: this.landmarkStorage.get('baseline') || [],
      eyebrowRaise: this.landmarkStorage.get('eyebrowRaise') || [],
      eyeClose: this.landmarkStorage.get('eyeClose') || [],
      smile: this.landmarkStorage.get('smile') || []
    };

    const finalResults = {
      patientInfo: {
        id: patientId,
        name: patientName,
        age: patientAge
      },
      timestamp: new Date().toISOString(),
      actions: this.getCompletedActionsList(),
      overallScore: this.calculateOverallScore(),
      clinicalAnalysis: this.clinicalResults,
      comparisonAnalysis: this.comparisonResults,
      landmarkData: rawLandmarkData // Include raw landmark data for individual calculations
    };

    console.log('📋 FINAL RESULTS CREATED:', {
      hasPatientInfo: !!finalResults.patientInfo,
      hasTimestamp: !!finalResults.timestamp,
      actionsCount: finalResults.actions?.length || 0,
      overallScore: finalResults.overallScore,
      hasClinicalAnalysis: !!finalResults.clinicalAnalysis,
      hasComparisonAnalysis: !!finalResults.comparisonAnalysis,
      resultKeys: Object.keys(finalResults)
    });

    console.log('🔄 COLLECTING EXAM RESULTS - COMPLETE');
    return finalResults;
  }

  private getCompletedActionsList(): string[] {
    // Return list of completed actions for the streamlined 4-action protocol
    return [
      'Neutral / Resting Face',
      'Raise Eyebrows',
      'Close Eyes Tightly',
      'Smile Wide'
    ];
  }

  /**
   * Prepare clinical examination data using real landmark data
   */
  private async prepareRealClinicalExaminationData(
    patientId: string,
    patientName: string,
    patientAge: string
  ): Promise<ClinicalExaminationData | null> {

    if (this.landmarkStorage.size === 0) {
      console.log('No real landmark data available for clinical analysis');
      return null;
    }

    // Convert stored landmarks to the format expected by clinical analysis
    // Note: Removed lipPucker and cheekPuff as per user preference
    const realLandmarkData = {
      baseline: this.landmarkStorage.get('baseline') || [],
      eyebrowRaise: this.landmarkStorage.get('eyebrowRaise') || [],
      eyeClose: this.landmarkStorage.get('eyeClose') || [],
      smile: this.landmarkStorage.get('smile') || []
    };

    // Validate all landmark data before proceeding
    const validationErrors: string[] = [];
    const requiredActions = ['baseline', 'eyebrowRaise', 'eyeClose', 'smile']; // Removed lipPucker and cheekPuff
    const expectedLandmarkCounts = [468, 478]; // Support both MediaPipe v1 and v2

    for (const action of requiredActions) {
      const landmarks = realLandmarkData[action as keyof typeof realLandmarkData];
      if (!landmarks || !Array.isArray(landmarks)) {
        validationErrors.push(`Missing landmark data for ${action}`);
      } else if (!expectedLandmarkCounts.includes(landmarks.length)) {
        validationErrors.push(`Invalid landmark count for ${action}: ${landmarks.length}/${expectedLandmarkCounts.join(' or ')}`);
      } else {
        // Validate landmark structure
        const invalidLandmarks = landmarks.filter((landmark) => {
          return !landmark ||
                 typeof landmark.x !== 'number' ||
                 typeof landmark.y !== 'number';
        });
        if (invalidLandmarks.length > 0) {
          validationErrors.push(`Invalid landmark structure for ${action}: ${invalidLandmarks.length} invalid points`);
        }
      }
    }

    if (validationErrors.length > 0) {
      console.error('Clinical data validation failed:', validationErrors);
      console.error('Cannot proceed with clinical analysis due to invalid landmark data');
      return null;
    }

    const clinicalData: ClinicalExaminationData = {
      patientInfo: {
        id: patientId,
        name: patientName,
        age: parseInt(patientAge) || 0,
        affectedSide: 'unknown'
      },
      landmarkData: realLandmarkData,
      timestamp: new Date().toISOString()
    };

    console.log('✓ Clinical examination data validation passed');
    console.log('Prepared real clinical examination data with landmark counts:', {
      baseline: realLandmarkData.baseline.length,
      eyebrowRaise: realLandmarkData.eyebrowRaise.length,
      eyeClose: realLandmarkData.eyeClose.length,
      smile: realLandmarkData.smile.length
    });

    return clinicalData;
  }

  /**
   * Prepare comparison examination data using real landmark data
   */
  private async prepareRealComparisonExaminationData(
    patientId: string,
    patientName: string,
    patientAge: string
  ): Promise<ComparisonExaminationData | null> {

    if (this.landmarkStorage.size === 0) {
      console.log('No real landmark data available for comparison analysis');
      return null;
    }

    // Convert stored landmarks to the format expected by comparison analysis
    // Note: Removed lipPucker and cheekPuff as per user preference
    const realLandmarkData = {
      baseline: this.landmarkStorage.get('baseline') || [],
      eyebrowRaise: this.landmarkStorage.get('eyebrowRaise') || [],
      eyeClose: this.landmarkStorage.get('eyeClose') || [],
      smile: this.landmarkStorage.get('smile') || []
    };

    // Validate all landmark data before proceeding (same validation as clinical data)
    const validationErrors: string[] = [];
    const requiredActions = ['baseline', 'eyebrowRaise', 'eyeClose', 'smile']; // Removed lipPucker and cheekPuff
    const expectedLandmarkCounts = [468, 478]; // Support both MediaPipe v1 and v2

    for (const action of requiredActions) {
      const landmarks = realLandmarkData[action as keyof typeof realLandmarkData];
      if (!landmarks || !Array.isArray(landmarks)) {
        validationErrors.push(`Missing landmark data for ${action}`);
      } else if (!expectedLandmarkCounts.includes(landmarks.length)) {
        validationErrors.push(`Invalid landmark count for ${action}: ${landmarks.length}/${expectedLandmarkCounts.join(' or ')}`);
      } else {
        // Validate landmark structure
        const invalidLandmarks = landmarks.filter((landmark) => {
          return !landmark ||
                 typeof landmark.x !== 'number' ||
                 typeof landmark.y !== 'number';
        });
        if (invalidLandmarks.length > 0) {
          validationErrors.push(`Invalid landmark structure for ${action}: ${invalidLandmarks.length} invalid points`);
        }
      }
    }

    if (validationErrors.length > 0) {
      console.error('Comparison data validation failed:', validationErrors);
      console.error('Cannot proceed with comparison analysis due to invalid landmark data');
      return null;
    }

    const comparisonData: ComparisonExaminationData = {
      patientInfo: {
        id: patientId,
        name: patientName,
        age: parseInt(patientAge) || 0
      },
      landmarkData: realLandmarkData,
      timestamp: new Date().toISOString()
    };

    console.log('✓ Comparison examination data validation passed');
    console.log('Prepared real comparison examination data with landmark counts:', {
      baseline: realLandmarkData.baseline.length,
      eyebrowRaise: realLandmarkData.eyebrowRaise.length,
      eyeClose: realLandmarkData.eyeClose.length,
      smile: realLandmarkData.smile.length
    });

    return comparisonData;
  }

  // Mock landmark data generation removed - only real data is used














  /**
   * CORE: 3D Pose-Invariant Smile Analysis
   * Extracted core functionality for reuse by other methods
   */
  private performCore3DSmileAnalysis(baseline: Landmark[], smile: Landmark[]): AnalysisResults {
    // Validate input data using utility
    if (!LandmarkUtils.validateLandmarkPair(baseline, smile)) {
      return ClinicalUtils.createErrorResult('Analysis Error - Missing Landmarks');
    }

    const averageMotion = (landmarksBefore: Landmark[], landmarksAfter: Landmark[], indices: number[]): number => {
      const motions = indices.map(index => LandmarkUtils.distance3D(landmarksBefore[index], landmarksAfter[index]));
      return motions.reduce((a, b) => a + b, 0) / motions.length;
    };

    try {
      // Define landmark indices for left and right mouth regions
      const leftIndices = [48, 61, 78, 80];   // Left mouth region landmarks
      const rightIndices = [291, 308, 328, 310]; // Right mouth region landmarks

      // Calculate 3D movement for each side
      const leftMotion = averageMotion(baseline, smile, leftIndices);
      const rightMotion = averageMotion(baseline, smile, rightIndices);

      // Normalize by face width (pose-invariant scaling)
      const faceWidth = LandmarkUtils.distance3D(baseline[33], baseline[263]); // IPD as face width
      const leftNorm = leftMotion / faceWidth;
      const rightNorm = rightMotion / faceWidth;

      // Calculate pose-invariant asymmetry
      const asymmetry = Math.abs(leftNorm - rightNorm) / Math.max(leftNorm, rightNorm);

      // Validate landmark quality using utility
      const qualityMetrics = LandmarkUtils.calculateLandmarkQuality(baseline, smile);
      const landmarkCoverage = qualityMetrics.coverage;
      const coverageQuality = qualityMetrics.quality;

      // Determine affected side and clinical severity
      let affectedSide = 'None';
      if (leftNorm < rightNorm * 0.8) {
        affectedSide = 'Left';
      } else if (rightNorm < leftNorm * 0.8) {
        affectedSide = 'Right';
      }

      let severity = 'Normal';
      if (asymmetry <= 0.1) {
        severity = 'Normal';
      } else if (asymmetry <= 0.25) {
        severity = 'Mild Asymmetry';
      } else if (asymmetry <= 0.5) {
        severity = 'Moderate Asymmetry';
      } else {
        severity = 'Severe Asymmetry';
      }

      // Return core analysis result
      return {
        left_movement: leftNorm,
        right_movement: rightNorm,
        asymmetry_index: asymmetry,
        severity,
        affected_side: affectedSide,
        horizontal_displacement: {
          left: leftNorm,
          right: rightNorm,
          asymmetry: Math.abs(leftNorm - rightNorm),
          method: '3d_pose_invariant_movement'
        },
        midline_analysis: {
          face_width_ipd: faceWidth,
          landmark_coverage: landmarkCoverage,
          coverage_quality: coverageQuality,
          analysis_type: '3d_pose_invariant'
        },
        distance_measurements: {
          left_corner: {
            raw_3d_movement: leftMotion,
            normalized_movement: leftNorm
          },
          right_corner: {
            raw_3d_movement: rightMotion,
            normalized_movement: rightNorm
          }
        },
        measurement_quality: {
          landmark_coverage_percent: landmarkCoverage * 100,
          coverage_quality: coverageQuality,
          valid_landmarks: qualityMetrics.validCount,
          total_landmarks: LandmarkUtils.CRITICAL_LANDMARKS.length,
          overall_quality: coverageQuality
        },
        analysis_method: '3d_pose_invariant_movement_analysis',
        landmarks_used: {
          left_mouth_region: leftIndices,
          right_mouth_region: rightIndices,
          face_width_reference: [33, 263]
        }
      };
    } catch (error) {
      return {
        left_movement: 0,
        right_movement: 0,
        asymmetry_index: 1,
        severity: 'Analysis Error - Exception Thrown',
        affected_side: 'Unknown',
        analysis_method: '3d_pose_invariant_error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private calculateOverallScore(): number {
    // Calculate overall examination score based on clinical analysis
    try {
      if (this.clinicalResults && this.clinicalResults.composite_scores) {
        const asymmetryIndex = this.clinicalResults.composite_scores.facial_asymmetry_index;
        if (typeof asymmetryIndex === 'number') {
          // Convert asymmetry index to percentage score (lower asymmetry = higher score)
          const score = Math.max(0, 100 - (asymmetryIndex * 100));
          console.log('📊 Calculated score from clinical results:', score);
          return score;
        }
      }
      console.log('📊 Using fallback score (no clinical results available)');
      return 87.5; // Fallback
    } catch (error) {
      console.error('❌ Error calculating overall score:', error);
      return 87.5; // Fallback
    }
  }

  /**
   * Store peak frame landmarks for a specific action type
   * Used by the peak tracking system to store the best frame
   */
  private storePeakFrameLandmarks(landmarks: Landmark[], actionType: string): void {
    // Validate landmark data before storage
    if (!landmarks || !Array.isArray(landmarks)) {
      return;
    }

    if (landmarks.length < 400) {
      return;
    }

    // Validate landmark structure
    const invalidLandmarks = landmarks.filter((landmark) => {
      if (!landmark || typeof landmark !== 'object') {
        return true;
      }
      if (typeof landmark.x !== 'number' || typeof landmark.y !== 'number') {
        return true;
      }
      return false;
    });

    if (invalidLandmarks.length > 0) {
      return;
    }

    // Map action types to storage keys
    const actionMap = [
      { keys: ['baseline', 'neutral'], storage: 'baseline' },
      { keys: ['eyebrow', 'raise'], storage: 'eyebrowRaise' },
      { keys: ['eye', 'close'], storage: 'eyeClose' },
      { keys: ['smile'], storage: 'smile' },
      { keys: ['pucker', 'lips'], storage: 'lipPucker' },
      { keys: ['cheek', 'puff'], storage: 'cheekPuff' }
    ];

    // Find matching storage key
    let storageKey = '';
    const normalizedActionType = actionType.toLowerCase();

    for (const map of actionMap) {
      if (map.keys.some(k => normalizedActionType.includes(k))) {
        storageKey = map.storage;
        break;
      }
    }

    if (!storageKey) {
      return;
    }

    // Create a deep copy of landmarks to ensure data integrity
    const landmarksCopy = LandmarkUtils.copyLandmarks(landmarks);

    // Store the peak frame landmarks
    this.landmarkStorage.set(storageKey, landmarksCopy);
  }

  /**
   * Store landmarks for clinical analysis based on current action
   */
  private storeLandmarksForAnalysis(landmarks: Landmark[]): void {
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (!currentAction) {
      return;
    }

    // Validate landmark data before storage
    if (!landmarks || !Array.isArray(landmarks)) {
      return;
    }

    // MediaPipe Face Mesh can return 468 (v1) or 478 (v2 with iris) landmarks
    const expectedLandmarkCounts = [468, 478];
    if (!expectedLandmarkCounts.includes(landmarks.length)) {
      // Don't return here - log the issue but still store what we have for debugging
    }

    // Validate landmark structure - each landmark should have x, y, z coordinates
    const invalidLandmarks = landmarks.filter((landmark) => {
      if (!landmark || typeof landmark !== 'object') {
        return true;
      }
      if (typeof landmark.x !== 'number' || typeof landmark.y !== 'number') {
        return true;
      }
      return false;
    });

    if (invalidLandmarks.length > 0) {
      // Don't store invalid data
      return;
    }

    // Update current action name
    this.currentActionName = currentAction.name.toLowerCase();

    // Store landmarks based on action type
    // Handle both the actual action names from config and normalized versions
    const normalizedActionName = this.currentActionName.toLowerCase().replace(/[^a-z]/g, '');

    // Map of all possible action name variants to storage keys
    // Updated to match the actual action names from ExamActionsConfig
    // Note: Removed lipPucker and cheekPuff as per user preference
    const actionMap = [
      { keys: ['neutral', 'neutral_face', 'neutral / resting face', 'neutralrestingface', 'resting', 'restingface', 'neutral/restingface'], storage: 'baseline' },
      { keys: ['eyebrow_raise', 'eyebrow_elevation', 'raise eyebrows', 'raiseeyebrows', 'eyebrowraise', 'raise', 'eyebrow'], storage: 'eyebrowRaise' },
      { keys: ['eye_close', 'gentle_eye_closure', 'close eyes tightly', 'closeeyestightly', 'eyeclose', 'eye', 'close', 'closeeyes'], storage: 'eyeClose' },
      { keys: ['smile', 'show_teeth', 'smile wide', 'smilewide', 'smiling'], storage: 'smile' }
    ];

    let recognized = false;
    for (const map of actionMap) {
      if (map.keys.some(k => this.currentActionName.includes(k) || normalizedActionName.includes(k))) {
        // Create a deep copy of landmarks to ensure data integrity
        const landmarksCopy = LandmarkUtils.copyLandmarks(landmarks);

        this.landmarkStorage.set(map.storage, landmarksCopy);

        recognized = true;
        break;
      }
    }

    if (!recognized) {
      return;
    }


  }















  // updateActionVisualization method removed - Action View no longer exists

  private getCurrentActionType(): ExamAction {
    // Get current action from exam orchestrator
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (!currentAction) return 'neutral';

    // Extract action name from the action object
    const actionName = typeof currentAction === 'string' ? currentAction :
                      currentAction.instruction || currentAction.name || '';

    // Map action names to ExamAction types with more flexible matching
    const lowerActionName = actionName.toLowerCase();

    if (lowerActionName.includes('eyebrow') || lowerActionName.includes('raise')) {
      return 'eyebrow_raise';
    } else if (lowerActionName.includes('eye') && lowerActionName.includes('close')) {
      return 'eye_close';
    } else if (lowerActionName.includes('smile')) {
      return 'smile';
    } else if (lowerActionName.includes('neutral')) {
      return 'neutral';
    } else {
      // Default mapping based on common patterns
      switch (lowerActionName) {
        case 'neutral face':
        case 'rest':
        case 'relax':
          return 'neutral';
        case 'eyebrow elevation':
        case 'forehead wrinkle':
          return 'eyebrow_raise';
        case 'gentle eye closure':
        case 'eye squeeze':
          return 'eye_close';
        case 'show teeth':
        case 'big smile':
          return 'smile';
        default:
          console.log('Unknown action, defaulting to neutral:', actionName);
          return 'neutral';
      }
    }
  }

  private updateUI(): void {
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (currentAction) {
      this.displayInstruction(currentAction.instruction);
    }

    // Note: Action visualization update removed - Action View no longer exists

    // Update button states based on current action
    this.updateButtonStates();
  }

  private displayInstruction(instruction: string): void {
    // Prevent further instructions after exam is completed
    if (this.examCompleted) return;
    // Only log when instruction actually changes
    // Optionally, track last spoken text via SpeechService if needed
    // if (this.speechService.getLastSpokenText() !== instruction) {
    //   console.log('displayInstruction called with new instruction:', instruction);
    // }

    // Get current action for enhanced instruction display
    const currentAction = this.examOrchestrator.getCurrentAction();
    let displayText: string;
    let speechText: string;
    if (currentAction && currentAction.name) {
      // Only log when action name changes
      if (this._lastActionName !== currentAction.name) {
        console.log('Current action name:', currentAction.name);
        this._lastActionName = currentAction.name;
      }
      // Enhanced instruction with action name and details
      const actionName = currentAction.name.replace(/_/g, ' ').toUpperCase();
      displayText = `${actionName}: ${instruction}`;
      // For speech, use shorter, more direct format
      speechText = this.getShortSpeechInstruction(currentAction.name, instruction);
    } else {
      // Fallback to basic instruction
      displayText = instruction;
      speechText = instruction;
    }
    // Update visual display using InstructionView
    this.instructionView.setInstruction(displayText);
    // Only speak if the instruction is different from the last spoken
    if (this.speechService.getLastSpokenText() !== speechText) {
      this.speechService.speakInstruction(speechText);
    }
  }

  /**
   * Show examination interface and hide patient form
   */
  private showExamInterface(): void {
    console.log('Showing examination interface...');

    // Hide patient form
    const patientForm = document.getElementById('patientForm');
    if (patientForm) {
      patientForm.style.display = 'none';
      console.log('Patient form hidden');
    }

    // Show examination output (camera views)
    const outputDiv = document.getElementById('output');
    if (outputDiv) {
      outputDiv.style.display = 'block';
      console.log('Output div shown');
    }

    // Show instructions
    const instructions = document.querySelector('.instructions');
    if (instructions) {
      (instructions as HTMLElement).style.display = 'block';
      console.log('Instructions shown');
    }

    // Show instruction area below camera views
    const instructionArea = document.getElementById('instructionArea');
    if (instructionArea) {
      instructionArea.style.display = 'block';
      console.log('Instruction area shown');
    }

    // Show instruction element
    const instructionElement = document.getElementById('instruction');
    if (instructionElement) {
      instructionElement.style.display = 'block';
      console.log('Instruction element shown');
    }

    // Show next button
    const nextBtn = document.getElementById('nextBtn');
    if (nextBtn) {
      nextBtn.style.display = 'inline-block';
      console.log('Next button shown');
    }

    // Show speech toggle button
    const speechToggleBtn = document.getElementById('speechToggleBtn');
    if (speechToggleBtn) {
      speechToggleBtn.style.display = 'inline-block';
      this.updateSpeechToggleButton(); // Ensure correct initial state
      console.log('Speech toggle button shown');
    }

    // Show test speech button
    const testSpeechBtn = document.getElementById('testSpeechBtn');
    if (testSpeechBtn) {
      testSpeechBtn.style.display = 'inline-block';
      console.log('Test speech button shown');
    }

    console.log('Examination interface setup complete');
  }


  /**
   * Show error message to user
   */
  private showErrorMessage(message: string): void {
    ErrorView.showErrorMessage(message);
  }

  /**
   * Map action name to ExamActionType
   */
  private mapActionToType(actionName: string): ExamActionType {
    const lowerName = actionName.toLowerCase();

    if (lowerName.includes('neutral') || lowerName.includes('resting')) {
      return 'baseline';
    } else if (lowerName.includes('eyebrow') || lowerName.includes('raise')) {
      return 'eyebrowRaise';
    } else if (lowerName.includes('eye') && lowerName.includes('close')) {
      return 'eyeClose';
    } else if (lowerName.includes('smile') || lowerName.includes('teeth')) {
      return 'smile';
    }

    // Default fallback
    return 'baseline';
  }

  /**
   * Update button states based on movement detection
   */
  private updateButtonStatesWithDetection(): void {
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (!currentAction) return;

    const actionType = this.mapActionToType(currentAction.name);
    const detectionStatus = this.movementDetectionService.getDetectionStatus();
    const isCurrentDetected = detectionStatus.get(actionType) || false;

    const nextBtn = document.getElementById('nextBtn') as HTMLButtonElement;
    const finishBtn = document.getElementById('finishBtn') as HTMLButtonElement;

    // Update next button state
    if (nextBtn) {
      if (isCurrentDetected) {
        nextBtn.disabled = false;
        nextBtn.style.opacity = '1';
        nextBtn.style.cursor = 'pointer';
        nextBtn.title = 'Movement detected - click to proceed';
      } else {
        nextBtn.disabled = true;
        nextBtn.style.opacity = '0.5';
        nextBtn.style.cursor = 'not-allowed';
        nextBtn.title = 'Complete the current movement to proceed';
      }
    }

    // Update finish button state (only for final action)
    if (finishBtn && (actionType === 'smile' || this.examOrchestrator.isExamCompleted())) {
      const allDetected = this.movementDetectionService.areAllMovementsDetected();

      if (allDetected) {
        finishBtn.disabled = false;
        finishBtn.style.opacity = '1';
        finishBtn.style.cursor = 'pointer';
        finishBtn.title = 'All movements detected - click to finish examination';
        this.detectionStatusView.showCompletionStatus(true);
      } else {
        finishBtn.disabled = true;
        finishBtn.style.opacity = '0.5';
        finishBtn.style.cursor = 'not-allowed';
        finishBtn.title = 'Complete all movements to finish examination';
        this.detectionStatusView.showCompletionStatus(false);
      }
    }

    // Update regular button states as well
    this.updateButtonStates();
  }





  private highlightRelevantLandmarks(): void {
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (!currentAction || !currentAction.targetMetric) return;

    // Get landmarks for the current metric being measured
    const relevantLandmarks = this.getRelevantLandmarksForMetric(currentAction.targetMetric);

    // Highlight the relevant landmarks in red
    this.visualizationService?.highlightLandmarks(relevantLandmarks, '#ffff00');
  }

  private getRelevantLandmarksForMetric(metric: string): number[] {
    const landmarkMap: Record<string, number[]> = {
      'forehead': [21, 251, 9, 10], // Forehead landmarks
      'eye_gap': [159,145386,374], // Eye landmarks
      'smile': [61, 291, 13, 14], // Mouth corner landmarks
      'lip': [84, 314, 17, 18], // Lip landmarks
      'nose': [1, 2, 5, 4, 6, 19, 20] // Nose landmarks
    };

    return landmarkMap[metric] || [];
  }



  private showResults(): void {
    // Hide exam interface and show results
    const examInterface = document.getElementById('examInterface');
    const resultsInterface = document.getElementById('resultsInterface');

    if (examInterface) examInterface.style.display = 'none';
    if (resultsInterface) resultsInterface.style.display = 'block';
  }

  async exportResults(): Promise<void> {
    try {
      // Use ResultsView for PDF export
      this.resultsView.exportToPDF();
    } catch (error) {
      console.error('Failed to export PDF results:', error);
      alert('Failed to export PDF results');
    }
  }

  /**
   * Store results for the dedicated results route
   */
  private storeResultsForRoute(): void {
    if (!this.examResults) {
      console.error('❌ CRITICAL: No exam results to store - this.examResults is null/undefined');
      return;
    }

    try {
      const storageData = {
        results: this.examResults,
        timestamp: new Date().toISOString(),
        expiryTime: new Date(Date.now() + (24 * 60 * 60 * 1000)).toISOString() // 24 hours
      };

      console.log('📦 STORING RESULTS:', {
        hasResults: !!this.examResults,
        resultsKeys: this.examResults ? Object.keys(this.examResults) : 'none',
        storageDataSize: JSON.stringify(storageData).length,
        timestamp: storageData.timestamp
      });

      localStorage.setItem('facialSymmetryResults', JSON.stringify(storageData));
      console.log('✅ Results stored successfully for results route');

      // Verify storage immediately
      const verification = localStorage.getItem('facialSymmetryResults');
      console.log('🔍 STORAGE VERIFICATION:', {
        stored: !!verification,
        size: verification ? verification.length : 0
      });
    } catch (error) {
      console.error('❌ CRITICAL: Failed to store results for results route:', error);
    }
  }

  /**
   * Navigate to the dedicated results route
   */
  private navigateToResults(): void {
    console.log('Navigating to results route...');
    window.history.pushState({}, '', '/results');
    window.dispatchEvent(new Event('popstate'));
  }

  // Camera switching functionality
  private async initializeCameraSwitching(): Promise<void> {
    try {
      console.log('Initializing camera switching functionality...');

      // Check if we're on mobile device
      const isMobile = /Mobi|Android/i.test(navigator.userAgent);

      if (!isMobile) {
        console.log('Desktop device detected - camera switching not needed');
        return;
      }

      // Enumerate available cameras
      await this.cameraRepository.enumerateCameras();

      // Show switch camera button if multiple cameras are available
      const switchCameraBtn = document.getElementById('switchCameraBtn');
      if (switchCameraBtn && this.cameraRepository.hasMultipleCameras()) {
        switchCameraBtn.style.display = 'inline-block';
        console.log('Camera switch button shown - multiple cameras detected');

        // Update button text based on current camera
        this.updateCameraSwitchButton();
      } else {
        console.log('Camera switch button hidden - single camera or not found');
      }
    } catch (error) {
      console.error('Error initializing camera switching:', error);
    }
  }

  private async switchCamera(): Promise<void> {
    try {
      console.log('Switching camera...');

      // Show loading state
      const switchCameraBtn = document.getElementById('switchCameraBtn') as HTMLButtonElement;
      if (switchCameraBtn) {
        switchCameraBtn.textContent = '🔄 Switching...';
        switchCameraBtn.disabled = true;
      }

      // Switch camera using repository
      await this.cameraRepository.switchCamera();

      // Update button text
      this.updateCameraSwitchButton();

      // Re-enable button
      if (switchCameraBtn) {
        switchCameraBtn.disabled = false;
      }

      console.log('Camera switched successfully');
    } catch (error) {
      console.error('Error switching camera:', error);

      // Reset button state on error
      const switchCameraBtn = document.getElementById('switchCameraBtn') as HTMLButtonElement;
      if (switchCameraBtn) {
        switchCameraBtn.disabled = false;
        this.updateCameraSwitchButton();
      }

      // Show error message
      this.showErrorMessage('Failed to switch camera. Please try again.');
    }
  }

  private updateCameraSwitchButton(): void {
    const switchCameraBtn = document.getElementById('switchCameraBtn');
    if (switchCameraBtn) {
      const currentFacingMode = this.cameraRepository.getCurrentFacingMode();
      const nextCamera = currentFacingMode === 'user' ? 'Back' : 'Front';
      switchCameraBtn.textContent = `📷 ${nextCamera} Camera`;
      switchCameraBtn.title = `Switch to ${nextCamera.toLowerCase()} camera`;
    }
  }
}
/*  */