# Glasses Detection Troubleshooting Guide

## Current Issue: Glasses Not Being Detected

### Improvements Made

#### 1. More Sensitive Thresholds
**Before:**
- Eyebrow gap: < 0.008
- Nose bridge: < 0.005  
- Temple distance: < 0.08
- Required score: ≥ 4

**After:**
- Eyebrow gap: < 0.015 (87% more sensitive)
- Nose bridge: < 0.010 (100% more sensitive)
- Temple distance: < 0.12 (50% more sensitive)
- Required score: ≥ 3 (25% lower threshold)

#### 2. Additional Detection Features
- **Eye width asymmetry**: Glasses can affect eye shape detection
- **Eyebrow asymmetry**: Frames can cause uneven eyebrow detection
- **Fallback detection**: Simple distance check with generous thresholds

#### 3. Enhanced Debug Information
The console now shows detailed measurements for each feature:
```
Glasses Detection Debug: {
  detected: false,
  score: 2,
  threshold: 3,
  features: {
    eyebrowCompression: "false (gap: 0.0234 < 0.015) [+2]",
    noseBridgeDistortion: "true (dist: 0.0087 < 0.010) [+2]",
    templeDistortion: "false (L: 0.1456, R: 0.1523 < 0.12) [+2]",
    ...
  }
}
```

## How to Use Debug Information

### 1. Open Browser Console
- **Chrome/Edge**: F12 → Console tab
- **Firefox**: F12 → Console tab
- **Safari**: Cmd+Option+I → Console tab

### 2. Start the Application
Visit `http://localhost:3000` and start the camera

### 3. Analyze Debug Output
Look for the "Glasses Detection Debug" messages in the console

### 4. Interpret the Results

#### Understanding Feature Scores:
- **eyebrowCompression [+2]**: Glasses push eyebrows closer to eyes
- **noseBridgeDistortion [+2]**: Glasses rest on nose bridge
- **templeDistortion [+2]**: Glasses frames extend to temples
- **eyeHeightAsymmetry [+1]**: Glasses affect eye shape detection
- **eyeWidthAsymmetry [+1]**: Glasses cause eye width differences
- **eyebrowAsymmetry [+1]**: Glasses cause eyebrow position differences

#### Score Interpretation:
- **Score 0-2**: No glasses detected
- **Score 3+**: Glasses detected
- **Score 10+**: Detected via fallback method

## Common Issues and Solutions

### Issue 1: Score Too Low (0-2)
**Possible Causes:**
- Glasses frames are very thin/minimal
- Good lighting makes landmarks very accurate
- Glasses sit high on nose bridge

**Solutions:**
- Check if fallback detection triggers
- Look at individual feature measurements
- Consider if glasses are actually affecting landmarks

### Issue 2: Inconsistent Detection
**Possible Causes:**
- Head movement
- Lighting changes
- Temporal filtering requiring 70% consensus

**Solutions:**
- Keep head still for 2-3 seconds
- Ensure consistent lighting
- Check detection percentage in overlay

### Issue 3: False Negatives for Obvious Glasses
**Possible Causes:**
- Landmark detection is very accurate despite glasses
- Glasses don't significantly affect facial landmarks
- MediaPipe is compensating for glasses

**Solutions:**
- Try different head angles
- Check if specific features are triggering
- Consider manual override option

## Manual Testing Steps

### 1. With Glasses On:
1. Start application
2. Look straight at camera
3. Check console for debug messages
4. Note which features trigger (should be several)
5. Verify detection overlay shows "DETECTED"

### 2. Without Glasses:
1. Remove glasses
2. Check that detection clears after a few seconds
3. Verify overlay shows "None"
4. Confirm no false positives

### 3. Edge Cases:
- **Sunglasses**: Should definitely detect
- **Reading glasses**: May or may not detect (depends on size)
- **Contact lenses**: Should not detect
- **Safety glasses**: Should detect

## Fallback Detection

If primary detection fails, a simple fallback method checks:
```typescript
const simpleLeftDist = Math.abs(leftEyebrow.y - leftEye.y);
const simpleRightDist = Math.abs(rightEyebrow.y - rightEye.y);
const detected = (simpleLeftDist < 0.020 && simpleRightDist < 0.020);
```

This catches obvious cases where glasses significantly compress the eyebrow-eye distance.

## Next Steps if Still Not Working

### 1. Check Raw Measurements
Look at the `rawMeasurements` in debug output to see actual landmark distances

### 2. Adjust Thresholds
If measurements are close but not triggering, we can further adjust thresholds

### 3. Add Visual Debugging
We can add visual markers on the canvas to show landmark positions

### 4. Alternative Detection Methods
- Edge detection around eye area
- Color analysis for frame detection
- Machine learning-based classification

The current system should detect most glasses. If it's still not working with your specific glasses, the debug information will help us fine-tune the detection parameters.
