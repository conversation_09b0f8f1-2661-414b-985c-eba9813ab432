// Domain Value Object: Facial Metrics
export class FacialMetrics {
  constructor(
    public readonly forehead: number,
    public readonly eyeGap: number,
    public readonly smile: number,
    public readonly lip: number,
    public readonly nose: number
  ) {}

  static create(data: {
    forehead: number;
    eye_gap: number;
    smile: number;
    lip: number;
    nose: number;
  }): FacialMetrics {
    return new FacialMetrics(
      data.forehead,
      data.eye_gap,
      data.smile,
      data.lip,
      data.nose
    );
  }

  calculateTFAI(): number {
    return (this.forehead + this.eyeGap + this.smile + this.lip + this.nose) / 5;
  }

  getMetricByName(name: string): number {
    switch (name) {
      case 'forehead': return this.forehead;
      case 'eye_gap': return this.eyeGap;
      case 'smile': return this.smile;
      case 'lip': return this.lip;
      case 'nose': return this.nose;
      default: throw new Error(`Unknown metric: ${name}`);
    }
  }

  toObject(): Record<string, number> {
    return {
      forehead: this.forehead,
      eye_gap: this.eyeGap,
      smile: this.smile,
      lip: this.lip,
      nose: this.nose
    };
  }
}
