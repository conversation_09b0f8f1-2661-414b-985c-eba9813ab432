// Domain Service: Clinical Comparison of Left and Right Facial Movements
// Provides detailed asymmetry analysis for <PERSON>'s palsy and facial paralysis assessment

export interface LandmarkPoint {
  x: number;
  y: number;
  z?: number;
}

export interface ClinicalComparisonResult {
  action: string;
  left_movement: number;
  right_movement: number;
  absolute_difference: number;
  percentage_difference: number;
  affected_side: 'left' | 'right' | 'bilateral' | 'none';
  severity: 'normal' | 'mild' | 'moderate' | 'severe';
  clinical_notes: string;
}

export interface DetailedComparisonReport {
  patient_id: string;
  examination_date: string;
  actions: {
    eyebrow_raise: ClinicalComparisonResult;
    eye_closure: ClinicalComparisonResult;
    smile: ClinicalComparisonResult;
    lip_pucker: ClinicalComparisonResult;
    nose_flare: ClinicalComparisonResult;
  };
  overall_assessment: {
    primary_affected_side: 'left' | 'right' | 'bilateral' | 'none';
    average_asymmetry: number;
    severity_distribution: Record<string, number>;
    clinical_recommendations: string[];
  };
}

export class ClinicalComparisonService {
  // MediaPipe landmark indices for clinical analysis
  private readonly LANDMARK_PAIRS = {
    // Eyebrow raise analysis - vertical movement from baseline
    eyebrow_raise: {
      left: 105,   // Left eyebrow peak
      right: 334,  // Right eyebrow peak
      measurement: 'vertical_displacement'
    },

    // Eye closure analysis - vertical eyelid gap
    eye_closure: {
      left: { upper: 159, lower: 145 },   // Left eye vertical landmarks
      right: { upper: 386, lower: 374 }, // Right eye vertical landmarks
      measurement: 'vertical_gap_reduction'
    },

    // Smile analysis - horizontal mouth corner movement
    smile: {
      left: 61,    // Left mouth corner
      right: 291,  // Right mouth corner
      measurement: 'horizontal_displacement'
    },

    // Lip pucker analysis - vertical lip compression
    lip_pucker: {
      left: 57,    // Left upper lip
      right: 287,  // Right upper lip
      measurement: 'vertical_compression'
    },

    // Nose flare analysis - lateral movement
    nose_flare: {
      left: 98,    // Left nostril
      right: 327,  // Right nostril
      measurement: 'lateral_displacement'
    }
  };

  // Clinical severity thresholds (percentage differences)
  private readonly SEVERITY_THRESHOLDS = {
    normal: 10,     // <10% difference
    mild: 25,       // 10-25% difference
    moderate: 50,   // 25-50% difference
    severe: 100     // >50% difference
  };

  /**
   * Main function to compare left and right facial movements for clinical assessment
   */
  public compareSidesClinicalReport(
    actionName: string,
    currentLandmarks: LandmarkPoint[],
    baselineLandmarks: LandmarkPoint[]
  ): ClinicalComparisonResult {

    console.log(`Performing clinical comparison for action: ${actionName}`);

    // Validate inputs - support both MediaPipe v1 (468) and v2 (478) landmarks
    const expectedLandmarkCounts = [468, 478];
    if (!currentLandmarks || !baselineLandmarks ||
        !expectedLandmarkCounts.includes(currentLandmarks.length) ||
        !expectedLandmarkCounts.includes(baselineLandmarks.length)) {
      throw new Error(`Invalid landmark data: Expected ${expectedLandmarkCounts.join(' or ')} landmarks for current and baseline`);
    }

    // Get landmark configuration for this action
    const actionKey = this.normalizeActionName(actionName);
    const landmarkConfig = this.LANDMARK_PAIRS[actionKey as keyof typeof this.LANDMARK_PAIRS];

    if (!landmarkConfig) {
      throw new Error(`Unsupported action: ${actionName}`);
    }

    // Calculate movements based on action type
    let leftMovement: number;
    let rightMovement: number;

    switch (actionKey) {
      case 'eyebrow_raise':
        leftMovement = this.calculateEyebrowMovement(currentLandmarks, baselineLandmarks, 'left');
        rightMovement = this.calculateEyebrowMovement(currentLandmarks, baselineLandmarks, 'right');
        break;

      case 'eye_closure':
        leftMovement = this.calculateEyeClosureMovement(currentLandmarks, baselineLandmarks, 'left');
        rightMovement = this.calculateEyeClosureMovement(currentLandmarks, baselineLandmarks, 'right');
        break;

      case 'smile':
        leftMovement = this.calculateSmileMovement(currentLandmarks, baselineLandmarks, 'left');
        rightMovement = this.calculateSmileMovement(currentLandmarks, baselineLandmarks, 'right');
        break;

      case 'lip_pucker':
        leftMovement = this.calculateLipPuckerMovement(currentLandmarks, baselineLandmarks, 'left');
        rightMovement = this.calculateLipPuckerMovement(currentLandmarks, baselineLandmarks, 'right');
        break;

      case 'nose_flare':
        leftMovement = this.calculateNoseFlareMovement(currentLandmarks, baselineLandmarks, 'left');
        rightMovement = this.calculateNoseFlareMovement(currentLandmarks, baselineLandmarks, 'right');
        break;

      default:
        throw new Error(`Movement calculation not implemented for action: ${actionName}`);
    }

    // Calculate differences and asymmetry
    const absoluteDifference = Math.abs(leftMovement - rightMovement);
    const maxMovement = Math.max(leftMovement, rightMovement);
    const percentageDifference = maxMovement > 0 ? (absoluteDifference / maxMovement) * 100 : 0;

    // Determine affected side
    const affectedSide = this.determineAffectedSide(leftMovement, rightMovement, percentageDifference);

    // Assign severity grade
    const severity = this.assignSeverityGrade(percentageDifference);

    // Generate clinical notes
    const clinicalNotes = this.generateClinicalNotes(actionName, leftMovement, rightMovement, affectedSide, severity);

    const result: ClinicalComparisonResult = {
      action: actionName,
      left_movement: Math.round(leftMovement * 1000) / 1000, // Round to 3 decimal places
      right_movement: Math.round(rightMovement * 1000) / 1000,
      absolute_difference: Math.round(absoluteDifference * 1000) / 1000,
      percentage_difference: Math.round(percentageDifference * 10) / 10, // Round to 1 decimal place
      affected_side: affectedSide,
      severity: severity,
      clinical_notes: clinicalNotes
    };

    console.log(`Clinical comparison result for ${actionName}:`, result);
    return result;
  }

  /**
   * Calculate eyebrow movement (vertical displacement from baseline)
   */
  private calculateEyebrowMovement(
    currentLandmarks: LandmarkPoint[],
    baselineLandmarks: LandmarkPoint[],
    side: 'left' | 'right'
  ): number {
    const landmarkIndex = side === 'left' ? 105 : 334;

    const currentPoint = currentLandmarks[landmarkIndex];
    const baselinePoint = baselineLandmarks[landmarkIndex];

    if (!currentPoint || !baselinePoint) {
      console.warn(`Missing landmark ${landmarkIndex} for ${side} eyebrow`);
      return 0;
    }

    // Calculate vertical displacement (negative Y means upward movement)
    const verticalDisplacement = Math.abs(baselinePoint.y - currentPoint.y);

    console.log(`${side} eyebrow movement: ${verticalDisplacement.toFixed(4)}`);
    return verticalDisplacement;
  }

  /**
   * Calculate eye closure movement (reduction in vertical eyelid gap)
   */
  private calculateEyeClosureMovement(
    currentLandmarks: LandmarkPoint[],
    baselineLandmarks: LandmarkPoint[],
    side: 'left' | 'right'
  ): number {
    const indices = side === 'left' ? { upper: 159, lower: 145 } : { upper: 386, lower: 374 };

    const currentUpper = currentLandmarks[indices.upper];
    const currentLower = currentLandmarks[indices.lower];
    const baselineUpper = baselineLandmarks[indices.upper];
    const baselineLower = baselineLandmarks[indices.lower];

    if (!currentUpper || !currentLower || !baselineUpper || !baselineLower) {
      console.warn(`Missing eye landmarks for ${side} eye closure`);
      return 0;
    }

    // Calculate vertical gap at baseline and current state
    const baselineGap = Math.abs(baselineUpper.y - baselineLower.y);
    const currentGap = Math.abs(currentUpper.y - currentLower.y);

    // Movement is the reduction in gap (how much the eye closed)
    const gapReduction = Math.max(0, baselineGap - currentGap);

    console.log(`${side} eye closure: baseline gap ${baselineGap.toFixed(4)}, current gap ${currentGap.toFixed(4)}, reduction ${gapReduction.toFixed(4)}`);
    return gapReduction;
  }

  /**
   * Calculate smile movement (horizontal displacement of mouth corner)
   */
  private calculateSmileMovement(
    currentLandmarks: LandmarkPoint[],
    baselineLandmarks: LandmarkPoint[],
    side: 'left' | 'right'
  ): number {
    const landmarkIndex = side === 'left' ? 61 : 291;

    const currentPoint = currentLandmarks[landmarkIndex];
    const baselinePoint = baselineLandmarks[landmarkIndex];

    if (!currentPoint || !baselinePoint) {
      console.warn(`Missing landmark ${landmarkIndex} for ${side} mouth corner`);
      return 0;
    }

    // Calculate horizontal displacement (lateral movement for smile)
    const horizontalDisplacement = Math.abs(currentPoint.x - baselinePoint.x);

    console.log(`${side} smile movement: ${horizontalDisplacement.toFixed(4)}`);
    return horizontalDisplacement;
  }

  /**
   * Calculate lip pucker movement (vertical compression)
   */
  private calculateLipPuckerMovement(
    currentLandmarks: LandmarkPoint[],
    baselineLandmarks: LandmarkPoint[],
    side: 'left' | 'right'
  ): number {
    const landmarkIndex = side === 'left' ? 57 : 287;

    const currentPoint = currentLandmarks[landmarkIndex];
    const baselinePoint = baselineLandmarks[landmarkIndex];

    if (!currentPoint || !baselinePoint) {
      console.warn(`Missing landmark ${landmarkIndex} for ${side} lip`);
      return 0;
    }

    // Calculate vertical compression (forward movement for pucker)
    const verticalCompression = Math.abs(currentPoint.y - baselinePoint.y);

    console.log(`${side} lip pucker movement: ${verticalCompression.toFixed(4)}`);
    return verticalCompression;
  }

  /**
   * Calculate nose flare movement (lateral displacement)
   */
  private calculateNoseFlareMovement(
    currentLandmarks: LandmarkPoint[],
    baselineLandmarks: LandmarkPoint[],
    side: 'left' | 'right'
  ): number {
    const landmarkIndex = side === 'left' ? 98 : 327;

    const currentPoint = currentLandmarks[landmarkIndex];
    const baselinePoint = baselineLandmarks[landmarkIndex];

    if (!currentPoint || !baselinePoint) {
      console.warn(`Missing landmark ${landmarkIndex} for ${side} nostril`);
      return 0;
    }

    // Calculate lateral displacement (outward flare)
    const lateralDisplacement = Math.abs(currentPoint.x - baselinePoint.x);

    console.log(`${side} nose flare movement: ${lateralDisplacement.toFixed(4)}`);
    return lateralDisplacement;
  }

  /**
   * Normalize action name to match internal keys
   */
  private normalizeActionName(actionName: string): string {
    const normalized = actionName.toLowerCase().replace(/[^a-z]/g, '_');

    // Map common variations to standard names
    const mappings: Record<string, string> = {
      'eyebrow_raise': 'eyebrow_raise',
      'eyebrow_elevation': 'eyebrow_raise',
      'raise_eyebrows': 'eyebrow_raise',
      'eye_close': 'eye_closure',
      'eye_closure': 'eye_closure',
      'close_eyes': 'eye_closure',
      'smile': 'smile',
      'grin': 'smile',
      'lip_pucker': 'lip_pucker',
      'pucker': 'lip_pucker',
      'kiss': 'lip_pucker',
      'nose_flare': 'nose_flare',
      'nostril_flare': 'nose_flare'
    };

    return mappings[normalized] || normalized;
  }

  /**
   * Determine which side is affected based on movement comparison
   */
  private determineAffectedSide(
    leftMovement: number,
    rightMovement: number,
    percentageDifference: number
  ): 'left' | 'right' | 'bilateral' | 'none' {

    // If difference is very small, consider normal
    if (percentageDifference < this.SEVERITY_THRESHOLDS.normal) {
      return 'none';
    }

    // If both sides have very little movement, consider bilateral
    if (leftMovement < 0.01 && rightMovement < 0.01) {
      return 'bilateral';
    }

    // Affected side is the one with less movement
    return leftMovement < rightMovement ? 'left' : 'right';
  }

  /**
   * Assign severity grade based on percentage difference
   */
  private assignSeverityGrade(percentageDifference: number): 'normal' | 'mild' | 'moderate' | 'severe' {
    if (percentageDifference < this.SEVERITY_THRESHOLDS.normal) {
      return 'normal';
    } else if (percentageDifference < this.SEVERITY_THRESHOLDS.mild) {
      return 'mild';
    } else if (percentageDifference < this.SEVERITY_THRESHOLDS.moderate) {
      return 'moderate';
    } else {
      return 'severe';
    }
  }

  /**
   * Generate clinical notes for the comparison result
   */
  private generateClinicalNotes(
    actionName: string,
    leftMovement: number,
    rightMovement: number,
    affectedSide: string,
    severity: string
  ): string {

    if (severity === 'normal') {
      return `${actionName}: Bilateral movement within normal limits. No significant asymmetry detected.`;
    }

    const strongerSide = leftMovement > rightMovement ? 'right' : 'left';
    const weakerSide = affectedSide;

    let notes = `${actionName}: `;

    switch (severity) {
      case 'mild':
        notes += `Mild asymmetry with ${weakerSide} side showing reduced movement. `;
        break;
      case 'moderate':
        notes += `Moderate asymmetry with notable ${weakerSide} side weakness. `;
        break;
      case 'severe':
        notes += `Severe asymmetry with significant ${weakerSide} side impairment. `;
        break;
    }

    notes += `${strongerSide.charAt(0).toUpperCase() + strongerSide.slice(1)} side demonstrates stronger movement. `;
    notes += `Consider follow-up assessment and potential intervention.`;

    return notes;
  }

  /**
   * Generate comprehensive clinical report for all facial actions
   */
  public generateDetailedComparisonReport(
    patientId: string,
    landmarkData: {
      baseline: LandmarkPoint[];
      eyebrow_raise: LandmarkPoint[];
      eye_closure: LandmarkPoint[];
      smile: LandmarkPoint[];
      lip_pucker: LandmarkPoint[];
      nose_flare?: LandmarkPoint[];
    }
  ): DetailedComparisonReport {

    console.log(`Generating detailed comparison report for patient: ${patientId}`);

    // Perform comparison for each action
    const eyebrowResult = this.compareSidesClinicalReport('Eyebrow Raise', landmarkData.eyebrow_raise, landmarkData.baseline);
    const eyeResult = this.compareSidesClinicalReport('Eye Closure', landmarkData.eye_closure, landmarkData.baseline);
    const smileResult = this.compareSidesClinicalReport('Smile', landmarkData.smile, landmarkData.baseline);

    // Handle lip pucker - create empty result if no data provided (removed from examination)
    const puckerResult = landmarkData.lip_pucker && landmarkData.lip_pucker.length > 0 ?
      this.compareSidesClinicalReport('Lip Pucker', landmarkData.lip_pucker, landmarkData.baseline) :
      this.createEmptyResult('Lip Pucker (Removed)');

    // Nose flare is optional
    const noseResult = landmarkData.nose_flare ?
      this.compareSidesClinicalReport('Nose Flare', landmarkData.nose_flare, landmarkData.baseline) :
      this.createEmptyResult('Nose Flare');

    // Calculate overall assessment
    const allResults = [eyebrowResult, eyeResult, smileResult, puckerResult];
    if (landmarkData.nose_flare) allResults.push(noseResult);

    const overallAssessment = this.calculateOverallAssessment(allResults);

    const report: DetailedComparisonReport = {
      patient_id: patientId,
      examination_date: new Date().toISOString(),
      actions: {
        eyebrow_raise: eyebrowResult,
        eye_closure: eyeResult,
        smile: smileResult,
        lip_pucker: puckerResult,
        nose_flare: noseResult
      },
      overall_assessment: overallAssessment
    };

    console.log('Detailed comparison report generated:', report);
    return report;
  }

  /**
   * Calculate overall assessment from individual action results
   */
  private calculateOverallAssessment(results: ClinicalComparisonResult[]): {
    primary_affected_side: 'left' | 'right' | 'bilateral' | 'none';
    average_asymmetry: number;
    severity_distribution: Record<string, number>;
    clinical_recommendations: string[];
  } {

    // Count affected sides
    const sideCount = { left: 0, right: 0, bilateral: 0, none: 0 };
    results.forEach(result => {
      sideCount[result.affected_side]++;
    });

    // Determine primary affected side
    let primaryAffectedSide: 'left' | 'right' | 'bilateral' | 'none' = 'none';
    if (sideCount.left > sideCount.right && sideCount.left > sideCount.none) {
      primaryAffectedSide = 'left';
    } else if (sideCount.right > sideCount.left && sideCount.right > sideCount.none) {
      primaryAffectedSide = 'right';
    } else if (sideCount.bilateral > 0) {
      primaryAffectedSide = 'bilateral';
    }

    // Calculate average asymmetry
    const validResults = results.filter(r => r.affected_side !== 'none');
    const averageAsymmetry = validResults.length > 0 ?
      validResults.reduce((sum, r) => sum + r.percentage_difference, 0) / validResults.length : 0;

    // Count severity distribution
    const severityDistribution: Record<string, number> = { normal: 0, mild: 0, moderate: 0, severe: 0 };
    results.forEach(result => {
      severityDistribution[result.severity]++;
    });

    // Generate clinical recommendations
    const recommendations = this.generateClinicalRecommendations(primaryAffectedSide, averageAsymmetry, severityDistribution);

    return {
      primary_affected_side: primaryAffectedSide,
      average_asymmetry: Math.round(averageAsymmetry * 10) / 10,
      severity_distribution: severityDistribution,
      clinical_recommendations: recommendations
    };
  }

  /**
   * Generate clinical recommendations based on overall assessment
   */
  private generateClinicalRecommendations(
    primaryAffectedSide: string,
    averageAsymmetry: number,
    severityDistribution: Record<string, number>
  ): string[] {

    const recommendations: string[] = [];

    // Based on affected side
    if (primaryAffectedSide !== 'none') {
      recommendations.push(`Primary weakness detected on ${primaryAffectedSide} side`);
      recommendations.push('Consider neurological evaluation for facial nerve function');
    }

    // Based on severity
    if (severityDistribution.severe > 0) {
      recommendations.push('Immediate medical attention recommended due to severe asymmetry');
      recommendations.push('Consider MRI or CT imaging to rule out structural causes');
    } else if (severityDistribution.moderate > 0) {
      recommendations.push('Moderate asymmetry requires follow-up within 2-4 weeks');
      recommendations.push('Physical therapy consultation for facial exercises');
    } else if (severityDistribution.mild > 0) {
      recommendations.push('Mild asymmetry - monitor for progression');
      recommendations.push('Follow-up in 4-6 weeks if symptoms persist');
    }

    // Based on average asymmetry
    if (averageAsymmetry > 40) {
      recommendations.push('High average asymmetry suggests significant facial nerve impairment');
      recommendations.push('Consider corticosteroid therapy if acute onset');
    } else if (averageAsymmetry > 20) {
      recommendations.push('Moderate average asymmetry warrants close monitoring');
    }

    // General recommendations
    if (primaryAffectedSide !== 'none') {
      recommendations.push('Document baseline measurements for future comparison');
      recommendations.push('Patient education on facial exercises and eye protection');
    }

    return recommendations;
  }

  /**
   * Create empty result for missing data
   */
  private createEmptyResult(actionName: string): ClinicalComparisonResult {
    return {
      action: actionName,
      left_movement: 0,
      right_movement: 0,
      absolute_difference: 0,
      percentage_difference: 0,
      affected_side: 'none',
      severity: 'normal',
      clinical_notes: `${actionName}: No data available for analysis.`
    };
  }

  /**
   * Export comparison results to CSV format
   */
  public exportToCSV(report: DetailedComparisonReport): string {
    const headers = [
      'Patient ID',
      'Examination Date',
      'Action',
      'Left Movement',
      'Right Movement',
      'Absolute Difference',
      'Percentage Difference',
      'Affected Side',
      'Severity',
      'Clinical Notes'
    ];

    let csv = headers.join(',') + '\n';

    // Add data rows for each action
    Object.entries(report.actions).forEach(([actionKey, result]) => {
      const row = [
        report.patient_id,
        new Date(report.examination_date).toLocaleDateString(),
        result.action,
        result.left_movement.toString(),
        result.right_movement.toString(),
        result.absolute_difference.toString(),
        result.percentage_difference.toString(),
        result.affected_side,
        result.severity,
        `"${result.clinical_notes}"` // Wrap in quotes for CSV safety
      ];
      csv += row.join(',') + '\n';
    });

    // Add overall assessment
    csv += '\nOverall Assessment\n';
    csv += `Primary Affected Side,${report.overall_assessment.primary_affected_side}\n`;
    csv += `Average Asymmetry,${report.overall_assessment.average_asymmetry}%\n`;
    csv += `Severity Distribution,${JSON.stringify(report.overall_assessment.severity_distribution)}\n`;

    // Add recommendations
    csv += '\nClinical Recommendations\n';
    report.overall_assessment.clinical_recommendations.forEach((rec, index) => {
      csv += `Recommendation ${index + 1},"${rec}"\n`;
    });

    return csv;
  }

  /**
   * Export comparison results to Markdown format
   */
  public exportToMarkdown(report: DetailedComparisonReport): string {
    let markdown = `# Clinical Facial Asymmetry Comparison Report\n\n`;

    markdown += `**Patient ID:** ${report.patient_id}\n`;
    markdown += `**Examination Date:** ${new Date(report.examination_date).toLocaleDateString()}\n`;
    markdown += `**Report Generated:** ${new Date().toLocaleDateString()}\n\n`;

    // Individual action results
    markdown += `## Individual Action Analysis\n\n`;

    Object.entries(report.actions).forEach(([actionKey, result]) => {
      markdown += `### ${result.action}\n\n`;
      markdown += `| Metric | Value |\n`;
      markdown += `|--------|-------|\n`;
      markdown += `| Left Movement | ${result.left_movement} |\n`;
      markdown += `| Right Movement | ${result.right_movement} |\n`;
      markdown += `| Absolute Difference | ${result.absolute_difference} |\n`;
      markdown += `| Percentage Difference | ${result.percentage_difference}% |\n`;
      markdown += `| Affected Side | ${result.affected_side} |\n`;
      markdown += `| Severity | ${result.severity} |\n\n`;
      markdown += `**Clinical Notes:** ${result.clinical_notes}\n\n`;
    });

    // Overall assessment
    markdown += `## Overall Assessment\n\n`;
    markdown += `- **Primary Affected Side:** ${report.overall_assessment.primary_affected_side}\n`;
    markdown += `- **Average Asymmetry:** ${report.overall_assessment.average_asymmetry}%\n\n`;

    markdown += `### Severity Distribution\n\n`;
    Object.entries(report.overall_assessment.severity_distribution).forEach(([severity, count]) => {
      markdown += `- **${severity.charAt(0).toUpperCase() + severity.slice(1)}:** ${count} actions\n`;
    });

    markdown += `\n### Clinical Recommendations\n\n`;
    report.overall_assessment.clinical_recommendations.forEach((rec, index) => {
      markdown += `${index + 1}. ${rec}\n`;
    });

    markdown += `\n---\n*Report generated by Clinical Facial Asymmetry Analysis System*\n`;

    return markdown;
  }
}
