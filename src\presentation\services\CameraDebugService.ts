// CameraDebugService.ts
// Handles camera and visualization debugging/clearing logic

export class CameraDebugService {
  static debugCameraStatus(): void {
    console.log('=== CAMERA DEBUG STATUS ===');
    const videoElement = document.getElementById('video') as HTMLVideoElement;
    if (videoElement) {
      console.log('Video element status:', {
        srcObject: !!videoElement.srcObject,
        videoWidth: videoElement.videoWidth,
        videoHeight: videoElement.videoHeight,
        readyState: videoElement.readyState,
        paused: videoElement.paused,
        currentTime: videoElement.currentTime,
        style: {
          display: videoElement.style.display,
          visibility: videoElement.style.visibility,
          opacity: videoElement.style.opacity
        }
      });
    } else {
      console.error('Video element not found!');
    }
    const canvases = ['canvas', 'analysisCanvas', 'actionCanvas'];
    canvases.forEach(canvasId => {
      const canvas = document.getElementById(canvasId) as HTMLCanvasElement;
      if (canvas) {
        console.log(`${canvasId} status:`, {
          width: canvas.width,
          height: canvas.height,
          style: {
            display: canvas.style.display,
            visibility: canvas.style.visibility
          }
        });
      } else {
        console.error(`${canvasId} not found!`);
      }
    });
    console.log('MediaPipe status:', {
      FaceMesh: typeof window.FaceMesh,
      Camera: typeof window.Camera
    });
    console.log('=== END CAMERA DEBUG ===');
  }

  static clearAllVisualizationServices(visualizationService: any, analysisVisualizationService: any, actionVisualizationService: any): void {
    try {
      if (visualizationService && typeof visualizationService.clearCanvas === 'function') {
        visualizationService.clearCanvas();
        console.log('Face visualization cleared');
      }
      if (analysisVisualizationService && typeof analysisVisualizationService.clearCanvas === 'function') {
        analysisVisualizationService.clearCanvas();
        console.log('Analysis visualization cleared');
      }
      if (actionVisualizationService && typeof actionVisualizationService.clearCanvas === 'function') {
        actionVisualizationService.clearCanvas();
        console.log('Action visualization cleared');
      }
      console.log('All visualization services cleared successfully');
    } catch (error) {
      console.error('Error clearing visualization services:', error);
    }
  }
}
