<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <style>
    /* Base styles */
    * {
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Arial, sans-serif;
      background: #f6f8fa;
      margin: 0;
      padding: 0;
      font-size: 15px;
      line-height: 1.6;
      -webkit-text-size-adjust: 100%;
      -webkit-tap-highlight-color: transparent;
      overflow-x: hidden;
    }

    h2 {
      color: #2c3e50;
      text-align: center;
      margin: 20px 0;
      font-size: 1.5em;
      padding: 0 20px;
    }

    /* Responsive container */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      width: 100%;
    }

    /* Legacy output styles for compatibility */
    #output {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 20px auto;
      width: 100%;
      max-width: 640px;
      text-align: center;
    }

    #video, #canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: auto;
      aspect-ratio: 4/3;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(44,62,80,0.12);
      margin: 0;
      padding: 0;
      z-index: 1;
    }

    #canvas {
      z-index: 2;
      pointer-events: none;
    }

    /* Patient form responsive styles */
    #patientForm {
      margin: 20px auto;
      background: #fff;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(44,62,80,0.10);
      max-width: 400px;
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 18px;
    }

    #patientForm label {
      display: flex;
      flex-direction: column;
      font-size: 1em;
      color: #34495e;
      margin-bottom: 0;
    }

    #patientForm input {
      margin-top: 6px;
      padding: 12px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 1em;
      background: #f9fafb;
      transition: border 0.2s;
      width: 100%;
    }

    #patientForm input:focus {
      border: 1.5px solid #3498db;
      outline: none;
      background: #fff;
    }

    #patientForm button {
      margin-top: 10px;
      padding: 12px 0;
      background: linear-gradient(90deg, #3498db 60%, #6dd5fa 100%);
      color: #fff;
      border: none;
      border-radius: 6px;
      font-size: 1em;
      font-weight: 600;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(52,152,219,0.08);
      transition: background 0.2s;
      width: 100%;
    }

    #patientForm button:hover {
      background: linear-gradient(90deg, #217dbb 60%, #4fc3f7 100%);
    }

    /* Responsive button styles */
    .btn {
      padding: 12px 20px;
      border: none;
      border-radius: 6px;
      font-size: 1em;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin: 5px;
      min-width: 120px;
    }

    .btn-primary {
      background: #3498db;
      color: white;
    }

    .btn-primary:hover {
      background: #2980b9;
      transform: translateY(-1px);
    }

    .btn-success {
      background: #27ae60;
      color: white;
    }

    .btn-success:hover {
      background: #229954;
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: #95a5a6;
      color: white;
    }

    .btn-secondary:hover {
      background: #7f8c8d;
      transform: translateY(-1px);
    }

    /* Media Queries for Responsive Design */

    /* Large screens (desktops) */
    @media (min-width: 1200px) {
      .container {
        max-width: 1140px;
      }

      h2 {
        font-size: 2em;
      }
    }

    /* Medium screens (tablets) */
    @media (max-width: 1199px) and (min-width: 768px) {
      .container {
        max-width: 960px;
      }

      #patientForm {
        max-width: 500px;
        padding: 30px;
      }

      .btn {
        min-width: 140px;
        padding: 14px 24px;
      }
    }

    /* Small screens (mobile landscape) */
    @media (max-width: 767px) and (min-width: 576px) {
      .container {
        padding: 0 15px;
      }

      h2 {
        font-size: 1.3em;
        margin: 15px 0;
      }

      #patientForm {
        margin: 15px auto;
        padding: 20px;
        max-width: 100%;
      }

      .btn {
        min-width: 100px;
        padding: 12px 16px;
        font-size: 0.9em;
        margin: 3px;
      }
    }

    /* Extra small screens (mobile portrait) */
    @media (max-width: 575px) {
      .container {
        padding: 0 10px;
        width: 100%;
      }

      h2 {
        font-size: 1.2em;
        margin: 10px 0;
        padding: 0 10px;
      }

      #patientForm {
        margin: 10px auto;
        padding: 15px;
        border-radius: 8px;
        width: calc(100% - 20px);
        max-width: 100%;
      }

      #patientForm input {
        padding: 12px;
        font-size: 16px; /* Prevents zoom on iOS */
        width: 100%;
        box-sizing: border-box;
      }

      #patientForm button {
        padding: 14px 0;
        font-size: 16px;
        width: 100%;
      }

      .btn {
        min-width: 80px;
        padding: 12px 16px;
        font-size: 0.9em;
        margin: 2px;
        border-radius: 6px;
        touch-action: manipulation;
      }

      #output {
        margin: 10px auto;
        padding: 0 10px;
        width: 100%;
      }

      #video, #canvas {
        border-radius: 8px;
        width: 100% !important;
        height: auto !important;
      }
    }

    /* Very small screens */
    @media (max-width: 375px) {
      .container {
        padding: 0 5px;
      }

      h2 {
        font-size: 1.1em;
        margin: 8px 0;
        padding: 0 5px;
      }

      #patientForm {
        margin: 5px auto;
        padding: 12px;
        width: calc(100% - 10px);
      }

      #patientForm input {
        padding: 10px;
        font-size: 16px;
      }

      .btn {
        min-width: 70px;
        padding: 10px 12px;
        font-size: 0.85em;
        margin: 1px;
      }
    }
  </style>
</head>
<body>
  <script>
    // Show instructions card only in exam view
    function showInstructionsCard(show) {
      const card = document.getElementById('instructionsCard');
      if (card) card.style.display = show ? 'block' : 'none';
    }
    // Show on exam start, hide on patient form/results
    document.addEventListener('DOMContentLoaded', () => {
      // Hide by default (already set in HTML)
      // Show when exam interface is shown
      const origShowExamInterface = window.showExamInterface;
      window.showExamInterface = function() {
        if (typeof origShowExamInterface === 'function') origShowExamInterface();
        showInstructionsCard(true);
      };
      // Hide when patient form or results are shown
      const origShowResultsPage = window.showResultsPage;
      window.showResultsPage = function() {
        if (typeof origShowResultsPage === 'function') origShowResultsPage();
        showInstructionsCard(false);
      };
      const patientForm = document.getElementById('patientForm');
      if (patientForm) {
        patientForm.addEventListener('submit', () => showInstructionsCard(false));
      }
    });
  </script>

  <!-- Route container -->
  <div id="routeContainer"></div>



  <!-- metrics now inside resultsView -->
  <script>
    // Clean SPA router: root route ('/') serves home, #/exam and #/results for other views
    // SPA router: loads partials into #routeContainer
    async function loadRoute(route) {
      const container = document.getElementById('routeContainer');
      if (!container) {
        console.error('Route container not found');
        return;
      }

      let file = '';
      if (route === '') {
        // Root route serves home content directly
        file = 'pages/home.html';
      } else if (route === 'exam') {
        file = 'pages/exam.html';
      } else if (route === 'results') {
        file = 'pages/results.html';
      } else if (route === 'image-assessment') {
        file = 'pages/image-assessment.html';
      } else {
        // Unknown routes redirect to root (home)
        console.log(`Unknown route '${route}', redirecting to home`);
        window.location.hash = '';
        return;
      }

      try {
        console.log(`Loading route: ${route}, file: ${file}`);
        const resp = await fetch(file);

        if (!resp.ok) {
          throw new Error(`Failed to fetch ${file}: ${resp.status} ${resp.statusText}`);
        }

        const html = await resp.text();
        container.innerHTML = html;

        // Execute any script tags in the loaded content
        if (route === 'image-assessment') {
          executeScriptsInContainer(container);
        }

        showInstructionsCard(route === 'exam');

        // Fire custom events after views are loaded with mobile-specific delays
        if (route === 'exam') {
          // Longer delay for mobile devices to ensure DOM is ready
          const delay = /Mobi|Android/i.test(navigator.userAgent) ? 200 : 50;
          setTimeout(() => {
            console.log('Dispatching examViewLoaded event');
            window.dispatchEvent(new Event('examViewLoaded'));
          }, delay);
        } else if (route === 'results') {
          // Even longer delay for results on mobile due to complexity
          const delay = /Mobi|Android/i.test(navigator.userAgent) ? 300 : 100;
          setTimeout(() => {
            console.log('Dispatching resultsViewLoaded event');
            window.dispatchEvent(new Event('resultsViewLoaded'));
          }, delay);
        } else if (route === 'image-assessment') {
          // Delay for image assessment view
          const delay = /Mobi|Android/i.test(navigator.userAgent) ? 200 : 50;
          setTimeout(() => {
            console.log('Dispatching imageAssessmentViewLoaded event');
            window.dispatchEvent(new Event('imageAssessmentViewLoaded'));
            // Load and initialize the image assessment module
            loadImageAssessmentModule();
          }, delay);
        }

        console.log(`Successfully loaded route: ${route}`);
      } catch (e) {
        console.error(`Error loading route ${route}:`, e);
        container.innerHTML = `
          <div style="
            color: #e74c3c;
            text-align: center;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            margin: 20px;
            border-left: 4px solid #e74c3c;
          ">
            <h3>Failed to load view: ${route}</h3>
            <p>Error: ${e.message}</p>
            <button onclick="window.location.hash = ''" style="
              background: #3498db;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 4px;
              cursor: pointer;
            ">Go to Home</button>
          </div>
        `;
      }
    }
    function handleRouteChange() {
      // Use pathname for clean routing
      let route = window.location.pathname.replace(/^\//, '');
      if (route === '') route = '';
      else if (route === 'exam') route = 'exam';
      else if (route === 'results') route = 'results';
      else if (route === 'image-assessment') route = 'image-assessment';
      else route = '';
      console.log(`Route changed to: ${route}, User Agent: ${navigator.userAgent}`);
      loadRoute(route);
    }

    // Function to load and initialize the image assessment module
    async function loadImageAssessmentModule() {
      console.log('Loading image assessment module...');

      try {
        // Import the image assessment module
        const module = await import('./ImageAssessmentModule.js');

        // Initialize the image assessment functionality
        module.initializeImageAssessment();

        console.log('Image assessment module loaded and initialized successfully');

      } catch (error) {
        console.error('Error loading image assessment module:', error);

        // Fallback: Define basic functions to prevent errors
        window.handleImageUpload = function(action, input) {
          console.log('Fallback handleImageUpload called:', action);
          alert('Image upload functionality is not available. Please refresh the page and try again.');
        };

        window.retakeImage = function(action) {
          console.log('Fallback retakeImage called:', action);
        };

        window.startImageAnalysis = function() {
          console.log('Fallback startImageAnalysis called');
          window.history.pushState({}, '', '/results');
          window.dispatchEvent(new Event('popstate'));
        };

        window.goBackToHome = function() {
          console.log('Fallback goBackToHome called');
          window.history.pushState({}, '', '/');
          window.dispatchEvent(new Event('popstate'));
        };
      }
    }

    // Function to execute script tags in a container
    function executeScriptsInContainer(container) {
      console.log('Executing scripts in container...');

      const scripts = container.querySelectorAll('script');
      scripts.forEach(script => {
        try {
          console.log('Executing script tag...');
          const newScript = document.createElement('script');
          newScript.textContent = script.textContent;
          document.head.appendChild(newScript);
          document.head.removeChild(newScript);
        } catch (error) {
          console.error('Error executing script:', error);
        }
      });
    }

    // Define image assessment functions globally to ensure they're always available
    window.handleImageUpload = function(action, input) {
      console.log('handleImageUpload called:', action, input);
      if (input.files && input.files[0]) {
        if (window.imageManager && window.imageManager.processFile) {
          window.imageManager.processFile(input.files[0], action);
        } else {
          console.log('Image manager not ready, storing file for later processing');
          // Store the file for later processing
          if (!window.pendingUploads) window.pendingUploads = [];
          window.pendingUploads.push({ action, file: input.files[0] });
        }
      }
    };

    window.retakeImage = function(action) {
      console.log('retakeImage called:', action);
      if (window.imageManager && window.imageManager.retakeImage) {
        window.imageManager.retakeImage(action);
      }
    };

    window.startImageAnalysis = function() {
      console.log('startImageAnalysis called');
      if (window.imageManager && window.imageManager.startAnalysis) {
        window.imageManager.startAnalysis();
      } else {
        // Fallback: navigate to results
        window.history.pushState({}, '', '/results');
        window.dispatchEvent(new Event('popstate'));
      }
    };

    window.goBackToHome = function() {
      console.log('goBackToHome called');
      window.history.pushState({}, '', '/');
      window.dispatchEvent(new Event('popstate'));
    };

    console.log('Global image assessment functions defined');

    // Add mobile-specific error handling
    window.addEventListener('error', (e) => {
      console.error('Global error caught:', e.error, e.filename, e.lineno);
      if (/Mobi|Android/i.test(navigator.userAgent)) {
        console.log('Mobile device detected, error details:', {
          message: e.message,
          filename: e.filename,
          lineno: e.lineno,
          colno: e.colno,
          error: e.error
        });
      }
    });

    window.addEventListener('unhandledrejection', (e) => {
      console.error('Unhandled promise rejection:', e.reason);
      if (/Mobi|Android/i.test(navigator.userAgent)) {
        console.log('Mobile device promise rejection:', e.reason);
      }
    });

    window.addEventListener('popstate', handleRouteChange);
    document.addEventListener('DOMContentLoaded', () => {
      console.log('DOM Content Loaded, initializing router');
      handleRouteChange();
    });
  </script>
  <!-- MediaPipe for facial landmarks - Using more stable versions -->
  <script crossorigin="anonymous" src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils@0.3.1640029074/camera_utils.js"></script>
  <script crossorigin="anonymous" src="https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh@0.4.1633559619/face_mesh.js"></script>

  <!-- Debug MediaPipe loading -->
  <script>
    window.addEventListener('load', () => {
      console.log('Page loaded');
      console.log('FaceMesh available:', typeof window.FaceMesh);
      console.log('Camera available:', typeof window.Camera);

      // Check if getUserMedia is available
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        console.log('getUserMedia is available');
      } else {
        console.error('getUserMedia is not available');
      }
    });

    // Manual camera test function
    window.testCameraManually = async function() {
      console.log('=== Manual Camera Test ===');
      try {
        const video = document.getElementById('video');
        if (!video) {
          console.error('Video element not found');
          return;
        }

        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        video.srcObject = stream;

        // Force video properties
        video.style.display = 'block';
        video.style.visibility = 'visible';
        video.style.opacity = '1';
        video.style.width = '480px';
        video.style.height = '360px';
        video.style.background = 'red'; // Red background to see if video area is visible

        await video.play();

        console.log('Manual test SUCCESS - video should be visible with red background');
        console.log('Video state:', {
          srcObject: !!video.srcObject,
          videoWidth: video.videoWidth,
          videoHeight: video.videoHeight,
          readyState: video.readyState,
          paused: video.paused
        });
      } catch (err) {
        console.error('Manual test FAILED:', err);
      }
    };

    // Test MediaPipe directly
    window.testMediaPipeDirectly = async function() {
      console.log('=== MediaPipe Direct Test ===');
      try {
        if (!window.FaceMesh) {
          console.error('FaceMesh not available');
          return;
        }

        const faceMesh = new window.FaceMesh({
          locateFile: (file) => {
            const url = `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh@0.4.1633559619/${file}`;
            console.log('Test loading MediaPipe file:', url);
            return url;
          }
        });
        console.log('FaceMesh created successfully');

        faceMesh.setOptions({
          maxNumFaces: 1,
          refineLandmarks: true,
          minDetectionConfidence: 0.5,
          minTrackingConfidence: 0.5
        });

        faceMesh.onResults((results) => {
          console.log('MediaPipe test results:', results);
          if (results.multiFaceLandmarks && results.multiFaceLandmarks.length > 0) {
            console.log('SUCCESS: Face detected with', results.multiFaceLandmarks[0].length, 'landmarks');
          } else {
            console.log('No face detected in test');
          }
        });

        // Test with video element
        const video = document.getElementById('video');
        if (video && video.srcObject) {
          console.log('Testing with current video stream...');
          await faceMesh.send({ image: video });
        } else {
          console.log('No video stream available for test');
        }

        console.log('MediaPipe test setup complete');
      } catch (err) {
        console.error('MediaPipe test FAILED:', err);
      }
    };

    // Complete camera and MediaPipe test
    window.testCompleteSystem = async function() {
      console.log('=== Complete System Test ===');

      // Step 1: Test camera
      await window.testCameraManually();

      // Step 2: Wait a moment
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Step 3: Test MediaPipe
      await window.testMediaPipeDirectly();

      console.log('=== Complete System Test Done ===');
    };

    // Debug function to show current state
    window.showDebugInfo = function() {
      const debugDiv = document.getElementById('debugInfo');
      const debugContent = document.getElementById('debugContent');

      if (!debugDiv || !debugContent) return;

      const video = document.getElementById('video');
      const examView = document.getElementById('examView');
      const homeView = document.getElementById('homeView');

      let info = `
        <strong>Current State:</strong><br>
        - Home View Display: ${homeView ? homeView.style.display : 'not found'}<br>
        - Exam View Display: ${examView ? examView.style.display : 'not found'}<br>
        - Video Element: ${video ? 'found' : 'not found'}<br>
        - Video Display: ${video ? video.style.display : 'N/A'}<br>
        - Video Visibility: ${video ? video.style.visibility : 'N/A'}<br>
        - Video Opacity: ${video ? video.style.opacity : 'N/A'}<br>
        - Video Source: ${video && video.srcObject ? 'has stream' : 'no stream'}<br>
        - Video Ready State: ${video ? video.readyState : 'N/A'}<br>
        - Video Paused: ${video ? video.paused : 'N/A'}<br>
        - MediaPipe FaceMesh: ${typeof window.FaceMesh}<br>
        - MediaPipe Camera: ${typeof window.Camera}<br>
        - getUserMedia: ${navigator.mediaDevices && navigator.mediaDevices.getUserMedia ? 'available' : 'not available'}<br>
        - Current Hash: ${window.location.hash}<br>
      `;

      debugContent.innerHTML = info;
      debugDiv.style.display = 'block';
    };

    // Auto-show debug info when exam starts
    window.addEventListener('hashchange', () => {
      if (window.location.hash === '#/exam') {
        setTimeout(() => window.showDebugInfo(), 1000);
      }
    });
  </script>

  <!-- Main application -->
  <script>
    // Debug: Log when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
      console.log('[DEBUG] DOMContentLoaded');
      const video = document.getElementById('video');
      if (video) {
        console.log('[DEBUG] Video element found:', video);
      } else {
        console.error('[DEBUG] Video element NOT found');
      }
    });
    // Debug: Log when window is loaded
    window.addEventListener('load', () => {
      console.log('[DEBUG] Window loaded');
      const video = document.getElementById('video');
      if (video) {
        console.log('[DEBUG] Video element on window load:', video);
      }
    });
  </script>
  <script type="module" src="main.js"></script>
</body>
</html>
