// Presentation Service: Integration of Clinical Comparison with Facial Symmetry Application
// Provides left vs right movement comparison for clinical assessment

import { ClinicalComparisonService, ClinicalComparisonResult, DetailedComparisonReport, LandmarkPoint } from '../../domain/services/ClinicalComparisonService.js';

export interface ComparisonExaminationData {
  patientInfo: {
    id: string;
    name: string;
    age: number;
  };
  landmarkData: {
    baseline: any[];
    eyebrowRaise: any[];
    eyeClose: any[];
    smile: any[];
    // Note: Removed lipPucker and cheekPuff as per user preference
  };
  timestamp: string;
}

export interface EnhancedComparisonResult {
  individual_comparisons: {
    eyebrow_raise: ClinicalComparisonResult;
    eye_closure: ClinicalComparisonResult;
    smile: ClinicalComparisonResult;
    lip_pucker: ClinicalComparisonResult;
  };
  detailed_report: DetailedComparisonReport;
  summary_statistics: {
    total_actions_analyzed: number;
    actions_with_asymmetry: number;
    most_affected_side: string;
    average_asymmetry_percentage: number;
    severity_breakdown: Record<string, number>;
  };
  clinical_priority: 'low' | 'medium' | 'high' | 'urgent';
}

export class ClinicalComparisonIntegrationService {
  private clinicalComparisonService: ClinicalComparisonService;

  constructor() {
    this.clinicalComparisonService = new ClinicalComparisonService();
  }

  /**
   * Perform comprehensive left vs right comparison analysis
   */
  public async performClinicalComparison(
    examinationData: ComparisonExaminationData
  ): Promise<EnhancedComparisonResult> {
    
    console.log('Starting clinical left vs right comparison for patient:', examinationData.patientInfo.id);

    // Convert examination data to landmark points
    const landmarkData = this.convertToLandmarkData(examinationData.landmarkData);

    // Perform individual action comparisons
    const eyebrowComparison = this.clinicalComparisonService.compareSidesClinicalReport(
      'Eyebrow Raise',
      landmarkData.eyebrowRaise,
      landmarkData.baseline
    );

    const eyeComparison = this.clinicalComparisonService.compareSidesClinicalReport(
      'Eye Closure',
      landmarkData.eyeClose,
      landmarkData.baseline
    );

    const smileComparison = this.clinicalComparisonService.compareSidesClinicalReport(
      'Smile',
      landmarkData.smile,
      landmarkData.baseline
    );

    // Note: Removed lipPucker comparison as per user preference
    // Create empty comparison result for compatibility
    const lipPuckerComparison = {
      action: 'Lip Pucker (Removed)',
      left_movement: 0,
      right_movement: 0,
      absolute_difference: 0,
      percentage_difference: 0,
      affected_side: 'none' as const,
      severity: 'normal' as const,
      clinical_notes: 'Lip pucker analysis removed from examination protocol'
    };

    // Generate detailed report
    // Note: Removed lipPucker from detailed report as per user preference
    const detailedReport = this.clinicalComparisonService.generateDetailedComparisonReport(
      examinationData.patientInfo.id,
      {
        baseline: landmarkData.baseline,
        eyebrow_raise: landmarkData.eyebrowRaise,
        eye_closure: landmarkData.eyeClose,
        smile: landmarkData.smile,
        lip_pucker: [] // Empty array since lipPucker was removed
      }
    );

    // Calculate summary statistics
    const summaryStatistics = this.calculateSummaryStatistics([
      eyebrowComparison,
      eyeComparison,
      smileComparison,
      lipPuckerComparison
    ]);

    // Determine clinical priority
    const clinicalPriority = this.determineClinicalPriority(summaryStatistics, detailedReport);

    const result: EnhancedComparisonResult = {
      individual_comparisons: {
        eyebrow_raise: eyebrowComparison,
        eye_closure: eyeComparison,
        smile: smileComparison,
        lip_pucker: lipPuckerComparison
      },
      detailed_report: detailedReport,
      summary_statistics: summaryStatistics,
      clinical_priority: clinicalPriority
    };

    console.log('Clinical comparison completed:', result);
    return result;
  }

  /**
   * Convert landmark data to LandmarkPoint format
   * Note: Removed lipPucker as per user preference
   */
  private convertToLandmarkData(landmarkData: any): {
    baseline: LandmarkPoint[];
    eyebrowRaise: LandmarkPoint[];
    eyeClose: LandmarkPoint[];
    smile: LandmarkPoint[];
    lipPucker: LandmarkPoint[];
  } {
    return {
      baseline: this.convertToLandmarkPoints(landmarkData.baseline),
      eyebrowRaise: this.convertToLandmarkPoints(landmarkData.eyebrowRaise),
      eyeClose: this.convertToLandmarkPoints(landmarkData.eyeClose),
      smile: this.convertToLandmarkPoints(landmarkData.smile),
      lipPucker: [] // Empty array since lipPucker was removed
    };
  }

  /**
   * Convert array to LandmarkPoint array
   */
  private convertToLandmarkPoints(landmarks: any[]): LandmarkPoint[] {
    return landmarks.map(landmark => ({
      x: landmark.x || 0,
      y: landmark.y || 0,
      z: landmark.z || 0
    }));
  }

  /**
   * Calculate summary statistics from comparison results
   */
  private calculateSummaryStatistics(results: ClinicalComparisonResult[]): {
    total_actions_analyzed: number;
    actions_with_asymmetry: number;
    most_affected_side: string;
    average_asymmetry_percentage: number;
    severity_breakdown: Record<string, number>;
  } {
    
    const totalActions = results.length;
    const actionsWithAsymmetry = results.filter(r => r.affected_side !== 'none').length;

    // Count affected sides
    const sideCount = { left: 0, right: 0, bilateral: 0, none: 0 };
    results.forEach(result => {
      sideCount[result.affected_side]++;
    });

    // Determine most affected side
    let mostAffectedSide = 'none';
    let maxCount = sideCount.none;
    Object.entries(sideCount).forEach(([side, count]) => {
      if (count > maxCount) {
        maxCount = count;
        mostAffectedSide = side;
      }
    });

    // Calculate average asymmetry
    const asymmetryResults = results.filter(r => r.affected_side !== 'none');
    const averageAsymmetry = asymmetryResults.length > 0 ? 
      asymmetryResults.reduce((sum, r) => sum + r.percentage_difference, 0) / asymmetryResults.length : 0;

    // Count severity breakdown
    const severityBreakdown: Record<string, number> = { normal: 0, mild: 0, moderate: 0, severe: 0 };
    results.forEach(result => {
      severityBreakdown[result.severity]++;
    });

    return {
      total_actions_analyzed: totalActions,
      actions_with_asymmetry: actionsWithAsymmetry,
      most_affected_side: mostAffectedSide,
      average_asymmetry_percentage: Math.round(averageAsymmetry * 10) / 10,
      severity_breakdown: severityBreakdown
    };
  }

  /**
   * Determine clinical priority based on results
   */
  private determineClinicalPriority(
    summaryStats: any,
    detailedReport: DetailedComparisonReport
  ): 'low' | 'medium' | 'high' | 'urgent' {
    
    // Check for severe cases
    if (summaryStats.severity_breakdown.severe > 0) {
      return 'urgent';
    }

    // Check for high asymmetry
    if (summaryStats.average_asymmetry_percentage > 40) {
      return 'high';
    }

    // Check for moderate cases
    if (summaryStats.severity_breakdown.moderate > 1 || summaryStats.average_asymmetry_percentage > 20) {
      return 'high';
    }

    // Check for mild cases
    if (summaryStats.severity_breakdown.moderate > 0 || summaryStats.actions_with_asymmetry > 2) {
      return 'medium';
    }

    // Check for any asymmetry
    if (summaryStats.actions_with_asymmetry > 0) {
      return 'low';
    }

    return 'low';
  }

  /**
   * Generate clinical comparison report in text format
   */
  public generateComparisonSummary(result: EnhancedComparisonResult): string {
    let summary = `# Clinical Left vs Right Facial Movement Comparison\n\n`;
    
    summary += `## Summary Statistics\n`;
    summary += `- **Total Actions Analyzed:** ${result.summary_statistics.total_actions_analyzed}\n`;
    summary += `- **Actions with Asymmetry:** ${result.summary_statistics.actions_with_asymmetry}\n`;
    summary += `- **Most Affected Side:** ${result.summary_statistics.most_affected_side}\n`;
    summary += `- **Average Asymmetry:** ${result.summary_statistics.average_asymmetry_percentage}%\n`;
    summary += `- **Clinical Priority:** ${result.clinical_priority.toUpperCase()}\n\n`;

    summary += `## Individual Action Results\n\n`;

    Object.entries(result.individual_comparisons).forEach(([actionKey, comparison]) => {
      summary += `### ${comparison.action}\n`;
      summary += `- **Left Movement:** ${comparison.left_movement}\n`;
      summary += `- **Right Movement:** ${comparison.right_movement}\n`;
      summary += `- **Difference:** ${comparison.percentage_difference}% (${comparison.severity})\n`;
      summary += `- **Affected Side:** ${comparison.affected_side}\n`;
      summary += `- **Notes:** ${comparison.clinical_notes}\n\n`;
    });

    summary += `## Clinical Assessment\n\n`;
    summary += `- **Primary Affected Side:** ${result.detailed_report.overall_assessment.primary_affected_side}\n`;
    summary += `- **Average Asymmetry:** ${result.detailed_report.overall_assessment.average_asymmetry}%\n\n`;

    summary += `### Severity Distribution\n\n`;
    Object.entries(result.detailed_report.overall_assessment.severity_distribution).forEach(([severity, count]) => {
      summary += `- **${severity.charAt(0).toUpperCase() + severity.slice(1)}:** ${count} actions\n`;
    });

    return summary;
  }

  /**
   * Export comparison results to CSV
   */
  public exportComparisonToCSV(result: EnhancedComparisonResult): string {
    return this.clinicalComparisonService.exportToCSV(result.detailed_report);
  }

  /**
   * Export comparison results to Markdown
   */
  public exportComparisonToMarkdown(result: EnhancedComparisonResult): string {
    return this.clinicalComparisonService.exportToMarkdown(result.detailed_report);
  }

  /**
   * Get priority color for UI display
   */
  public getPriorityColor(priority: string): string {
    switch (priority) {
      case 'urgent': return '#e74c3c';  // Red
      case 'high': return '#e67e22';    // Orange
      case 'medium': return '#f39c12';  // Yellow
      case 'low': return '#27ae60';     // Green
      default: return '#95a5a6';        // Gray
    }
  }

  /**
   * Get severity color for UI display
   */
  public getSeverityColor(severity: string): string {
    switch (severity) {
      case 'severe': return '#e74c3c';     // Red
      case 'moderate': return '#e67e22';   // Orange
      case 'mild': return '#f39c12';       // Yellow
      case 'normal': return '#27ae60';     // Green
      default: return '#95a5a6';           // Gray
    }
  }
}
