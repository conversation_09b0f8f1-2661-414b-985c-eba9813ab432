// Domain Entity: Exam Session
import { Patient } from './Patient.js';
import { ExamAction } from './ExamAction.js';
import { MetricResult } from '../value-objects/MetricResult.js';

export class ExamSession {
  private _currentActionIndex: number = 0;
  private _isStarted: boolean = false;
  private _results: MetricResult[] = [];

  constructor(
    public readonly patient: Patient,
    public readonly actions: ExamAction[]
  ) {}

  start(): void {
    this._isStarted = true;
    this._currentActionIndex = 0;
  }

  getCurrentAction(): ExamAction | null {
    if (this._currentActionIndex >= this.actions.length) {
      return null;
    }
    return this.actions[this._currentActionIndex];
  }

  nextAction(): boolean {
    if (this._currentActionIndex < this.actions.length - 1) {
      this._currentActionIndex++;
      return true;
    }
    return false;
  }

  addResult(result: MetricResult): void {
    this._results.push(result);
  }

  getResults(): MetricResult[] {
    return [...this._results];
  }

  isCompleted(): boolean {
    return this._currentActionIndex >= this.actions.length;
  }

  get isStarted(): boolean {
    return this._isStarted;
  }

  get currentActionIndex(): number {
    return this._currentActionIndex;
  }
}
