<div id="profileView" class="profile-container">
  <!-- Navigation Header -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <div class="brand-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z" fill="currentColor"/>
          </svg>
        </div>
        <span class="brand-text">Facial Symmetry Analysis</span>
      </div>
      <div class="nav-actions">
        <button class="nav-btn" onclick="goBackToHome()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" fill="currentColor"/>
          </svg>
          Back to Home
        </button>
      </div>
    </div>
  </nav>

  <!-- Profile Section -->
  <section class="profile-section">
    <div class="profile-container-inner">
      <!-- Test Notice -->
      <div class="test-notice">
        <div class="notice-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="notice-content">
          <h4>Test Profile</h4>
          <p>This is a temporary test profile that will be deleted in the future.</p>
        </div>
      </div>

      <!-- Profile Header -->
      <div class="profile-header">
        <div class="profile-image-container">
          <img 
            src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face&auto=format&q=80" 
            alt="Tareq Attiya Profile Picture" 
            class="profile-image"
            onerror="this.src='https://via.placeholder.com/300x300/3b82f6/ffffff?text=TA'"
          />
          <div class="profile-status">
            <span class="status-dot"></span>
            Active
          </div>
        </div>
        
        <div class="profile-info">
          <h1 class="profile-name">Tareq Attiya</h1>
          <p class="profile-title">Software Engineer & Medical Technology Researcher</p>
          <div class="profile-badges">
            <span class="badge badge-primary">Developer</span>
            <span class="badge badge-secondary">Researcher</span>
            <span class="badge badge-success">Test Profile</span>
          </div>
        </div>
      </div>

      <!-- Profile Details -->
      <div class="profile-details">
        <div class="details-grid">
          <!-- Personal Information -->
          <div class="detail-card">
            <div class="card-header">
              <div class="card-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" fill="currentColor"/>
                </svg>
              </div>
              <h3>Personal Information</h3>
            </div>
            <div class="card-content">
              <div class="info-row">
                <span class="info-label">Full Name:</span>
                <span class="info-value">Tareq Attiya</span>
              </div>
              <div class="info-row">
                <span class="info-label">Age:</span>
                <span class="info-value">32 years</span>
              </div>
              <div class="info-row">
                <span class="info-label">Location:</span>
                <span class="info-value">San Francisco, CA</span>
              </div>
              <div class="info-row">
                <span class="info-label">Joined:</span>
                <span class="info-value">January 2024</span>
              </div>
            </div>
          </div>

          <!-- Professional Information -->
          <div class="detail-card">
            <div class="card-header">
              <div class="card-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20,6C20.58,6 21.05,6.2 21.42,6.59C21.8,7 22,7.45 22,8V19C22,19.55 21.8,20 21.42,20.41C21.05,20.8 20.58,21 20,21H4C3.42,21 2.95,20.8 2.58,20.41C2.2,20 2,19.55 2,19V8C2,7.45 2.2,7 2.58,6.59C2.95,6.2 3.42,6 4,6H8V4C8,3.42 8.2,2.95 8.58,2.58C8.95,2.2 9.42,2 10,2H14C14.58,2 15.05,2.2 15.42,2.58C15.8,2.95 16,3.42 16,4V6H20M4,8V19H20V8H4M10,4V6H14V4H10Z" fill="currentColor"/>
                </svg>
              </div>
              <h3>Professional Details</h3>
            </div>
            <div class="card-content">
              <div class="info-row">
                <span class="info-label">Position:</span>
                <span class="info-value">Senior Software Engineer</span>
              </div>
              <div class="info-row">
                <span class="info-label">Department:</span>
                <span class="info-value">Medical Technology</span>
              </div>
              <div class="info-row">
                <span class="info-label">Specialization:</span>
                <span class="info-value">Computer Vision & AI</span>
              </div>
              <div class="info-row">
                <span class="info-label">Experience:</span>
                <span class="info-value">8+ years</span>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="detail-card">
            <div class="card-header">
              <div class="card-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4M20,8L12,13L4,8V6L12,11L20,6V8Z" fill="currentColor"/>
                </svg>
              </div>
              <h3>Contact Information</h3>
            </div>
            <div class="card-content">
              <div class="info-row">
                <span class="info-label">Email:</span>
                <span class="info-value"><EMAIL></span>
              </div>
              <div class="info-row">
                <span class="info-label">Phone:</span>
                <span class="info-value">+****************</span>
              </div>
              <div class="info-row">
                <span class="info-label">LinkedIn:</span>
                <span class="info-value">linkedin.com/in/tareqattiya</span>
              </div>
              <div class="info-row">
                <span class="info-label">GitHub:</span>
                <span class="info-value">github.com/tareqattiya</span>
              </div>
            </div>
          </div>

          <!-- Skills & Expertise -->
          <div class="detail-card">
            <div class="card-header">
              <div class="card-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9,5V9H21V5M9,19H21V15H9M9,14H21V10H9M4,9H8L6,7L4,9M4,19H8L6,17L4,19M4,14H8L6,12L4,14Z" fill="currentColor"/>
                </svg>
              </div>
              <h3>Skills & Expertise</h3>
            </div>
            <div class="card-content">
              <div class="skills-grid">
                <span class="skill-tag">JavaScript</span>
                <span class="skill-tag">TypeScript</span>
                <span class="skill-tag">Python</span>
                <span class="skill-tag">Computer Vision</span>
                <span class="skill-tag">Machine Learning</span>
                <span class="skill-tag">Medical Imaging</span>
                <span class="skill-tag">React</span>
                <span class="skill-tag">Node.js</span>
                <span class="skill-tag">TensorFlow</span>
                <span class="skill-tag">OpenCV</span>
                <span class="skill-tag">MediaPipe</span>
                <span class="skill-tag">Healthcare Tech</span>
              </div>
            </div>
          </div>

          <!-- Projects -->
          <div class="detail-card full-width">
            <div class="card-header">
              <div class="card-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z" fill="currentColor"/>
                </svg>
              </div>
              <h3>Recent Projects</h3>
            </div>
            <div class="card-content">
              <div class="projects-list">
                <div class="project-item">
                  <div class="project-info">
                    <h4>Facial Symmetry Analysis Platform</h4>
                    <p>Advanced computer vision system for Bell's palsy assessment using MediaPipe and clinical grading standards.</p>
                    <div class="project-tags">
                      <span class="project-tag">TypeScript</span>
                      <span class="project-tag">MediaPipe</span>
                      <span class="project-tag">Medical AI</span>
                    </div>
                  </div>
                  <div class="project-status">
                    <span class="status-badge active">Active</span>
                  </div>
                </div>
                
                <div class="project-item">
                  <div class="project-info">
                    <h4>Medical Image Processing Toolkit</h4>
                    <p>Open-source toolkit for medical image analysis and processing with focus on accessibility and ease of use.</p>
                    <div class="project-tags">
                      <span class="project-tag">Python</span>
                      <span class="project-tag">OpenCV</span>
                      <span class="project-tag">Healthcare</span>
                    </div>
                  </div>
                  <div class="project-status">
                    <span class="status-badge completed">Completed</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="profile-actions">
        <button class="action-btn primary" onclick="goBackToHome()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z" fill="currentColor"/>
          </svg>
          Back to Home
        </button>
        <button class="action-btn secondary" onclick="alert('This is a test profile - Contact feature not available')">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4M20,8L12,13L4,8V6L12,11L20,6V8Z" fill="currentColor"/>
          </svg>
          Contact
        </button>
      </div>
    </div>
  </section>
</div>

<style>
/* Profile Page Styles */
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: #1a202c;
  line-height: 1.6;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-icon {
  color: #3b82f6;
  display: flex;
  align-items: center;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
}

.nav-btn {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

/* Test Notice */
.test-notice {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notice-icon {
  color: #d97706;
  flex-shrink: 0;
}

.notice-content h4 {
  font-size: 1rem;
  font-weight: 700;
  color: #92400e;
  margin: 0 0 0.25rem 0;
}

.notice-content p {
  font-size: 0.875rem;
  color: #a16207;
  margin: 0;
}

/* Profile Section */
.profile-section {
  padding: 2rem 0 4rem 0;
}

.profile-container-inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Profile Header */
.profile-header {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.8);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.profile-image-container {
  position: relative;
  flex-shrink: 0;
}

.profile-image {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #3b82f6;
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.3);
}

.profile-status {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: white;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  color: #059669;
  border: 2px solid #059669;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #059669;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.profile-title {
  font-size: 1.25rem;
  color: #64748b;
  margin: 0 0 1rem 0;
}

.profile-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.badge-secondary {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
}

.badge-success {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
}

/* Profile Details */
.profile-details {
  margin-bottom: 2rem;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.detail-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
}

.detail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.detail-card.full-width {
  grid-column: 1 / -1;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.card-icon {
  color: #3b82f6;
  display: flex;
  align-items: center;
}

.card-header h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: #64748b;
  font-size: 0.875rem;
}

.info-value {
  color: #1a202c;
  font-weight: 500;
  text-align: right;
}

/* Skills Grid */
.skills-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.skill-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.skill-tag:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Projects */
.projects-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.project-info {
  flex: 1;
}

.project-info h4 {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.project-info p {
  color: #64748b;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.project-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.project-tag {
  background: #e2e8f0;
  color: #475569;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 500;
}

.project-status {
  flex-shrink: 0;
  margin-left: 1rem;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
}

.status-badge.completed {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
}

/* Profile Actions */
.profile-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.action-btn {
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.4);
}

.action-btn.secondary {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.action-btn.secondary:hover {
  background: #e2e8f0;
  color: #475569;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .details-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .profile-container-inner {
    padding: 0 1rem;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .profile-name {
    font-size: 2rem;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .detail-card {
    padding: 1.5rem;
  }

  .project-item {
    flex-direction: column;
    gap: 1rem;
  }

  .project-status {
    margin-left: 0;
    align-self: flex-start;
  }

  .profile-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .profile-header {
    padding: 1.5rem;
  }

  .profile-image {
    width: 120px;
    height: 120px;
  }

  .profile-name {
    font-size: 1.75rem;
  }

  .detail-card {
    padding: 1rem;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .info-value {
    text-align: left;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>

<script>
// Navigation function
function goBackToHome() {
  window.history.pushState({}, '', '/');
  window.dispatchEvent(new Event('popstate'));
}

// Initialize profile page
document.addEventListener('DOMContentLoaded', function() {
  console.log('Tareq Attiya profile page loaded');
  
  // Add some interactive effects
  const detailCards = document.querySelectorAll('.detail-card');
  detailCards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-4px)';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
    });
  });

  // Add click effect to skill tags
  const skillTags = document.querySelectorAll('.skill-tag');
  skillTags.forEach(tag => {
    tag.addEventListener('click', function() {
      this.style.transform = 'scale(0.95)';
      setTimeout(() => {
        this.style.transform = 'scale(1)';
      }, 150);
    });
  });
});
</script>
