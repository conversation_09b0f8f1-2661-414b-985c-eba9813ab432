# Clean Architecture Implementation

This project has been restructured to follow Clean Architecture principles, providing better separation of concerns, testability, and maintainability.

## Architecture Overview

The application is organized into four main layers:

### 1. Domain Layer (`src/domain/`)
Contains the core business logic and rules, independent of external concerns.

#### Entities (`src/domain/entities/`)
- **Patient.ts** - Patient information and validation
- **ExamSession.ts** - Exam session management and state
- **ExamAction.ts** - Individual exam actions/instructions

#### Value Objects (`src/domain/value-objects/`)
- **MetricResult.ts** - Facial symmetry measurement results
- **FacialMetrics.ts** - Facial measurement data

#### Domain Services (`src/domain/services/`)
- **MetricCalculationService.ts** - Core facial metric calculations

#### Repository Interfaces (`src/domain/repositories/`)
- **IExamRepository.ts** - Exam data persistence interface
- **ICameraRepository.ts** - Camera operations interface

### 2. Application Layer (`src/application/`)
Orchestrates the domain logic and coordinates between layers.

#### Use Cases (`src/application/use-cases/`)
- **StartExamUseCase.ts** - Initialize and start an exam session
- **ProcessFacialDataUseCase.ts** - Process camera data and calculate metrics
- **ExportResultsUseCase.ts** - Export exam results in various formats

#### Application Services (`src/application/services/`)
- **ExamOrchestrator.ts** - Coordinates exam flow and use cases

### 3. Infrastructure Layer (`src/infrastructure/`)
Handles external concerns and implements repository interfaces.

#### Repositories (`src/infrastructure/repositories/`)
- **ExamRepository.ts** - In-memory exam data storage
- **CameraRepository.ts** - MediaPipe camera integration

#### Configuration (`src/infrastructure/config/`)
- **ExamActionsConfig.ts** - Default exam actions configuration

#### Dependency Injection (`src/infrastructure/di/`)
- **DependencyContainer.ts** - IoC container for dependency management

### 4. Presentation Layer (`src/presentation/`)
Handles user interface and user interactions.

#### Controllers (`src/presentation/controllers/`)
- **ExamController.ts** - Manages exam UI interactions

#### Components (`src/presentation/components/`)
- **PatientForm.ts** - Patient information form component

#### Application (`src/presentation/app/`)
- **FacialSymmetryApp.ts** - Main application entry point

## Key Benefits

### 1. **Separation of Concerns**
Each layer has a specific responsibility and doesn't depend on implementation details of other layers.

### 2. **Dependency Inversion**
High-level modules don't depend on low-level modules. Both depend on abstractions.

### 3. **Testability**
Business logic is isolated and can be tested independently of UI and external dependencies.

### 4. **Maintainability**
Changes to external dependencies (camera, storage) don't affect business logic.

### 5. **Flexibility**
Easy to swap implementations (e.g., different camera libraries, storage mechanisms).

## Dependency Flow

```
Presentation → Application → Domain
     ↓              ↓
Infrastructure ←────┘
```

- **Presentation** depends on Application
- **Application** depends on Domain
- **Infrastructure** implements Domain interfaces
- **Domain** has no dependencies on other layers

## Usage

The application is initialized through the main entry point:

```typescript
// main-new.ts
import { FacialSymmetryApp } from './src/presentation/app/FacialSymmetryApp.js';

document.addEventListener('DOMContentLoaded', () => {
  const app = new FacialSymmetryApp();
  app.initialize();
});
```

The `FacialSymmetryApp` uses the `DependencyContainer` to wire up all dependencies and provides a clean interface for the application.

## Migration Notes

- Legacy files in `src/` (camera.ts, metrics.ts, etc.) can be gradually removed
- The new architecture maintains the same functionality while providing better structure
- All business logic is now properly encapsulated in the domain layer
- External dependencies are abstracted through repository interfaces
