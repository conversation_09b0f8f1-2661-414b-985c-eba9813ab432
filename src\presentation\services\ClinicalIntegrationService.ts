// Presentation Service: Integration of Clinical Analysis with Facial Symmetry Application
// Bridges the clinical analysis service with the existing examination workflow

import { ClinicalAnalysisService, MovementData, PatientMetadata, ClinicalAnalysisResult, LandmarkPoint } from '../../domain/services/ClinicalAnalysisService.js';
import { Landmark } from '../../shared/types/index.js';

export interface ClinicalExaminationData {
  patientInfo: {
    id: string;
    name: string;
    age: number;
    affectedSide?: 'left' | 'right' | 'bilateral' | 'unknown';
  };
  landmarkData: {
    baseline: Landmark[];
    eyebrowRaise: Landmark[];
    eyeClose: Landmark[];
    smile: Landmark[];
    // Note: Removed lipPucker and cheekPuff as per user preference
  };
  timestamp: string;
}

export interface EnhancedClinicalResult extends ClinicalAnalysisResult {
  visualization_data: {
    affected_regions: string[];
    asymmetry_heatmap: number[];
    movement_vectors: Array<{
      landmark_index: number;
      displacement: { x: number; y: number };
      severity: string;
    }>;
  };
  statistical_confidence: {
    asymmetry_p_value: number;
    confidence_interval: { lower: number; upper: number };
    measurement_reliability: number;
  };
}

export class ClinicalIntegrationService {
  private clinicalAnalysisService: ClinicalAnalysisService;

  constructor() {
    this.clinicalAnalysisService = new ClinicalAnalysisService();
  }

  /**
   * Perform comprehensive clinical analysis on examination data
   * Mouth analysis related code is primarily within this method:
   * Lines approx: 30-90
   */
  public async performClinicalAnalysis(
    examinationData: ClinicalExaminationData
  ): Promise<EnhancedClinicalResult> {

    console.log('Starting clinical analysis for patient:', examinationData.patientInfo.id);

    // Convert examination data to clinical analysis format
    const movementData = this.convertToMovementData(examinationData.landmarkData);
    const baselineData = this.convertToLandmarkPoints(examinationData.landmarkData.baseline);
    const patientMetadata = this.convertToPatientMetadata(examinationData.patientInfo, examinationData.timestamp);

    // Validate input data
    const validation = this.clinicalAnalysisService.validateInputData(
      movementData,
      baselineData,
      patientMetadata
    );

    if (!validation.valid) {
      throw new Error(`Clinical analysis validation failed: ${validation.errors.join(', ')}`);
    }

    // Perform core clinical analysis
    const clinicalResult = this.clinicalAnalysisService.analyzeFacialAsymmetry(
      movementData,
      baselineData,
      patientMetadata
    );

    // Calculate vertical and horizontal distances for mouth corners
    const verticalHorizontalDistances = this.calculateVerticalHorizontalDistances(baselineData);

    // Generate enhanced visualization data
    const visualizationData = this.generateVisualizationData(
      movementData,
      baselineData,
      clinicalResult
    );

    // Calculate statistical confidence metrics
    const statisticalConfidence = this.calculateStatisticalConfidence(
      movementData,
      baselineData,
      clinicalResult
    );

    // Combine results with added vertical and horizontal distances
    const enhancedResult: EnhancedClinicalResult & { symmetryMetrics?: Record<string, unknown> } = {
      ...clinicalResult,
      visualization_data: visualizationData,
      statistical_confidence: statisticalConfidence,
      symmetryMetrics: verticalHorizontalDistances
    };

    console.log('Clinical analysis completed.');
    return enhancedResult;
  }

  /**
   * Calculate vertical and horizontal distances for mouth corners relative to reference lines
   */
  private calculateVerticalHorizontalDistances(baselineData: LandmarkPoint[]): { 
    leftVerticalDistance: number; 
    rightVerticalDistance: number; 
    leftHorizontalDistance: number; 
    rightHorizontalDistance: number; 
    verticalAsymmetry: number; 
    horizontalAsymmetry: number; 
  } {
    // Reference landmarks
    const leftEyeInner = baselineData[133];
    const rightEyeInner = baselineData[362];
    const chinTip = baselineData[152];
    const leftMouthCorner = baselineData[61];
    const rightMouthCorner = baselineData[291];

    if (!leftEyeInner || !rightEyeInner || !chinTip || !leftMouthCorner || !rightMouthCorner) {
      return {
        leftVerticalDistance: 0,
        rightVerticalDistance: 0,
        leftHorizontalDistance: 0,
        rightHorizontalDistance: 0,
        verticalAsymmetry: 0,
        horizontalAsymmetry: 0
      };
    }

    // Calculate vertical reference line midpoint
    const eyeMidpoint = {
      x: (leftEyeInner.x + rightEyeInner.x) / 2,
      y: (leftEyeInner.y + rightEyeInner.y) / 2
    };

    // Calculate vertical distances (perpendicular to vertical reference line)
    const verticalLeftDistance = this.calculatePerpendicularDistance(leftMouthCorner, eyeMidpoint, chinTip);
    const verticalRightDistance = this.calculatePerpendicularDistance(rightMouthCorner, eyeMidpoint, chinTip);

    // Calculate horizontal distances (perpendicular to horizontal reference line)
    // Horizontal reference line is tilted to match eye line angle
    const deltaY = rightEyeInner.y - leftEyeInner.y;
    const deltaX = rightEyeInner.x - leftEyeInner.x;
    const eyeAngle = Math.atan2(deltaY, deltaX);

    // Calculate horizontal reference line endpoints centered at chin tip
    const chinTipCoords = chinTip;
    const lineLength = 0.6; // normalized length, arbitrary scale
    const halfLength = lineLength / 2;

    const leftEnd = {
      x: chinTipCoords.x - halfLength * Math.cos(eyeAngle),
      y: chinTipCoords.y - halfLength * Math.sin(eyeAngle)
    };

    const rightEnd = {
      x: chinTipCoords.x + halfLength * Math.cos(eyeAngle),
      y: chinTipCoords.y + halfLength * Math.sin(eyeAngle)
    };

    // Calculate horizontal distances (perpendicular to horizontal reference line)
    const horizontalLeftDistance = this.calculatePerpendicularDistance(leftMouthCorner, leftEnd, rightEnd);
    const horizontalRightDistance = this.calculatePerpendicularDistance(rightMouthCorner, leftEnd, rightEnd);

    // Calculate asymmetry percentages
    const maxVertical = Math.max(verticalLeftDistance, verticalRightDistance);
    const verticalAsymmetry = maxVertical > 0 ? (maxVertical - Math.min(verticalLeftDistance, verticalRightDistance)) / maxVertical : 0;

    const maxHorizontal = Math.max(horizontalLeftDistance, horizontalRightDistance);
    const horizontalAsymmetry = maxHorizontal > 0 ? (maxHorizontal - Math.min(horizontalLeftDistance, horizontalRightDistance)) / maxHorizontal : 0;

    return {
      leftVerticalDistance: verticalLeftDistance,
      rightVerticalDistance: verticalRightDistance,
      leftHorizontalDistance: horizontalLeftDistance,
      rightHorizontalDistance: horizontalRightDistance,
      verticalAsymmetry,
      horizontalAsymmetry
    };
  }

  /**
   * Calculate perpendicular distance from a point to a line defined by two points
   */
  private calculatePerpendicularDistance(point: LandmarkPoint, linePoint1: LandmarkPoint, linePoint2: LandmarkPoint): number {
    const x0 = point.x;
    const y0 = point.y;
    const x1 = linePoint1.x;
    const y1 = linePoint1.y;
    const x2 = linePoint2.x;
    const y2 = linePoint2.y;

    const numerator = Math.abs((y2 - y1)*x0 - (x2 - x1)*y0 + x2*y1 - y2*x1);
    const denominator = Math.sqrt(Math.pow(y2 - y1, 2) + Math.pow(x2 - x1, 2));

    return denominator !== 0 ? numerator / denominator : 0;
  }

  /**
   * Convert landmark data to movement data format
   * Note: Removed lipPucker and cheekPuff as per user preference
   */
  private convertToMovementData(landmarkData: Record<string, unknown>): MovementData {
    return {
      eyebrow_raise: this.convertToLandmarkPoints(landmarkData.eyebrowRaise as Landmark[]),
      eye_close: this.convertToLandmarkPoints(landmarkData.eyeClose as Landmark[]),
      smile: this.convertToLandmarkPoints(landmarkData.smile as Landmark[]),
    };
  }

  /**
   * Convert Landmark array to LandmarkPoint array
   */
  private convertToLandmarkPoints(landmarks: Landmark[]): LandmarkPoint[] {
    return landmarks.map(landmark => ({
      x: landmark.x,
      y: landmark.y,
      z: landmark.z
    }));
  }

  /**
   * Convert patient info to metadata format
   */
  private convertToPatientMetadata(patientInfo: Record<string, unknown>, timestamp: string): PatientMetadata {
    return {
      patient_id: patientInfo.id as string,
      age: parseInt(patientInfo.age as string) || 0,
      affected_side: (patientInfo.affectedSide as "left" | "right" | "bilateral" | "unknown") || 'unknown',
      examination_date: timestamp
    };
  }

  /**
   * Generate visualization data for clinical results
   */
  private generateVisualizationData(
    movementData: MovementData,
    baselineData: LandmarkPoint[],
    clinicalResult: ClinicalAnalysisResult
  ): {
    affected_regions: string[];
    asymmetry_heatmap: number[];
    movement_vectors: Array<{
      landmark_index: number;
      displacement: { x: number; y: number };
      severity: string;
    }>;
  } {

    // Identify affected regions based on analysis
    const affectedRegions: string[] = [];
    if (clinicalResult.regional_analysis.forehead.severity_grade !== 'Normal') {
      affectedRegions.push('forehead');
    }
    if (clinicalResult.regional_analysis.eye.severity_grade !== 'Normal') {
      affectedRegions.push('eye');
    }
    if (clinicalResult.regional_analysis.smile.severity_grade !== 'Normal') {
      affectedRegions.push('mouth');
    }

    // Generate asymmetry heatmap (simplified version)
    const asymmetryHeatmap = this.generateAsymmetryHeatmap(movementData, baselineData);

    // Calculate movement vectors for key landmarks
    const movementVectors = this.calculateMovementVectors(movementData, baselineData);

    return {
      affected_regions: affectedRegions,
      asymmetry_heatmap: asymmetryHeatmap,
      movement_vectors: movementVectors
    };
  }

  /**
   * Generate asymmetry heatmap data
   */
  private generateAsymmetryHeatmap(
    movementData: MovementData,
    baselineData: LandmarkPoint[]
  ): number[] {
    const heatmap: number[] = new Array(468).fill(0);

    // Calculate movement intensity for each landmark across all actions
    Object.values(movementData).forEach((actionData: LandmarkPoint[]) => {
      actionData.forEach((point: LandmarkPoint, index: number) => {
        if (baselineData[index]) {
          const movement = Math.sqrt(
            Math.pow(point.x - baselineData[index].x, 2) +
            Math.pow(point.y - baselineData[index].y, 2)
          );
          heatmap[index] += movement;
        }
      });
    });

    // Normalize heatmap values
    const maxValue = Math.max(...heatmap);
    return heatmap.map(value => maxValue > 0 ? value / maxValue : 0);
  }

  /**
   * Calculate movement vectors for key landmarks
   */
  private calculateMovementVectors(
    movementData: MovementData,
    baselineData: LandmarkPoint[]
  ): Array<{
    landmark_index: number;
    displacement: { x: number; y: number };
    severity: string;
  }> {

    const keyLandmarks = [
      // Eyebrow landmarks
      70, 63, 105, 66, 107, 300, 293, 334, 296, 336,
      // Eye landmarks
      33, 133, 159, 145, 362, 263, 386, 374,
      // Mouth landmarks
      61, 291, 13, 12, 15
    ];

    const vectors: Array<{
      landmark_index: number;
      displacement: { x: number; y: number };
      severity: string;
    }> = [];

    // Calculate average displacement across all actions for key landmarks
    keyLandmarks.forEach(landmarkIndex => {
      let totalDisplacementX = 0;
      let totalDisplacementY = 0;
      let actionCount = 0;

      Object.values(movementData).forEach((actionData: LandmarkPoint[]) => {
        if (actionData[landmarkIndex] && baselineData[landmarkIndex]) {
          totalDisplacementX += actionData[landmarkIndex].x - baselineData[landmarkIndex].x;
          totalDisplacementY += actionData[landmarkIndex].y - baselineData[landmarkIndex].y;
          actionCount++;
        }
      });

      if (actionCount > 0) {
        const avgDisplacementX = totalDisplacementX / actionCount;
        const avgDisplacementY = totalDisplacementY / actionCount;
        const magnitude = Math.sqrt(avgDisplacementX * avgDisplacementX + avgDisplacementY * avgDisplacementY);

        let severity = 'Normal';
        if (magnitude > 0.05) severity = 'Severe';
        else if (magnitude > 0.03) severity = 'Moderate';
        else if (magnitude > 0.01) severity = 'Mild';

        vectors.push({
          landmark_index: landmarkIndex,
          displacement: { x: avgDisplacementX, y: avgDisplacementY },
          severity
        });
      }
    });

    return vectors;
  }

  /**
   * Calculate statistical confidence metrics
   */
  private calculateStatisticalConfidence(
    movementData: MovementData,
    baselineData: LandmarkPoint[],
    clinicalResult: ClinicalAnalysisResult
  ): {
    asymmetry_p_value: number;
    confidence_interval: { lower: number; upper: number };
    measurement_reliability: number;
  } {

    // Simplified statistical calculations
    // In a real implementation, these would use proper statistical methods

    const asymmetryIndex = clinicalResult.composite_scores.facial_asymmetry_index;

    // Mock p-value calculation (would use proper statistical test)
    const asymmetryPValue = asymmetryIndex > 0.15 ? 0.001 : asymmetryIndex > 0.05 ? 0.05 : 0.2;

    // Mock confidence interval (would use proper statistical methods)
    const margin = asymmetryIndex * 0.1;
    const confidenceInterval = {
      lower: Math.max(0, asymmetryIndex - margin),
      upper: Math.min(1, asymmetryIndex + margin)
    };

    // Mock measurement reliability (would be based on landmark detection confidence)
    const measurementReliability = 0.95 - (asymmetryIndex * 0.1);

    return {
      asymmetry_p_value: asymmetryPValue,
      confidence_interval: confidenceInterval,
      measurement_reliability: Math.max(0.7, measurementReliability)
    };
  }

  /**
   * Generate clinical report in markdown format
   */
  public generateClinicalReport(result: EnhancedClinicalResult): string {
    const report = `
# Clinical Facial Symmetry Analysis Report

## Patient Information
- **Patient ID:** ${result.patient_id}
- **Examination Date:** ${new Date(result.examination_date).toLocaleDateString()}
- **Affected Side:** ${result.affected_side}

## Clinical Assessment

### Regional Analysis

#### Forehead/Eyebrow Region
- **Asymmetry:** ${result.regional_analysis.forehead.asymmetry_percentage.toFixed(1)}%
- **Severity:** ${result.regional_analysis.forehead.severity_grade}
- **Affected Side:** ${result.regional_analysis.forehead.affected_side}

#### Eye Region
- **Asymmetry:** ${result.regional_analysis.eye.asymmetry_percentage.toFixed(1)}%
- **Severity:** ${result.regional_analysis.eye.severity_grade}
- **Lagophthalmos:** ${result.regional_analysis.eye.lagophthalmos_present ? 'Present' : 'Absent'}
- **Affected Side:** ${result.regional_analysis.eye.affected_side}

#### Smile/Mouth Region
- **Asymmetry:** ${result.regional_analysis.smile.asymmetry_percentage.toFixed(1)}%
- **Severity:** ${result.regional_analysis.smile.severity_grade}
- **Commissure Droop:** ${result.regional_analysis.smile.commissure_droop?.toFixed(1)}%
- **Affected Side:** ${result.regional_analysis.smile.affected_side}

### Composite Scores
- **Facial Asymmetry Index:** ${result.composite_scores.facial_asymmetry_index.toFixed(3)}
- **Functional Impairment Score:** ${result.composite_scores.functional_impairment_score.toFixed(1)}/100

### Statistical Confidence
- **Asymmetry P-value:** ${result.statistical_confidence.asymmetry_p_value.toFixed(3)}
- **Confidence Interval:** ${result.statistical_confidence.confidence_interval.lower.toFixed(3)} - ${result.statistical_confidence.confidence_interval.upper.toFixed(3)}
- **Measurement Reliability:** ${(result.statistical_confidence.measurement_reliability * 100).toFixed(1)}%

---
*Report generated by Clinical Facial Symmetry Analysis System*
*Date: ${new Date().toLocaleDateString()}*
    `;

    return report.trim();
  }

}
