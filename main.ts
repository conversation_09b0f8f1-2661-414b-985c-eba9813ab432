// Main entry point - Clean Architecture implementation
import { FacialSymmetryApp } from './src/presentation/app/FacialSymmetryApp.js';


// SPA-aware initialization: Only initialize app logic when the correct view is present

// Use a global type-safe property for the app instance
declare global {
  interface Window {
    _facialSymmetryApp?: FacialSymmetryApp;
  }
}

function initializeApp() {
  // Initialize app if not already initialized
  if (!window._facialSymmetryApp) {
    window._facialSymmetryApp = new FacialSymmetryApp();
    window._facialSymmetryApp.initialize();
    console.log('[DEBUG] FacialSymmetryApp initialized');
  }
}

function initializeAppIfViewPresent() {
  // Initialize if any view exists (home, exam, results, or image assessment)
  const patientForm = document.getElementById('patientForm');
  const examView = document.getElementById('examView');
  const resultsContainer = document.getElementById('resultsContainer');
  const imageAssessmentContainer = document.getElementById('imageAssessmentContainer');
  const modeSelection = document.getElementById('modeSelection');

  if (patientForm || examView || resultsContainer || imageAssessmentContainer || modeSelection) {
    initializeApp();
  }
}

// Listen for custom events after routes are loaded
window.addEventListener('examViewLoaded', () => {
  initializeAppIfViewPresent();
});

window.addEventListener('resultsViewLoaded', () => {
  initializeAppIfViewPresent();
});

window.addEventListener('imageAssessmentViewLoaded', () => {
  initializeAppIfViewPresent();
});

// Listen for DOM content loaded to catch initial home view
document.addEventListener('DOMContentLoaded', () => {
  // Small delay to ensure route content is loaded
  setTimeout(() => {
    initializeAppIfViewPresent();

    // Fallback: If app still not initialized after 500ms, force initialize
    // This ensures the app is available for home page interactions
    setTimeout(() => {
      if (!window._facialSymmetryApp) {
        console.log('[DEBUG] Force initializing app for home page');
        initializeApp();
      }
    }, 500);
  }, 100);
});

// Listen for hash changes to reinitialize when needed
window.addEventListener('hashchange', () => {
  // Small delay to ensure new route content is loaded
  setTimeout(() => {
    initializeAppIfViewPresent();
  }, 100);
});
