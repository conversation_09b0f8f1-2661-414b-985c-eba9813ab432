// Domain Value Object: Metric Result
export class MetricResult {
  constructor(
    public readonly actionName: string,
    public readonly metricName: string,
    public readonly value: number,
    public readonly score: number,
    public readonly label: string,
    public readonly timestamp: Date = new Date()
  ) {}

  static create(data: {
    actionName: string;
    metricName: string;
    value: number;
    score: number;
    label: string;
  }): MetricResult {
    return new MetricResult(
      data.actionName,
      data.metricName,
      data.value,
      data.score,
      data.label
    );
  }

  isNormal(): boolean {
    return this.score === 0;
  }

  isSevere(): boolean {
    return this.score >= 4;
  }
}
