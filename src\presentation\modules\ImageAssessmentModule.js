// Image Assessment Module - Standalone functionality for image-based facial analysis
export class ImageAssessmentManager {
  constructor() {
    this.uploadedImages = new Map();
    this.totalSize = 0;
    this.requiredActions = ['baseline', 'eyebrow_raise', 'eye_close', 'smile'];
    this.maxFileSize = 10 * 1024 * 1024; // 10MB
    this.allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

    // Face detection components
    this.faceDetection = null;
    this.faceMesh = null;
    this.isModelLoading = false;
    this.modelsLoaded = false;

    this.initializePatientInfo();
    this.setupDragAndDrop();

    // Initialize face detection asynchronously
    this.initializeFaceDetection().then(() => {
      console.log('Face detection initialization completed');
    }).catch(error => {
      console.error('Face detection initialization failed:', error);
    });
  }

  initializePatientInfo() {
    // Get patient data from localStorage or URL params
    const patientData = this.getPatientData();
    if (patientData) {
      // Use setTimeout to ensure DOM elements are available
      setTimeout(() => {
        const nameEl = document.getElementById('patientName');
        const ageEl = document.getElementById('patientAge');
        const idEl = document.getElementById('patientId');
        
        if (nameEl) nameEl.textContent = patientData.name || 'Unknown';
        if (ageEl) ageEl.textContent = patientData.age || 'Unknown';
        if (idEl) idEl.textContent = patientData.id || 'Unknown';
        
        console.log('Patient info updated:', patientData);
      }, 100);
    }
  }

  getPatientData() {
    // Try to get from localStorage first
    try {
      const stored = localStorage.getItem('currentPatientData');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error reading patient data from localStorage:', error);
    }

    // Fallback to URL params or default
    return {
      name: 'Test Patient',
      age: '30',
      id: 'P001'
    };
  }

  async initializeFaceDetection() {
    console.log('Initializing face detection models...');
    this.isModelLoading = true;

    try {
      // Wait for MediaPipe to be available
      await this.waitForMediaPipe();

      if (typeof window.FaceMesh !== 'undefined') {
        this.faceMesh = new window.FaceMesh({
          locateFile: (file) => {
            return `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh@0.4.**********/${file}`;
          }
        });

        this.faceMesh.setOptions({
          maxNumFaces: 1,
          refineLandmarks: true,
          minDetectionConfidence: 0.5,
          minTrackingConfidence: 0.5
        });

        // Set up results handler (required for MediaPipe)
        this.faceMesh.onResults((results) => {
          // Store results for validation
          this.lastFaceMeshResults = results;
          console.log('FaceMesh results received:', results);
        });

        console.log('FaceMesh initialized successfully');

        // Test if FaceMesh is working
        this.testFaceMeshInitialization();
      }

      this.modelsLoaded = true;
      this.isModelLoading = false;
      console.log('Face detection models loaded successfully');

    } catch (error) {
      console.error('Error loading face detection models:', error);
      this.isModelLoading = false;
      // Continue without face detection - will show warning to user
    }
  }

  async waitForMediaPipe() {
    return new Promise((resolve) => {
      if (typeof window.FaceMesh !== 'undefined') {
        resolve();
        return;
      }

      const checkInterval = setInterval(() => {
        if (typeof window.FaceMesh !== 'undefined') {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);

      // Timeout after 10 seconds
      setTimeout(() => {
        clearInterval(checkInterval);
        resolve();
      }, 10000);
    });
  }

  testFaceMeshInitialization() {
    console.log('Testing FaceMesh initialization...');
    console.log('FaceMesh object:', this.faceMesh);
    console.log('FaceMesh methods available:', {
      send: typeof this.faceMesh?.send,
      setOptions: typeof this.faceMesh?.setOptions,
      onResults: typeof this.faceMesh?.onResults
    });

    // Test button removed - face validation is working
  }

  addTestButton() {
    // Add a test button to verify face detection is working
    const testButton = document.createElement('button');
    testButton.textContent = 'Test Face Detection';
    testButton.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 9999;
      background: #ff6b6b;
      color: white;
      border: none;
      padding: 10px;
      border-radius: 5px;
      cursor: pointer;
    `;

    testButton.onclick = () => this.testFaceDetectionWithSampleImage();
    document.body.appendChild(testButton);
  }

  async testFaceDetectionWithSampleImage() {
    console.log('Testing face detection with sample image...');

    if (!this.faceMesh) {
      console.error('FaceMesh not available for testing');
      alert('FaceMesh not available for testing');
      return;
    }

    // Create a test canvas with a simple pattern
    const canvas = document.createElement('canvas');
    canvas.width = 300;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');

    // Draw a simple face-like pattern
    ctx.fillStyle = '#ffdbac'; // skin color
    ctx.fillRect(0, 0, 300, 300);

    // Eyes
    ctx.fillStyle = '#000';
    ctx.fillRect(80, 100, 20, 20);
    ctx.fillRect(200, 100, 20, 20);

    // Nose
    ctx.fillRect(140, 150, 20, 30);

    // Mouth
    ctx.fillRect(120, 200, 60, 10);

    try {
      this.lastFaceMeshResults = null;
      await this.faceMesh.send({ image: canvas });

      setTimeout(() => {
        console.log('Test results:', this.lastFaceMeshResults);
        if (this.lastFaceMeshResults && this.lastFaceMeshResults.multiFaceLandmarks && this.lastFaceMeshResults.multiFaceLandmarks.length > 0) {
          alert('Face detection is working! Detected face with landmarks.');
        } else {
          alert('Face detection test failed - no face detected in test image.');
        }
      }, 1000);

    } catch (error) {
      console.error('Test failed:', error);
      alert('Face detection test failed with error: ' + error.message);
    }
  }

  setupDragAndDrop() {
    this.requiredActions.forEach(action => {
      const uploadArea = document.getElementById(`uploadArea_${action}`);
      if (uploadArea) {
        uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        uploadArea.addEventListener('drop', (e) => this.handleDrop(e, action));
        uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
      }
    });
  }

  handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.style.borderColor = '#3b82f6';
    e.currentTarget.style.background = '#f8fafc';
  }

  handleDragLeave(e) {
    e.preventDefault();
    e.currentTarget.style.borderColor = '#d1d5db';
    e.currentTarget.style.background = '';
  }

  handleDrop(e, action) {
    e.preventDefault();
    e.currentTarget.style.borderColor = '#d1d5db';
    e.currentTarget.style.background = '';

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      this.processFile(files[0], action);
    }
  }

  validateFile(file) {
    if (!this.allowedTypes.includes(file.type)) {
      alert('Please upload a valid image file (JPG, PNG, or WebP)');
      return false;
    }

    if (file.size > this.maxFileSize) {
      alert('File size must be less than 10MB');
      return false;
    }

    return true;
  }

  async validateFaceInImage(imageElement) {
    console.log('=== FACE VALIDATION START ===');
    console.log('Models loaded:', this.modelsLoaded);
    console.log('Is loading:', this.isModelLoading);
    console.log('FaceMesh available:', !!this.faceMesh);
    console.log('FaceMesh object:', this.faceMesh);
    console.log('Image element:', imageElement);
    console.log('Image dimensions:', imageElement.width, 'x', imageElement.height);

    // FORCE STRICT VALIDATION - NO BYPASSING
    if (!this.faceMesh || this.isModelLoading) {
      console.warn('Face detection models not loaded yet, waiting...');

      // Wait up to 10 seconds for models to load
      let waitTime = 0;
      while ((!this.faceMesh || this.isModelLoading) && waitTime < 10000) {
        await new Promise(resolve => setTimeout(resolve, 500));
        waitTime += 500;
        console.log(`Waiting for face detection models... ${waitTime}ms`);
      }

      // Check again after waiting
      if (!this.faceMesh) {
        console.error('❌ REJECTING: FaceMesh not available after waiting');
        return {
          isValid: false,
          error: 'Face detection system not initialized. Cannot validate image.'
        };
      }
    }

    if (!this.modelsLoaded) {
      console.error('❌ REJECTING: Models not loaded');
      return {
        isValid: false,
        error: 'Face detection models not loaded. Please refresh the page and try again.'
      };
    }

    try {
      let faceDetected = false;
      let landmarksDetected = false;

      // Use FaceMesh for face and landmark detection
      if (this.faceMesh) {
        console.log('Starting face detection with FaceMesh...');

        // Reset results
        this.lastFaceMeshResults = null;

        try {
          // Send image to FaceMesh
          await this.faceMesh.send({ image: imageElement });

          // Wait for results with longer timeout
          await new Promise(resolve => setTimeout(resolve, 1000));

          console.log('FaceMesh results:', this.lastFaceMeshResults);

          if (this.lastFaceMeshResults && this.lastFaceMeshResults.multiFaceLandmarks) {
            const faces = this.lastFaceMeshResults.multiFaceLandmarks;
            console.log(`FaceMesh detected ${faces.length} face(s)`);

            if (faces.length > 0) {
              faceDetected = true;
              const landmarks = faces[0];
              if (landmarks && landmarks.length >= 468) { // MediaPipe FaceMesh has 468 landmarks
                landmarksDetected = true;
                console.log(`Face mesh detected ${landmarks.length} landmarks`);
              } else {
                console.log(`Insufficient landmarks detected: ${landmarks ? landmarks.length : 0}`);
              }
            }
          } else {
            console.log('No face landmarks detected in results');
          }
        } catch (error) {
          console.error('Error during FaceMesh processing:', error);
          throw error;
        }
      } else {
        console.error('FaceMesh not available for face detection');
        return {
          isValid: false,
          error: 'Face detection system not properly initialized.'
        };
      }

      if (!faceDetected) {
        return {
          isValid: false,
          error: 'No face detected in the image. Please upload an image with a clear, visible face.'
        };
      }

      if (faceDetected && !landmarksDetected) {
        return {
          isValid: true,
          warning: 'Face detected but detailed landmarks may not be available. Analysis accuracy might be reduced.'
        };
      }

      return {
        isValid: true,
        message: 'Face and landmarks detected successfully!'
      };

    } catch (error) {
      console.error('Error during face validation:', error);
      return {
        isValid: false,
        error: 'Face validation failed due to technical error. Please try again with a different image.'
      };
    }
  }

  async processFile(file, action) {
    if (!this.validateFile(file)) {
      return;
    }

    this.updateStatus(action, 'uploading', 'Processing image...');

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        // Create image element for face validation
        const img = new Image();
        img.onload = async () => {
          try {
            console.log('🖼️ Image loaded, starting face validation...');

            // Validate face in image
            this.updateStatus(action, 'uploading', 'Detecting face...');
            const validation = await this.validateFaceInImage(img);

            console.log('🔍 Validation result:', validation);

            if (!validation.isValid) {
              // Face validation failed
              console.log('❌ Face validation FAILED, rejecting image');
              this.updateStatus(action, 'error', validation.error);
              this.showValidationMessage(action, validation.error, 'error');
              return;
            }

            console.log('✅ Face validation PASSED, accepting image');

            // Face validation passed - store the image
            this.uploadedImages.set(action, {
              file: file,
              dataUrl: e.target.result,
              size: file.size,
              faceValidation: validation
            });

            this.showImagePreview(action, e.target.result);

            // Show appropriate status message - only use showValidationMessage for user feedback
            if (validation.warning) {
              this.updateStatus(action, 'completed', 'Image uploaded');
              this.showValidationMessage(action, validation.warning, 'warning');
            } else if (validation.message) {
              this.updateStatus(action, 'completed', 'Image uploaded');
              this.showValidationMessage(action, validation.message, 'success');
            } else {
              this.updateStatus(action, 'completed', 'Image uploaded successfully');
            }

            this.updateUploadCard(action, true);
            this.updateSummary();
            this.checkAllUploaded();

          } catch (error) {
            console.error('Error during face validation:', error);
            this.updateStatus(action, 'error', 'Face validation failed');
          }
        };

        img.onerror = () => {
          this.updateStatus(action, 'error', 'Failed to load image');
        };

        img.src = e.target.result;

      } catch (error) {
        console.error('Error processing file:', error);
        this.updateStatus(action, 'error', 'Failed to process image');
      }
    };

    reader.onerror = () => {
      this.updateStatus(action, 'error', 'Failed to read file');
    };

    reader.readAsDataURL(file);
  }

  showImagePreview(action, dataUrl) {
    const placeholder = document.querySelector(`#uploadArea_${action} .upload-placeholder`);
    const preview = document.getElementById(`preview_${action}`);
    const previewImg = document.getElementById(`previewImg_${action}`);

    if (placeholder && preview && previewImg) {
      placeholder.style.display = 'none';
      preview.style.display = 'block';
      previewImg.src = dataUrl;
    }
  }

  showValidationMessage(action, message, type) {
    // Create or update validation message element
    const uploadCard = document.querySelector(`[data-action="${action}"]`);
    if (!uploadCard) return;

    // Remove existing validation message
    const existingMessage = uploadCard.querySelector('.validation-message');
    if (existingMessage) {
      existingMessage.remove();
    }

    // Create new validation message
    const messageEl = document.createElement('div');
    messageEl.className = `validation-message validation-${type}`;
    messageEl.innerHTML = `
      <div class="validation-content">
        <span class="validation-icon">
          ${type === 'error' ? '⚠️' : type === 'warning' ? '⚠️' : '✅'}
        </span>
        <span class="validation-text">${message}</span>
      </div>
    `;

    // Add styles
    messageEl.style.cssText = `
      margin-top: 8px;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 0.875rem;
      line-height: 1.4;
      ${type === 'error' ? 'background: #fef2f2; border: 1px solid #fecaca; color: #dc2626;' : ''}
      ${type === 'warning' ? 'background: #fffbeb; border: 1px solid #fed7aa; color: #d97706;' : ''}
      ${type === 'success' ? 'background: #f0fdf4; border: 1px solid #bbf7d0; color: #16a34a;' : ''}
    `;

    // Insert after the upload area
    const uploadArea = uploadCard.querySelector(`#uploadArea_${action}`);
    if (uploadArea) {
      uploadArea.parentNode.insertBefore(messageEl, uploadArea.nextSibling);
    }

    // Auto-remove success messages after 5 seconds
    if (type === 'success') {
      setTimeout(() => {
        if (messageEl.parentNode) {
          messageEl.remove();
        }
      }, 5000);
    }
  }

  updateStatus(action, status, message) {
    const statusElement = document.getElementById(`status_${action}`);
    const indicator = statusElement?.querySelector('.status-indicator');

    if (indicator) {
      indicator.className = `status-indicator ${status}`;
      const span = indicator.querySelector('span');
      if (span) span.textContent = message;

      // Update icon based on status
      const icon = indicator.querySelector('svg');
      if (icon && status === 'completed') {
        icon.innerHTML = '<path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z" fill="currentColor"/>';
      } else if (icon && status === 'error') {
        icon.innerHTML = '<path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z" fill="currentColor"/>';
      }
    }
  }

  updateUploadCard(action, completed) {
    const card = document.querySelector(`[data-action="${action}"]`);
    if (card) {
      if (completed) {
        card.classList.add('completed');
      } else {
        card.classList.remove('completed');
      }
    }
  }

  updateSummary() {
    const uploadCount = this.uploadedImages.size;
    const totalSize = Array.from(this.uploadedImages.values())
      .reduce((sum, img) => sum + img.size, 0);

    const uploadCountEl = document.getElementById('uploadCount');
    const totalSizeEl = document.getElementById('totalSize');
    
    if (uploadCountEl) uploadCountEl.textContent = `${uploadCount} / ${this.requiredActions.length}`;
    if (totalSizeEl) totalSizeEl.textContent = `${(totalSize / (1024 * 1024)).toFixed(1)} MB`;
  }

  checkAllUploaded() {
    const allUploaded = this.requiredActions.every(action => this.uploadedImages.has(action));
    const analyzeBtn = document.getElementById('analyzeBtn');

    if (analyzeBtn) {
      analyzeBtn.disabled = !allUploaded;
      if (allUploaded) {
        const btnText = analyzeBtn.querySelector('#analyzeBtnText');
        if (btnText) btnText.textContent = 'Analyze Images (Ready!)';
      }
    }
  }

  retakeImage(action) {
    // Remove the uploaded image
    this.uploadedImages.delete(action);

    // Reset UI
    const placeholder = document.querySelector(`#uploadArea_${action} .upload-placeholder`);
    const preview = document.getElementById(`preview_${action}`);
    const fileInput = document.getElementById(`fileInput_${action}`);

    if (placeholder && preview && fileInput) {
      placeholder.style.display = 'flex';
      preview.style.display = 'none';
      fileInput.value = '';
    }

    // Clear validation messages
    const uploadCard = document.querySelector(`[data-action="${action}"]`);
    const validationMessage = uploadCard?.querySelector('.validation-message');
    if (validationMessage) {
      validationMessage.remove();
    }

    this.updateStatus(action, 'pending', 'Waiting for upload');
    this.updateUploadCard(action, false);
    this.updateSummary();
    this.checkAllUploaded();
  }

  async startAnalysis() {
    if (this.uploadedImages.size !== this.requiredActions.length) {
      alert('Please upload all required images before starting analysis');
      return;
    }

    const analyzeBtn = document.getElementById('analyzeBtn');
    const analyzeBtnText = document.getElementById('analyzeBtnText');

    // Update button state
    if (analyzeBtn) analyzeBtn.disabled = true;
    if (analyzeBtnText) analyzeBtnText.textContent = 'Processing Images...';

    try {
      // Store images for analysis
      const imageData = {};
      for (const [action, data] of this.uploadedImages) {
        imageData[action] = data.dataUrl;
      }

      // Store in localStorage for the analysis controller
      localStorage.setItem('uploadedImages', JSON.stringify(imageData));
      localStorage.setItem('analysisMode', 'images');

      // Navigate to results (will be processed by image analysis controller)
      console.log('Starting image-based analysis...');

      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Navigate to results
      window.history.pushState({}, '', '/results');
      window.dispatchEvent(new Event('popstate'));

    } catch (error) {
      console.error('Analysis failed:', error);
      alert('Analysis failed. Please try again.');

      // Reset button
      if (analyzeBtn) analyzeBtn.disabled = false;
      if (analyzeBtnText) analyzeBtnText.textContent = 'Analyze Images';
    }
  }
}

// Global functions for HTML onclick handlers
export function handleImageUpload(action, input) {
  console.log('handleImageUpload called:', action, input);
  if (input.files && input.files[0]) {
    if (window.imageManager && window.imageManager.processFile) {
      window.imageManager.processFile(input.files[0], action);
    } else {
      console.error('Image manager not initialized');
    }
  }
}

export function retakeImage(action) {
  console.log('retakeImage called:', action);
  if (window.imageManager && window.imageManager.retakeImage) {
    window.imageManager.retakeImage(action);
  }
}

export function startImageAnalysis() {
  console.log('startImageAnalysis called');
  if (window.imageManager && window.imageManager.startAnalysis) {
    window.imageManager.startAnalysis();
  }
}

export function goBackToHome() {
  console.log('goBackToHome called');
  window.history.pushState({}, '', '/');
  window.dispatchEvent(new Event('popstate'));
}

// Initialize when this module is loaded
export function initializeImageAssessment() {
  if (!window.imageManager) {
    window.imageManager = new ImageAssessmentManager();
    console.log('ImageAssessmentManager initialized from module');
  }
  
  // Make functions globally accessible
  window.handleImageUpload = handleImageUpload;
  window.retakeImage = retakeImage;
  window.startImageAnalysis = startImageAnalysis;
  window.goBackToHome = goBackToHome;
  
  console.log('Image assessment functions made globally available');
}
