# Detection Debugging Guide

## Current Issue: Backwards Detection Results

The detection system was giving backwards results:
- **Detecting glasses when NOT wearing them**
- **NOT detecting glasses when wearing them**
- **Similar issues with mask detection**

## New Debugging System

### 1. Simplified Detection Logic

I've completely rewritten the detection with ultra-simple rules:

```typescript
// GLASSES: Only detect if BOTH eyebrow-eye distances are very small
if (leftDist < 0.015 && rightDist < 0.015) {
  glassesDetected = true;
}

// MASK: Only detect if mouth is essentially invisible
if (mouthHeight < 0.002) {
  maskDetected = true;
}
```

### 2. Extensive Debug Logging

The system now logs detailed measurements every 30 frames:

```
=== DETECTION DEBUG (Frame 30) ===
Raw measurements:
  Left eyebrow-eye distance: 0.0234
  Right eyebrow-eye distance: 0.0198
  Average eyebrow-eye distance: 0.0216
  Mouth height: 0.0045

Detection logic:
  Glasses threshold: both distances < 0.015
  Mask threshold: mouth height < 0.002

Results:
  Glasses detected: false (L✗) (R✗)
  Mask detected: false (✗)
========================
```

## How to Debug

### 1. Open Browser Console
- **Chrome/Edge**: F12 → Console tab
- **Firefox**: F12 → Console tab
- **Safari**: Cmd+Option+I → Console tab

### 2. Start the Application
Visit `http://localhost:3000` and start the camera

### 3. Analyze the Debug Output

#### **Without Glasses/Mask** (Expected):
```
Raw measurements:
  Left eyebrow-eye distance: 0.0234    ← Should be > 0.015
  Right eyebrow-eye distance: 0.0198   ← Should be > 0.015
  Mouth height: 0.0045                 ← Should be > 0.002

Results:
  Glasses detected: false (L✗) (R✗)    ← Correct!
  Mask detected: false (✗)             ← Correct!
```

#### **With Glasses** (Expected):
```
Raw measurements:
  Left eyebrow-eye distance: 0.0089    ← Should be < 0.015
  Right eyebrow-eye distance: 0.0112   ← Should be < 0.015
  Mouth height: 0.0045                 ← Should be > 0.002

Results:
  Glasses detected: true (L✓) (R✓)     ← Should detect!
  Mask detected: false (✗)             ← Correct!
```

#### **With Mask** (Expected):
```
Raw measurements:
  Left eyebrow-eye distance: 0.0234    ← Should be > 0.015
  Right eyebrow-eye distance: 0.0198   ← Should be > 0.015
  Mouth height: 0.0008                 ← Should be < 0.002

Results:
  Glasses detected: false (L✗) (R✗)    ← Correct!
  Mask detected: true (✓)              ← Should detect!
```

## Troubleshooting Steps

### 1. Check Your Actual Measurements

**Without any accessories:**
1. Start the app and look at the console
2. Note down your baseline measurements:
   - Left eyebrow-eye distance: ____
   - Right eyebrow-eye distance: ____
   - Mouth height: ____

**With glasses:**
1. Put on your glasses
2. Check if the eyebrow-eye distances get smaller
3. They should drop below 0.015 for detection

**With mask:**
1. Put on a mask
2. Check if mouth height gets very small
3. It should drop below 0.002 for detection

### 2. Analyze the Results

#### **If glasses aren't detected when wearing them:**
- Check if eyebrow-eye distances are actually getting smaller
- Your glasses might not compress the landmarks enough
- Try different glasses (thicker frames work better)
- Consider adjusting the threshold from 0.015 to a higher value

#### **If glasses are detected when NOT wearing them:**
- Check your baseline eyebrow-eye distances
- If they're naturally < 0.015, you have naturally close eyebrows
- We need to adjust the threshold to be more strict

#### **If mask detection is wrong:**
- Check your baseline mouth height
- Mask detection is very strict (< 0.002)
- May need to adjust based on your facial structure

### 3. Threshold Adjustment

Based on your measurements, we can adjust the thresholds:

```typescript
// Current thresholds
const GLASSES_THRESHOLD = 0.015;
const MASK_THRESHOLD = 0.002;

// If you have naturally close eyebrows, make it stricter:
const GLASSES_THRESHOLD = 0.010; // or even 0.008

// If you have a naturally small mouth, make it stricter:
const MASK_THRESHOLD = 0.001; // or even 0.0005
```

## Expected Measurements

### **Typical Values Without Accessories:**
- **Eyebrow-eye distance**: 0.020 - 0.035
- **Mouth height**: 0.003 - 0.008

### **Typical Values With Glasses:**
- **Eyebrow-eye distance**: 0.008 - 0.018 (compressed by frames)
- **Mouth height**: 0.003 - 0.008 (unchanged)

### **Typical Values With Mask:**
- **Eyebrow-eye distance**: 0.020 - 0.035 (unchanged)
- **Mouth height**: 0.0005 - 0.002 (severely compressed)

## Next Steps

### 1. Collect Your Data
Run the app and collect measurements for:
- Your face without accessories
- Your face with glasses
- Your face with mask (if available)

### 2. Report Results
Share the debug output showing:
- What the measurements are
- Whether detection is working correctly
- What thresholds would work better for your face

### 3. Threshold Tuning
Based on your specific measurements, we can:
- Adjust the detection thresholds
- Add personalized calibration
- Implement adaptive thresholds

The goal is to find thresholds that work reliably for your specific facial structure and accessories.
