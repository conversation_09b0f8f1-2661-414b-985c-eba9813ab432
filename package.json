{"name": "web_browser", "version": "1.0.0", "description": "", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc && npm run copy:assets", "build:client": "tsc", "copy:assets": "xcopy src\\assets\\html\\pages\\index.html dist\\ /Y && if not exist dist\\pages mkdir dist\\pages && xcopy src\\assets\\html\\pages\\*.html dist\\pages\\ /Y && if not exist dist\\test mkdir dist\\test && xcopy src\\assets\\html\\test\\*.html dist\\test\\ /Y && xcopy src\\presentation\\modules\\ImageAssessmentModule.js dist\\ /Y", "dev": "npm run build && npx http-server dist -p 9700 -o", "serve": "npm run clean && npm run build && npx http-server dist -p 9700 -o", "serve:dist": "npm run build && npx http-server dist -p 9700 -o", "serve:root": "npx http-server . -p 9601 -o", "clean": "<PERSON><PERSON><PERSON> dist", "clean:win": "if exist dist rmdir /s /q dist", "rebuild": "npm run clean && npm run build", "deploy": "npm run build && firebase deploy --only hosting"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2"}, "devDependencies": {"@types/node": "^22.15.21", "http-server": "^14.1.1", "rimraf": "^6.0.1", "typescript": "^5.8.3"}}