# Facial Symmetry Algorithm Improvements

## Problem Identified
The original algorithm was producing abnormally high asymmetry scores for healthy individuals, indicating several issues:

1. **Incorrect landmark indices** - Some MediaPipe Face Mesh landmarks were not optimal
2. **Overly sensitive thresholds** - Normal facial variation was being classified as asymmetry
3. **Poor noise handling** - Single outlier measurements could skew results
4. **Inappropriate normalization** - Scaling factors weren't realistic

## Improvements Made

### 1. Updated Landmark Selection
**Before:**
- Used generic landmarks that may not be stable
- Some indices were potentially incorrect for MediaPipe Face Mesh

**After:**
- Selected more reliable landmark points:
  - Forehead: Temple areas (21, 251) instead of arbitrary points
  - Eyes: Proper inner/outer corners and eyebrow points
  - Mouth: Accurate corner and lip landmarks
  - Nose: Better nostril reference points
  - Face outline: Stable reference points for normalization

### 2. Improved Scoring Thresholds
**Before:**
```typescript
if (value < 0.02) return 'Normal';      // Too strict
if (value < 0.04) return 'Mild';        // Too strict
if (value < 0.07) return 'Moderate';    // Too strict
```

**After:**
```typescript
if (value < 0.05) return 'Normal';      // More realistic
if (value < 0.08) return 'Mild';        // Accounts for natural variation
if (value < 0.12) return 'Moderate';    // Better clinical correlation
```

### 3. Enhanced Metric Calculations
**Improvements:**
- **Better normalization**: Use face outline width instead of arbitrary scaling
- **Error handling**: Prevent division by very small numbers
- **Value capping**: Limit extreme outliers to reasonable maximums
- **Coordinate system**: Use normalized coordinates (0-1) for consistency

### 4. Statistical Improvements
**Before:** Simple averaging of all measurements
**After:** 
- **Median filtering**: Reduces impact of outliers and noise
- **Robust aggregation**: Less sensitive to temporary detection errors
- **Improved stability**: More consistent results across measurements

### 5. Algorithm Robustness
**Added:**
- Minimum face width validation
- Outlier detection and capping
- Better error handling for missing landmarks
- Smoothing to reduce measurement noise

## Expected Results for Healthy Individuals

### Normal Range (Score 0):
- Most measurements should be < 0.05
- Indicates good facial symmetry
- Expected for individuals without facial paralysis

### Mild Asymmetry (Score 1):
- Measurements 0.05 - 0.08
- Natural facial variation
- Still within normal limits for most people

### Clinical Significance:
- Scores 2+ indicate potential clinical concern
- Better correlation with actual facial paralysis
- Reduced false positives for healthy individuals

## Technical Details

### Landmark Mapping:
```typescript
forehead_left: 21,    // Left temple
forehead_right: 251,  // Right temple
eye_left_outer: 33,   // Left eye outer corner
eye_right_outer: 263, // Right eye outer corner
mouth_left: 61,       // Left mouth corner
mouth_right: 291,     // Right mouth corner
```

### Median Calculation:
```typescript
const sortedValues = values.sort((a, b) => a - b);
const median = sortedValues.length % 2 === 0 
  ? (sortedValues[n/2-1] + sortedValues[n/2]) / 2
  : sortedValues[Math.floor(n/2)];
```

## Testing Recommendations

1. **Test with healthy individuals** - Should see mostly "Normal" scores
2. **Test different lighting conditions** - Algorithm should be stable
3. **Test various head positions** - Minor head tilts shouldn't affect results significantly
4. **Compare with clinical assessments** - Validate against known cases

The improved algorithm should now provide more clinically accurate and realistic facial symmetry assessments.
