<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Results Transformation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Results Transformation</h1>
        <p>This tool tests the data transformation between examination results and the results view.</p>

        <div class="test-section">
            <h3>1. Create Mock Examination Results</h3>
            <button class="button" onclick="createMockResults()">Create Mock Results</button>
            <div id="mockResults" class="results"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Results Transformation</h3>
            <button class="button" onclick="testTransformation()">Transform Results</button>
            <div id="transformationResults" class="results"></div>
        </div>

        <div class="test-section">
            <h3>3. Store and Load Results</h3>
            <button class="button" onclick="storeResults()">Store Results</button>
            <button class="button" onclick="loadResults()">Load Results</button>
            <button class="button" onclick="clearResults()">Clear Results</button>
            <div id="storageResults" class="results"></div>
        </div>

        <div class="test-section">
            <h3>4. Navigate to Results Page</h3>
            <button class="button" onclick="navigateToResults()">Go to Results Page</button>
            <div id="navigationResults" class="results"></div>
        </div>
    </div>

    <script>
        let mockExamResults = null;

        function createMockResults() {
            // Create mock landmark data with realistic coordinates
            const createMockLandmarks = (count = 468) => {
                const landmarks = [];
                for (let i = 0; i < count; i++) {
                    landmarks.push({
                        x: Math.random() * 0.8 + 0.1, // 0.1 to 0.9
                        y: Math.random() * 0.8 + 0.1, // 0.1 to 0.9
                        z: Math.random() * 0.1 - 0.05 // -0.05 to 0.05
                    });
                }
                return landmarks;
            };

            // Create baseline landmarks
            const baseline = createMockLandmarks();
            
            // Create eyebrow raise landmarks (slightly higher eyebrows)
            const eyebrowRaise = baseline.map((landmark, index) => {
                // Eyebrow landmarks: 70, 63, 105, 66, 107 (left) and 300, 293, 334, 296, 336 (right)
                const isEyebrowLandmark = [70, 63, 105, 66, 107, 300, 293, 334, 296, 336].includes(index);
                return {
                    x: landmark.x,
                    y: isEyebrowLandmark ? landmark.y - 0.02 : landmark.y, // Move eyebrows up
                    z: landmark.z
                };
            });

            // Create eye close landmarks (closer eye landmarks)
            const eyeClose = baseline.map((landmark, index) => {
                // Eye landmarks: various indices for upper and lower eyelids
                const isEyeLandmark = [159, 145, 158, 153, 160, 144, 386, 374, 385, 380, 387, 373].includes(index);
                return {
                    x: landmark.x,
                    y: isEyeLandmark ? landmark.y + (Math.random() * 0.01) : landmark.y, // Close eyes slightly
                    z: landmark.z
                };
            });

            // Create smile landmarks (wider mouth)
            const smile = baseline.map((landmark, index) => {
                // Mouth corner landmarks: 61 (left), 291 (right)
                if (index === 61) { // Left mouth corner
                    return {
                        x: landmark.x - 0.015, // Move left corner outward
                        y: landmark.y - 0.01,  // Move up slightly
                        z: landmark.z
                    };
                } else if (index === 291) { // Right mouth corner
                    return {
                        x: landmark.x + 0.015, // Move right corner outward
                        y: landmark.y - 0.01,  // Move up slightly
                        z: landmark.z
                    };
                } else {
                    return landmark;
                }
            });

            mockExamResults = {
                patientInfo: {
                    id: 'TEST-001',
                    name: 'Test Patient',
                    age: '30'
                },
                timestamp: new Date().toISOString(),
                actions: ['Neutral / Resting Face', 'Raise Eyebrows', 'Close Eyes Tightly', 'Smile Wide'],
                overallScore: 87.5,
                landmarkData: {
                    baseline: baseline,
                    eyebrowRaise: eyebrowRaise,
                    eyeClose: eyeClose,
                    smile: smile
                },
                clinicalAnalysis: {
                    regional_analysis: {
                        forehead: {
                            asymmetry_percentage: 10.9,
                            severity_grade: 'Mild',
                            affected_side: 'Left'
                        },
                        eye: {
                            asymmetry_percentage: 8.5,
                            severity_grade: 'Mild',
                            affected_side: 'Right'
                        },
                        smile: {
                            asymmetry_percentage: 12.3,
                            severity_grade: 'Mild',
                            affected_side: 'Left'
                        }
                    },
                    composite_scores: {
                        facial_asymmetry_index: 0.125,
                        functional_impairment_score: 0.15
                    }
                }
            };

            document.getElementById('mockResults').textContent = 
                `✅ Mock examination results created successfully!\n\n` +
                `Patient: ${mockExamResults.patientInfo.name} (${mockExamResults.patientInfo.id})\n` +
                `Landmark data:\n` +
                `  - Baseline: ${mockExamResults.landmarkData.baseline.length} landmarks\n` +
                `  - Eyebrow Raise: ${mockExamResults.landmarkData.eyebrowRaise.length} landmarks\n` +
                `  - Eye Close: ${mockExamResults.landmarkData.eyeClose.length} landmarks\n` +
                `  - Smile: ${mockExamResults.landmarkData.smile.length} landmarks\n\n` +
                `Clinical Analysis:\n` +
                `  - Forehead asymmetry: ${mockExamResults.clinicalAnalysis.regional_analysis.forehead.asymmetry_percentage}%\n` +
                `  - Eye asymmetry: ${mockExamResults.clinicalAnalysis.regional_analysis.eye.asymmetry_percentage}%\n` +
                `  - Smile asymmetry: ${mockExamResults.clinicalAnalysis.regional_analysis.smile.asymmetry_percentage}%`;
        }

        function testTransformation() {
            if (!mockExamResults) {
                document.getElementById('transformationResults').innerHTML = 
                    '<span class="error">❌ Please create mock results first!</span>';
                return;
            }

            try {
                // Simulate the transformation logic from ResultsController
                const transformedResults = transformResultsForView(mockExamResults);
                
                document.getElementById('transformationResults').textContent = 
                    `✅ Results transformation completed!\n\n` +
                    `Transformed symmetry metrics:\n` +
                    `  - Left Eyebrow Elevation: ${transformedResults.symmetryMetrics.leftEyebrowElevation.toFixed(2)}°\n` +
                    `  - Right Eyebrow Elevation: ${transformedResults.symmetryMetrics.rightEyebrowElevation.toFixed(2)}°\n` +
                    `  - Eyebrow Asymmetry: ${transformedResults.symmetryMetrics.eyebrowAsymmetry.toFixed(1)}%\n\n` +
                    `  - Left Eye Closure: ${transformedResults.symmetryMetrics.leftEyeClosure.toFixed(1)}%\n` +
                    `  - Right Eye Closure: ${transformedResults.symmetryMetrics.rightEyeClosure.toFixed(1)}%\n` +
                    `  - Eye Asymmetry: ${transformedResults.symmetryMetrics.eyeAsymmetry.toFixed(1)}%\n\n` +
                    `  - Left Mouth Movement: ${transformedResults.symmetryMetrics.leftMouthMovement.toFixed(2)}mm\n` +
                    `  - Right Mouth Movement: ${transformedResults.symmetryMetrics.rightMouthMovement.toFixed(2)}mm\n` +
                    `  - Mouth Asymmetry: ${transformedResults.symmetryMetrics.mouthAsymmetry.toFixed(1)}%`;
                    
            } catch (error) {
                document.getElementById('transformationResults').innerHTML = 
                    `<span class="error">❌ Transformation failed: ${error.message}</span>`;
            }
        }

        // Simplified transformation logic for testing
        function transformResultsForView(results) {
            const symmetryMetrics = {
                leftEyebrowElevation: 0,
                rightEyebrowElevation: 0,
                eyebrowAsymmetry: 0,
                eyebrowSymmetry: 85,
                leftEyeClosure: 0,
                rightEyeClosure: 0,
                eyeAsymmetry: 0,
                eyeSymmetry: 85,
                leftMouthMovement: 0,
                rightMouthMovement: 0,
                mouthAsymmetry: 0,
                mouthSymmetry: 85
            };

            // Calculate individual measurements from landmark data
            if (results.landmarkData) {
                // Eyebrow analysis
                if (results.landmarkData.baseline && results.landmarkData.eyebrowRaise) {
                    const eyebrowAnalysis = calculateEyebrowElevations(
                        results.landmarkData.baseline, 
                        results.landmarkData.eyebrowRaise
                    );
                    if (eyebrowAnalysis) {
                        symmetryMetrics.leftEyebrowElevation = eyebrowAnalysis.leftElevation;
                        symmetryMetrics.rightEyebrowElevation = eyebrowAnalysis.rightElevation;
                        symmetryMetrics.eyebrowAsymmetry = eyebrowAnalysis.asymmetryPercentage;
                        symmetryMetrics.eyebrowSymmetry = Math.max(0, 100 - eyebrowAnalysis.asymmetryPercentage);
                    }
                }

                // Eye analysis
                if (results.landmarkData.baseline && results.landmarkData.eyeClose) {
                    const eyeAnalysis = calculateEyeClosures(
                        results.landmarkData.baseline, 
                        results.landmarkData.eyeClose
                    );
                    if (eyeAnalysis) {
                        symmetryMetrics.leftEyeClosure = eyeAnalysis.leftClosurePercentage;
                        symmetryMetrics.rightEyeClosure = eyeAnalysis.rightClosurePercentage;
                        symmetryMetrics.eyeAsymmetry = eyeAnalysis.asymmetryPercentage;
                        symmetryMetrics.eyeSymmetry = Math.max(0, 100 - eyeAnalysis.asymmetryPercentage);
                    }
                }

                // Mouth analysis
                if (results.landmarkData.baseline && results.landmarkData.smile) {
                    const mouthAnalysis = calculateMouthMovements(
                        results.landmarkData.baseline, 
                        results.landmarkData.smile
                    );
                    if (mouthAnalysis) {
                        symmetryMetrics.leftMouthMovement = mouthAnalysis.leftMovement;
                        symmetryMetrics.rightMouthMovement = mouthAnalysis.rightMovement;
                        symmetryMetrics.mouthAsymmetry = mouthAnalysis.asymmetryPercentage;
                        symmetryMetrics.mouthSymmetry = Math.max(0, 100 - mouthAnalysis.asymmetryPercentage);
                    }
                }
            }

            return {
                patientInfo: results.patientInfo,
                timestamp: results.timestamp,
                actions: results.actions,
                overallScore: results.overallScore,
                symmetryMetrics: symmetryMetrics
            };
        }

        // Updated calculation functions for testing (using correct units)
        function calculateEyebrowElevations(baseline, eyebrowRaise) {
            try {
                const leftEyebrowLandmarks = [70, 63, 105, 66, 107];
                const rightEyebrowLandmarks = [300, 293, 334, 296, 336];

                const baselineLeftEyebrow = leftEyebrowLandmarks.map(i => baseline[i]);
                const baselineRightEyebrow = rightEyebrowLandmarks.map(i => baseline[i]);
                const raisedLeftEyebrow = leftEyebrowLandmarks.map(i => eyebrowRaise[i]);
                const raisedRightEyebrow = rightEyebrowLandmarks.map(i => eyebrowRaise[i]);

                const faceWidth = Math.sqrt(
                    Math.pow(baseline[454].x - baseline[234].x, 2) +
                    Math.pow(baseline[454].y - baseline[234].y, 2)
                );

                // Calculate eyebrow elevation angles in degrees using normalized coordinates
                const leftAngles = baselineLeftEyebrow.map((baselinePoint, i) => {
                    const raisedPoint = raisedLeftEyebrow[i];

                    // Calculate vertical displacement in normalized coordinates
                    const verticalDisplacement = Math.abs(baselinePoint.y - raisedPoint.y);

                    // Calculate horizontal reference distance (to eye center for angle calculation)
                    const eyeCenterLeft = baseline[133]; // Left eye center
                    const horizontalDistance = Math.abs(baselinePoint.x - eyeCenterLeft.x);

                    // Calculate elevation angle using arctangent (normalized coordinates work fine for angles)
                    const angleRadians = Math.atan(verticalDisplacement / Math.max(horizontalDistance, 0.01));
                    const angleDegrees = angleRadians * (180 / Math.PI);

                    return angleDegrees;
                });

                const rightAngles = baselineRightEyebrow.map((baselinePoint, i) => {
                    const raisedPoint = raisedRightEyebrow[i];

                    // Calculate vertical displacement in normalized coordinates
                    const verticalDisplacement = Math.abs(baselinePoint.y - raisedPoint.y);

                    // Calculate horizontal reference distance (to eye center for angle calculation)
                    const eyeCenterRight = baseline[362]; // Right eye center
                    const horizontalDistance = Math.abs(baselinePoint.x - eyeCenterRight.x);

                    // Calculate elevation angle using arctangent (normalized coordinates work fine for angles)
                    const angleRadians = Math.atan(verticalDisplacement / Math.max(horizontalDistance, 0.01));
                    const angleDegrees = angleRadians * (180 / Math.PI);

                    return angleDegrees;
                });

                const leftMeanElevation = leftAngles.reduce((a, b) => a + b, 0) / leftAngles.length;
                const rightMeanElevation = rightAngles.reduce((a, b) => a + b, 0) / rightAngles.length;

                const asymmetryPercentage = Math.abs(leftMeanElevation - rightMeanElevation) /
                                          Math.max(leftMeanElevation, rightMeanElevation) * 100;

                return {
                    leftElevation: leftMeanElevation,
                    rightElevation: rightMeanElevation,
                    asymmetryPercentage: asymmetryPercentage
                };
            } catch (error) {
                console.error('Error in eyebrow analysis:', error);
                return null;
            }
        }

        function calculateEyeClosures(baseline, eyeClose) {
            try {
                const leftEyePairs = [[159, 145], [158, 153], [160, 144]];
                const rightEyePairs = [[386, 374], [385, 380], [387, 373]];

                const baselineLeftDistances = leftEyePairs.map(([top, bottom]) => 
                    Math.abs(baseline[top].y - baseline[bottom].y)
                );
                const baselineRightDistances = rightEyePairs.map(([top, bottom]) => 
                    Math.abs(baseline[top].y - baseline[bottom].y)
                );
                const closureLeftDistances = leftEyePairs.map(([top, bottom]) => 
                    Math.abs(eyeClose[top].y - eyeClose[bottom].y)
                );
                const closureRightDistances = rightEyePairs.map(([top, bottom]) => 
                    Math.abs(eyeClose[top].y - eyeClose[bottom].y)
                );

                const avgBaselineLeft = baselineLeftDistances.reduce((a, b) => a + b, 0) / baselineLeftDistances.length;
                const avgBaselineRight = baselineRightDistances.reduce((a, b) => a + b, 0) / baselineRightDistances.length;
                const avgClosureLeft = closureLeftDistances.reduce((a, b) => a + b, 0) / closureLeftDistances.length;
                const avgClosureRight = closureRightDistances.reduce((a, b) => a + b, 0) / closureRightDistances.length;

                const leftClosurePercentage = ((avgBaselineLeft - avgClosureLeft) / avgBaselineLeft) * 100;
                const rightClosurePercentage = ((avgBaselineRight - avgClosureRight) / avgBaselineRight) * 100;
                const asymmetryPercentage = Math.abs(leftClosurePercentage - rightClosurePercentage);

                return {
                    leftClosurePercentage: leftClosurePercentage,
                    rightClosurePercentage: rightClosurePercentage,
                    asymmetryPercentage: asymmetryPercentage
                };
            } catch (error) {
                console.error('Error in eye closure analysis:', error);
                return null;
            }
        }

        function calculateMouthMovements(baseline, smile) {
            try {
                const leftCornerIndex = 61;
                const rightCornerIndex = 291;

                const baselineLeftCorner = baseline[leftCornerIndex];
                const baselineRightCorner = baseline[rightCornerIndex];
                const smileLeftCorner = smile[leftCornerIndex];
                const smileRightCorner = smile[rightCornerIndex];

                // Calculate face width for normalization (distance between outer canthi in normalized coordinates)
                const faceWidthNormalized = Math.sqrt(
                    Math.pow(baseline[454].x - baseline[234].x, 2) +
                    Math.pow(baseline[454].y - baseline[234].y, 2)
                );

                // Calculate movement distances in normalized coordinates
                const leftMovementNormalized = Math.sqrt(
                    Math.pow(smileLeftCorner.x - baselineLeftCorner.x, 2) +
                    Math.pow(smileLeftCorner.y - baselineLeftCorner.y, 2)
                );

                const rightMovementNormalized = Math.sqrt(
                    Math.pow(smileRightCorner.x - baselineRightCorner.x, 2) +
                    Math.pow(smileRightCorner.y - baselineRightCorner.y, 2)
                );

                // Convert to millimeters using face width as reference
                // Average adult face width is ~140mm, so we normalize by face width and scale
                const estimatedFaceWidthMm = 140;
                const leftMovementMm = (leftMovementNormalized / faceWidthNormalized) * estimatedFaceWidthMm;
                const rightMovementMm = (rightMovementNormalized / faceWidthNormalized) * estimatedFaceWidthMm;

                const asymmetryPercentage = Math.abs(leftMovementMm - rightMovementMm) /
                                          Math.max(leftMovementMm, rightMovementMm) * 100;

                return {
                    leftMovement: leftMovementMm,
                    rightMovement: rightMovementMm,
                    asymmetryPercentage: asymmetryPercentage
                };
            } catch (error) {
                console.error('Error in mouth movement analysis:', error);
                return null;
            }
        }

        function storeResults() {
            if (!mockExamResults) {
                document.getElementById('storageResults').innerHTML = 
                    '<span class="error">❌ Please create mock results first!</span>';
                return;
            }

            try {
                const storageKey = 'facial_symmetry_exam_results';
                const expiryTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
                
                const dataToStore = {
                    results: mockExamResults,
                    timestamp: new Date().toISOString(),
                    expiryTime: expiryTime.toISOString()
                };

                localStorage.setItem(storageKey, JSON.stringify(dataToStore));
                
                document.getElementById('storageResults').innerHTML = 
                    `<span class="success">✅ Results stored successfully!</span>\n` +
                    `Storage key: ${storageKey}\n` +
                    `Expires: ${expiryTime.toLocaleString()}`;
            } catch (error) {
                document.getElementById('storageResults').innerHTML = 
                    `<span class="error">❌ Storage failed: ${error.message}</span>`;
            }
        }

        function loadResults() {
            try {
                const storageKey = 'facial_symmetry_exam_results';
                const storedData = localStorage.getItem(storageKey);
                
                if (!storedData) {
                    document.getElementById('storageResults').innerHTML = 
                        '<span class="error">❌ No stored results found!</span>';
                    return;
                }

                const parsedData = JSON.parse(storedData);
                const now = new Date();
                const expiryTime = new Date(parsedData.expiryTime);

                if (now > expiryTime) {
                    document.getElementById('storageResults').innerHTML = 
                        '<span class="error">❌ Stored results have expired!</span>';
                    localStorage.removeItem(storageKey);
                    return;
                }

                document.getElementById('storageResults').innerHTML = 
                    `<span class="success">✅ Results loaded successfully!</span>\n` +
                    `Patient: ${parsedData.results.patientInfo.name}\n` +
                    `Stored: ${new Date(parsedData.timestamp).toLocaleString()}\n` +
                    `Expires: ${expiryTime.toLocaleString()}`;
            } catch (error) {
                document.getElementById('storageResults').innerHTML = 
                    `<span class="error">❌ Loading failed: ${error.message}</span>`;
            }
        }

        function clearResults() {
            const storageKey = 'facial_symmetry_exam_results';
            localStorage.removeItem(storageKey);
            document.getElementById('storageResults').innerHTML = 
                '<span class="success">✅ Results cleared from storage!</span>';
        }

        function navigateToResults() {
            if (!mockExamResults) {
                document.getElementById('navigationResults').innerHTML = 
                    '<span class="error">❌ Please create and store mock results first!</span>';
                return;
            }

            // Store results first
            storeResults();
            
            // Navigate to results page
            setTimeout(() => {
                window.location.hash = '#/results';
                document.getElementById('navigationResults').innerHTML = 
                    '<span class="success">✅ Navigating to results page...</span>';
            }, 500);
        }
    </script>
</body>
</html>
