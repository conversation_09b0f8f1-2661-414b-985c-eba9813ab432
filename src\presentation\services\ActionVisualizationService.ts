// Presentation Service: Action Visualization (Third Canvas)
import { Landmark } from '../../shared/types/index.js';

export type ExamAction = 'neutral' | 'eyebrow_raise' | 'eye_close' | 'smile' | 'lip_pucker' | 'cheek_puff';

export class ActionVisualizationService {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private currentLandmarks: Landmark[] = [];
  private currentAction: ExamAction = 'neutral';
  private actionProgress: number = 0;

  constructor(canvasId: string) {
    this.canvas = document.getElementById(canvasId) as HTMLCanvasElement;
    this.ctx = this.canvas.getContext('2d') as CanvasRenderingContext2D;

    if (!this.canvas || !this.ctx) {
      throw new Error(`Action canvas element with id '${canvasId}' not found`);
    }

    this.setupCanvas();
  }

  private setupCanvas(): void {
    // Set canvas background
    this.ctx.fillStyle = '#111';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // Add initial text
    this.ctx.fillStyle = '#ff6600';
    this.ctx.font = '20px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('Action View', this.canvas.width / 2, this.canvas.height / 2);
    this.ctx.font = '14px Arial';
    this.ctx.fillText('Waiting for exam to start...', this.canvas.width / 2, this.canvas.height / 2 + 30);
  }

  setCurrentAction(action: ExamAction): void {
    this.currentAction = action;
    this.updateActionView();
  }

  updateLandmarks(landmarks: Landmark[]): void {
    console.log('ActionVisualizationService: Received landmarks:', landmarks ? landmarks.length : 'null');
    this.currentLandmarks = landmarks;
    this.updateActionView();
  }

  setActionProgress(progress: number): void {
    this.actionProgress = Math.max(0, Math.min(100, progress));
    this.updateActionView();
  }

  private updateActionView(): void {
    this.clearCanvasInternal();

    if (!this.currentLandmarks || this.currentLandmarks.length === 0) {
      this.showWaitingMessage();
      return;
    }

    this.drawActionInstructions();
    this.drawActionProgress();
    this.drawActionSpecificVisualization();
    this.drawActionFeedback();
  }

  private clearCanvasInternal(): void {
    this.ctx.fillStyle = '#111';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
  }

  private showWaitingMessage(): void {
    this.ctx.fillStyle = '#ff6600';
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('Waiting for face detection...', this.canvas.width / 2, this.canvas.height / 2);
  }

  private drawActionInstructions(): void {
    const instructions = this.getActionInstructions();

    // Background for instructions
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(10, 10, this.canvas.width - 20, 80);

    // Border
    this.ctx.strokeStyle = '#ff6600';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(10, 10, this.canvas.width - 20, 80);

    // Action title
    this.ctx.fillStyle = '#ff6600';
    this.ctx.font = 'bold 18px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(instructions.title, this.canvas.width / 2, 35);

    // Instructions
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '14px Arial';
    this.ctx.fillText(instructions.instruction, this.canvas.width / 2, 55);

    // Tips
    this.ctx.fillStyle = '#cccccc';
    this.ctx.font = '12px Arial';
    this.ctx.fillText(instructions.tip, this.canvas.width / 2, 75);
  }

  private getActionInstructions(): { title: string; instruction: string; tip: string } {
    switch (this.currentAction) {
      case 'neutral':
        return {
          title: 'Neutral Expression',
          instruction: 'Maintain a relaxed, neutral facial expression',
          tip: 'Look directly at the camera with a calm face'
        };
      case 'eyebrow_raise':
        return {
          title: 'Eyebrow Raise',
          instruction: 'Raise both eyebrows as high as possible',
          tip: 'Keep your eyes open and mouth relaxed'
        };
      case 'eye_close':
        return {
          title: 'Eye Closure',
          instruction: 'Close both eyes tightly',
          tip: 'Squeeze your eyelids together firmly'
        };
      case 'smile':
        return {
          title: 'Smile',
          instruction: 'Show your biggest smile',
          tip: 'Smile with both your mouth and eyes'
        };
      case 'lip_pucker':
        return {
          title: 'Lip Pucker',
          instruction: 'Pucker your lips as if kissing',
          tip: 'Push your lips forward and together'
        };
      case 'cheek_puff':
        return {
          title: 'Cheek Puff',
          instruction: 'Puff out both cheeks with air',
          tip: 'Fill your cheeks with air and hold'
        };
      default:
        return {
          title: 'Unknown Action',
          instruction: 'Follow the instructions',
          tip: 'Perform the requested facial expression'
        };
    }
  }

  private drawActionProgress(): void {
    // Progress bar background
    const barX = 50;
    const barY = this.canvas.height - 60;
    const barWidth = this.canvas.width - 100;
    const barHeight = 20;

    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(barX - 5, barY - 25, barWidth + 10, 40);

    this.ctx.strokeStyle = '#666';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(barX, barY, barWidth, barHeight);

    // Progress fill
    const progressWidth = (this.actionProgress / 100) * barWidth;
    const progressColor = this.actionProgress < 50 ? '#ff6600' : '#00ff00';

    this.ctx.fillStyle = progressColor;
    this.ctx.fillRect(barX, barY, progressWidth, barHeight);

    // Progress text
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '12px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`Progress: ${this.actionProgress.toFixed(0)}%`, this.canvas.width / 2, barY - 8);
  }

  private drawActionSpecificVisualization(): void {
    switch (this.currentAction) {
      case 'eyebrow_raise':
        this.drawEyebrowHighlight();
        break;
      case 'eye_close':
        this.drawEyeHighlight();
        break;
      case 'smile':
        this.drawMouthHighlight();
        break;
      case 'lip_pucker':
        this.drawLipHighlight();
        break;
      case 'cheek_puff':
        this.drawCheekHighlight();
        break;
      default:
        this.drawFaceOutline();
        break;
    }
  }

  private drawEyebrowHighlight(): void {
    // Highlight eyebrow area with correct MediaPipe landmarks
    // Left eyebrow landmarks (MediaPipe face mesh)
    const leftEyebrowIndices = [70, 63, 105, 66, 107, 55, 65, 52, 53, 46];
    // Right eyebrow landmarks (MediaPipe face mesh)
    const rightEyebrowIndices = [300, 293, 334, 296, 336, 285, 295, 282, 283, 276];

    // Forehead area for eyebrow elevation
    const foreheadIndices = [9, 10, 151];

    this.drawHighlightedFeature([...leftEyebrowIndices, ...rightEyebrowIndices, ...foreheadIndices], '#ffff00', 'Raise Eyebrows');
  }

  private drawEyeHighlight(): void {
    // Highlight eye area with correct MediaPipe landmarks
    // Left eye contour landmarks
    const leftEyeIndices = [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246];
    // Right eye contour landmarks
    const rightEyeIndices = [362, 398, 384, 385, 386, 387, 388, 466, 263, 249, 390, 373, 374, 380, 381, 382];

    this.drawHighlightedFeature([...leftEyeIndices, ...rightEyeIndices], '#00ffff', 'Close Eyes');
  }

  private drawMouthHighlight(): void {
    // Highlight mouth area for smiling with correct MediaPipe landmarks
    // Outer lip contour
    const outerLipIndices = [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318, 402, 317, 14, 87, 178, 88, 95, 78, 191, 80, 81, 82];
    // Inner lip contour
    const innerLipIndices = [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415, 310, 311, 312, 13, 82, 81, 80, 191];

    this.drawHighlightedFeature([...outerLipIndices, ...innerLipIndices], '#ff00ff', 'Smile Wide');
  }

  private drawLipHighlight(): void {
    // Highlight lip area specifically for puckering with correct MediaPipe landmarks
    // Upper lip landmarks
    const upperLipIndices = [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318];
    // Lower lip landmarks
    const lowerLipIndices = [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415];
    // Lip corners
    const lipCornerIndices = [61, 291, 39, 181, 84, 17, 314, 405];

    this.drawHighlightedFeature([...upperLipIndices, ...lowerLipIndices, ...lipCornerIndices], '#ff69b4', 'Pucker Lips');
  }

  private drawCheekHighlight(): void {
    // Highlight cheek area for puffing with correct MediaPipe landmarks
    // Left cheek area landmarks
    const leftCheekIndices = [116, 117, 118, 119, 120, 121, 126, 142, 36, 205, 206, 207, 213, 192, 147];
    // Right cheek area landmarks
    const rightCheekIndices = [345, 346, 347, 348, 349, 350, 355, 371, 266, 425, 426, 427, 436, 416, 376];

    this.drawHighlightedFeature([...leftCheekIndices, ...rightCheekIndices], '#ffa500', 'Puff Cheeks');
  }

  private drawFaceOutline(): void {
    // Draw basic face outline for neutral with correct MediaPipe landmarks
    // Face contour landmarks (perimeter of face)
    const faceOutlineIndices = [10, 338, 297, 332, 284, 251, 389, 356, 454, 323, 361, 288, 397, 365, 379, 378, 400, 377, 152, 148, 176, 149, 150, 136, 172, 58, 132, 93, 234, 127, 162, 21, 54, 103, 67, 109];

    this.drawHighlightedFeature(faceOutlineIndices, '#888888', 'Neutral Face');
  }

  private drawHighlightedFeature(indices: number[], color: string, label: string): void {
    const points = indices.map(idx => this.currentLandmarks[idx]).filter(Boolean);

    console.log(`Drawing ${label}: ${points.length}/${indices.length} landmarks found`);

    if (points.length === 0) {
      console.log(`No landmarks found for ${label}`);
      return;
    }

    // Draw highlighted points (smaller markers)
    this.ctx.fillStyle = color;
    this.ctx.strokeStyle = color;
    this.ctx.lineWidth = 1;

    for (const point of points) {
      this.ctx.beginPath();
      this.ctx.arc(
        point.x * this.canvas.width,
        point.y * this.canvas.height,
        2,
        0,
        2 * Math.PI
      );
      this.ctx.fill();
      this.ctx.stroke();
    }

    // Draw feature label positioned to avoid obscuring landmarks
    if (points.length > 0) {
      const centerX = points.reduce((sum, p) => sum + p.x, 0) / points.length * this.canvas.width;
      const centerY = points.reduce((sum, p) => sum + p.y, 0) / points.length * this.canvas.height;

      // Calculate optimal label position to avoid landmarks
      let labelX = centerX;
      let labelY = centerY;

      // Position label based on feature type and location
      switch (label) {
        case 'Raise Eyebrows':
          // Position above eyebrows
          labelY = Math.min(...points.map(p => p.y)) * this.canvas.height - 25;
          break;
        case 'Close Eyes':
          // Position to the right of eyes
          labelX = Math.max(...points.map(p => p.x)) * this.canvas.width + 20;
          break;
        case 'Smile Wide':
        case 'Pucker Lips':
          // Position below mouth
          labelY = Math.max(...points.map(p => p.y)) * this.canvas.height + 25;
          break;
        case 'Puff Cheeks':
          // Position to the side of cheeks
          labelX = this.canvas.width - 80;
          labelY = centerY;
          break;
        case 'Neutral Face':
          // Position at top-right corner
          labelX = this.canvas.width - 80;
          labelY = 30;
          break;
        default:
          // Default: offset to avoid center
          labelX = centerX + 40;
          labelY = centerY - 20;
      }

      // Ensure label stays within canvas bounds
      labelX = Math.max(40, Math.min(labelX, this.canvas.width - 40));
      labelY = Math.max(20, Math.min(labelY, this.canvas.height - 10));

      // Draw semi-transparent background with rounded corners
      const textWidth = this.ctx.measureText(label).width;
      const padding = 8;
      const bgWidth = textWidth + (padding * 2);
      const bgHeight = 18;
      const bgX = labelX - bgWidth / 2;
      const bgY = labelY - bgHeight / 2 - 2;

      // Rounded rectangle background (with fallback for compatibility)
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
      this.ctx.beginPath();

      // Use roundRect if available, otherwise use regular rectangle
      if (typeof this.ctx.roundRect === 'function') {
        this.ctx.roundRect(bgX, bgY, bgWidth, bgHeight, 4);
      } else {
        this.ctx.rect(bgX, bgY, bgWidth, bgHeight);
      }
      this.ctx.fill();

      // Border for better visibility
      this.ctx.strokeStyle = color;
      this.ctx.lineWidth = 1;
      this.ctx.stroke();

      // Draw text label
      this.ctx.fillStyle = color;
      this.ctx.font = 'bold 11px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(label, labelX, labelY + 2);

      // Draw connecting line from label to feature center (subtle)
      if (Math.abs(labelX - centerX) > 30 || Math.abs(labelY - centerY) > 30) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 1;
        this.ctx.globalAlpha = 0.4;
        this.ctx.setLineDash([2, 3]);
        this.ctx.beginPath();
        this.ctx.moveTo(labelX, labelY + 8);
        this.ctx.lineTo(centerX, centerY);
        this.ctx.stroke();
        this.ctx.setLineDash([]);
        this.ctx.globalAlpha = 1.0;
      }
    }
  }

  private drawActionFeedback(): void {
    // Performance feedback based on action progress
    let feedback: string;
    let feedbackColor: string;

    if (this.actionProgress < 30) {
      feedback = 'Keep going...';
      feedbackColor = '#ff6600';
    } else if (this.actionProgress < 70) {
      feedback = 'Good progress!';
      feedbackColor = '#ffff00';
    } else if (this.actionProgress < 90) {
      feedback = 'Almost there!';
      feedbackColor = '#90ee90';
    } else {
      feedback = 'Perfect!';
      feedbackColor = '#00ff00';
    }

    // Feedback background
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(this.canvas.width - 120, this.canvas.height - 120, 110, 50);

    this.ctx.strokeStyle = feedbackColor;
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(this.canvas.width - 120, this.canvas.height - 120, 110, 50);

    // Feedback text
    this.ctx.fillStyle = feedbackColor;
    this.ctx.font = 'bold 14px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(feedback, this.canvas.width - 65, this.canvas.height - 90);
  }

  // Public method to clear canvas
  public clearCanvas(): void {
    if (this.ctx) {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
    this.currentLandmarks = [];
  }
}
