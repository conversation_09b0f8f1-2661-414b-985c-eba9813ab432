// Application Use Case: Export Results
import { ExamSession } from '../../domain/entities/ExamSession.js';
import { IExamRepository } from '../../domain/repositories/IExamRepository.js';

export interface ExportResultsRequest {
  session: ExamSession;
  format: 'csv' | 'markdown';
}

export class ExportResultsUseCase {
  constructor(private examRepository: IExamRepository) {}

  async execute(request: ExportResultsRequest): Promise<string> {
    const { session, format } = request;
    
    if (!session.isCompleted()) {
      throw new Error('Exam session is not completed');
    }

    return await this.examRepository.exportResults(format);
  }
}
