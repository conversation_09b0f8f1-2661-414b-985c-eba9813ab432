// Domain Entity: Patient
export class Patient {
  constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly age: string,
    public readonly date: string
  ) {}

  static create(data: {
    id: string;
    name: string;
    age: string;
    date?: string;
  }): Patient {
    return new Patient(
      data.id,
      data.name,
      data.age,
      data.date || new Date().toISOString()
    );
  }

  isValid(): boolean {
    return !!(this.id && this.name && this.age);
  }
}
