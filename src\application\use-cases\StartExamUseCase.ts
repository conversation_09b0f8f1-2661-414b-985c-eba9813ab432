// Application Use Case: Start Exam
import { Patient } from '../../domain/entities/Patient.js';
import { ExamSession } from '../../domain/entities/ExamSession.js';
import { ExamAction } from '../../domain/entities/ExamAction.js';
import { IExamRepository } from '../../domain/repositories/IExamRepository.js';
import { ICameraRepository } from '../../domain/repositories/ICameraRepository.js';

export interface StartExamRequest {
  patientData: {
    id: string;
    name: string;
    age: string;
  };
  videoElement: HTMLVideoElement;
}

export class StartExamUseCase {
  constructor(
    private examRepository: IExamRepository,
    private cameraRepository: ICameraRepository,
    private examActions: ExamAction[]
  ) {}

  async execute(request: StartExamRequest): Promise<ExamSession> {
    // Create patient entity
    const patient = Patient.create(request.patientData);
    
    if (!patient.isValid()) {
      throw new Error('Invalid patient data');
    }

    // Create exam session
    const session = new ExamSession(patient, this.examActions);
    
    // Start camera
    await this.cameraRepository.startCamera(request.videoElement);
    
    // Save session
    await this.examRepository.saveSession(session);
    
    // Start the exam
    session.start();
    
    return session;
  }
}
