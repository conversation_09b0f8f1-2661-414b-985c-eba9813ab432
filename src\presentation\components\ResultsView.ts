// Import removed - export functionality now implemented directly in ResultsView
// ResultsView.ts
// Handles rendering of the results page and exporting results in various formats

import { VersionManager } from '../../core/version/VersionManager.js';

export class ResultsView {
  private examResults: any;
  private clinicalComparisonService: any;

  constructor(clinicalComparisonService: any) {
    this.clinicalComparisonService = clinicalComparisonService;
  }

  setResults(results: any) {
    console.log('ResultsView: Full results object:', JSON.stringify(results, null, 2));
    console.log('ResultsView: Symmetry metrics:', results.symmetryMetrics);
    // Calculate normalization factor using landmarks 33 and 263
    const normalizationFactor = Math.sqrt(
      Math.pow(results.symmetryMetrics?.normalizationLandmark2?.x - results.symmetryMetrics?.normalizationLandmark1?.x, 2) +
      Math.pow(results.symmetryMetrics?.normalizationLandmark2?.y - results.symmetryMetrics?.normalizationLandmark1?.y, 2)
    ) || 1; // Default to 1 if calculation fails

    console.log('ResultsView: Mouth measurements:', {
      horizontal: {
        left: results.symmetryMetrics?.leftHorizontalDistance,
        right: results.symmetryMetrics?.rightHorizontalDistance,
        asymmetry: results.symmetryMetrics?.horizontalAsymmetry,
        normalized: {
          left: (results.symmetryMetrics?.leftHorizontalDistance / normalizationFactor) * 100,
          right: (results.symmetryMetrics?.rightHorizontalDistance / normalizationFactor) * 100
        }
      },
      vertical: {
        left: results.symmetryMetrics?.leftVerticalDistance,
        right: results.symmetryMetrics?.rightVerticalDistance,
        asymmetry: results.symmetryMetrics?.verticalAsymmetry,
        normalized: {
          left: (results.symmetryMetrics?.leftVerticalDistance / normalizationFactor) * 100,
          right: (results.symmetryMetrics?.rightVerticalDistance / normalizationFactor) * 100
        }
      },
      normalizationFactor
    });
    this.examResults = results;
  }

  renderResultsPage(): void {
    if (!this.examResults) return;

    // Debug: Log the symmetry metrics to see if House-Brackmann grade is present
    console.log('ResultsView: Symmetry metrics:', this.examResults.symmetryMetrics);
    console.log('ResultsView: House-Brackmann grade:', this.examResults.symmetryMetrics?.housebrackmannGrade);

    // Remove existing results page if any
    const existingResults = document.getElementById('resultsPage');
    if (existingResults) existingResults.remove();
    // Insert results page into document
    document.body.insertAdjacentHTML('beforeend', this.generateResultsHTML());
    this.setupResultsEventListeners();
  }

  /**
   * Generate results HTML for the dedicated results route
   * This method is now public and can be called by ResultsController
   */
  generateResultsHTML(): string {
    return this.generateResultsHTMLInternal();
  }

  /**
   * Set up event listeners for the results page
   * This should be called after the HTML is inserted into the DOM
   */
  setupEventListeners(): void {
    // Use setTimeout to ensure DOM is ready
    setTimeout(() => {
      this.setupResultsEventListeners();
    }, 100);
  }

  // --- The following methods are adapted from ExamController ---
  private generateResultsHTMLInternal(): string {
    const results = this.examResults;
    const patient = results.patientInfo;
    return `
      <!-- Print Styles -->
      <style>
        @media print {
          body { margin: 0; padding: 0; background: white !important; }
          #resultsPage {
            background: white !important;
            padding: 10px !important;
            min-height: auto !important;
          }
          .print-hide { display: none !important; }
          .print-break { page-break-after: always; }
          h1, h2, h3 { color: #000 !important; }
          .gradient-bg { background: #f8f9fa !important; color: #000 !important; }
          .card-shadow { box-shadow: none !important; border: 1px solid #ddd !important; }
        }
      </style>
      <div id="resultsPage" style="
        background: #f6f8fa;
        min-height: 100vh;
        padding: 20px;
        font-family: 'Segoe UI', Arial, sans-serif;
      ">
        <div style="
          max-width: 1200px;
          margin: 0 auto;
          background: white;
          border-radius: 12px;
          box-shadow: 0 4px 16px rgba(44,62,80,0.12);
          overflow: hidden;
        ">
          <!-- Header -->
          <div style="
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            padding: 30px;
            text-align: center;
          ">
            <h1 style="margin: 0; font-size: 2.2em; font-weight: 300;">
              Facial Symmetry Analysis Results
            </h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 1.1em;">
              Complete Examination Report
            </p>
          </div>

          <!-- Patient Information -->
          <div style="padding: 30px; border-bottom: 1px solid #e1e8ed;">
            <h2 style="color: #2c3e50; margin-bottom: 20px; font-size: 1.5em;">
              Patient Information
            </h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
              <div>
                <strong>Patient ID:</strong> ${patient.id || 'N/A'}
              </div>
              <div>
                <strong>Name:</strong> ${patient.name || 'N/A'}
              </div>
              <div>
                <strong>Age:</strong> ${patient.age || 'N/A'} years
              </div>
              <div>
                <strong>Exam Date:</strong> ${new Date(results.timestamp).toLocaleDateString()}
              </div>
              <div>
                <strong>Exam Time:</strong> ${new Date(results.timestamp).toLocaleTimeString()}
              </div>
              <div>
                <strong>Overall Score:</strong>
                <span style="
                  color: ${results.overallScore >= 85 ? '#27ae60' : results.overallScore >= 70 ? '#f39c12' : '#e74c3c'};
                  font-weight: bold;
                ">${results.overallScore}%</span>
              </div>
            </div>
          </div>

          <!-- Symmetry Metrics -->
          <div style="padding: 30px; border-bottom: 1px solid #e1e8ed;">
            <h2 style="color: #2c3e50; margin-bottom: 20px; font-size: 1.5em;">
              Symmetry Measurements
            </h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
              ${this.generateDetailedMetricCard('Eyebrow Analysis', results.symmetryMetrics)}
              ${this.generateDetailedMetricCard('Eye Analysis', results.symmetryMetrics)}
              ${this.generateDetailedMetricCard('Mouth Analysis', results.symmetryMetrics)}
            </div>

          </div>

          <!-- Overall Facial Symmetry Score -->
          <div style="padding: 30px; border-bottom: 1px solid #e1e8ed;">
            <h2 style="color: #2c3e50; margin-bottom: 20px; font-size: 1.5em;">
              Overall Facial Symmetry Score
            </h2>
            ${this.generateOverallSymmetryScoreDisplay(results.symmetryMetrics)}
          </div>

          <!-- Synkinesis Analysis Results -->
          <div style="padding: 30px; border-bottom: 1px solid #e1e8ed;">
            <h2 style="color: #2c3e50; margin-bottom: 20px; font-size: 1.5em;">
              Synkinesis Analysis
            </h2>
            ${this.generateSynkinesisAnalysisSection(results)}
          </div>

          <!-- Action Controls -->
          <div class="print-hide" style="padding: 30px; text-align: center;">
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
              <button id="exportPdfBtn" style="
                padding: 12px 24px;
                background: linear-gradient(135deg, #dc3545, #e74c3c);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
                display: flex;
                align-items: center;
                gap: 8px;
              " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(220, 53, 69, 0.4)'"
                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(220, 53, 69, 0.3)'">
                📄 Download PDF Report
              </button>

              <button id="printResultsBtn" style="
                padding: 12px 24px;
                background: #34495e;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 1em;
                font-weight: 600;
                cursor: pointer;
                transition: background 0.2s;
              ">Print Results</button>
            </div>
          </div>
        </div>

        <!-- Simplified Version Information Footer -->
        ${this.generateSimpleVersionFooter()}

      </div>
    `;
  }

  /**
   * Generate simplified version footer for the results page
   */
  private generateSimpleVersionFooter(): string {
    const versionMetadata = VersionManager.getExportMetadata();

    // Get release date from version metadata or use current date as fallback
    const releaseDate = versionMetadata.releaseDate || new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return `
      <div style="
        margin-top: 40px;
        padding: 20px 30px;
        border-top: 1px solid #e1e8ed;
        background: #f8f9fa;
        text-align: center;
      ">
        <div style="
          color: #6c757d;
          font-size: 0.9em;
          line-height: 1.5;
        ">
          <div style="margin-bottom: 5px;">
            Facial Symmetry Analysis Application
          </div>
          <div style="display: flex; justify-content: center; align-items: center; gap: 20px; flex-wrap: wrap;">
            <span>Version ${versionMetadata.applicationVersion}</span>
            <span>•</span>
            <span>Released: ${releaseDate}</span>
          </div>
        </div>
      </div>
    `;
  }

  private generateClinicalAnalysisSection(results: any): string { return ''; }
  private generateClinicalComparisonSection(results: any): string { return ''; }
  private generateRegionalAnalysisCard(title: string, analysis: any): string { return ''; }
  /**
   * Generate individual synkinesis card with detailed information
   */
  private generateSynkinesisCard(title: string, description: string, synkinesisData: any, icon: string): string {
    if (!synkinesisData.present) {
      return `
        <div style="
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          border-left: 6px solid #27ae60;
          text-align: center;
        ">
          <div style="font-size: 2em; margin-bottom: 15px;">${icon}</div>
          <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 1.1em;">${title}</h4>
          <p style="margin: 0 0 15px 0; color: #7f8c8d; font-size: 0.9em;">${description}</p>
          <div style="
            background: #d4edda;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
          ">
            <div style="color: #27ae60; font-weight: bold; font-size: 1.1em;">
              Not Detected
            </div>
            <div style="color: #155724; font-size: 0.9em; margin-top: 5px;">
              Normal facial nerve function
            </div>
          </div>
        </div>
      `;
    }

    const severityColor = this.getSeverityColor(synkinesisData.severity);
    const percentage = synkinesisData.percentage || 0;

    return `
      <div style="
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-left: 6px solid ${severityColor};
      ">
        <div style="display: flex; align-items: center; margin-bottom: 20px;">
          <div style="font-size: 2em; margin-right: 15px;">${icon}</div>
          <div>
            <h4 style="margin: 0 0 5px 0; color: #2c3e50; font-size: 1.1em;">${title}</h4>
            <p style="margin: 0; color: #7f8c8d; font-size: 0.9em;">${description}</p>
          </div>
        </div>

        <div style="
          background: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        ">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <span style="color: #7f8c8d;">Severity:</span>
            <span style="
              background: ${severityColor};
              color: white;
              padding: 4px 12px;
              border-radius: 20px;
              font-size: 0.9em;
              font-weight: 600;
            ">
              ${synkinesisData.severity}
            </span>
          </div>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="color: #7f8c8d;">Unintended Movement:</span>
            <span style="font-weight: bold; color: ${severityColor}; font-size: 1.2em;">
              ${percentage.toFixed(1)}%
            </span>
          </div>
        </div>

        <div style="
          background: ${this.getSynkinesisRecommendationBackground(synkinesisData.severity)};
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid ${severityColor};
        ">
          <div style="font-size: 0.9em; color: #2c3e50; font-weight: 600; margin-bottom: 5px;">
            Clinical Recommendation
          </div>
          <div style="color: #2c3e50; font-size: 0.9em;">
            ${this.getSynkinesisRecommendation(synkinesisData.severity)}
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Generate clinical interpretation for synkinesis findings
   */
  private generateSynkinesisClinicalInterpretation(eyeMouthSynkinesis: any, mouthEyeSynkinesis: any): string {
    const hasEyeMouth = eyeMouthSynkinesis.present;
    const hasMouthEye = mouthEyeSynkinesis.present;

    if (!hasEyeMouth && !hasMouthEye) {
      return `
        <div style="
          background: #d4edda;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #27ae60;
        ">
          <p style="margin: 0; color: #155724; line-height: 1.6;">
            No synkinesis patterns detected. This indicates excellent facial nerve recovery with proper reinnervation patterns. The facial nerve has regenerated correctly without abnormal cross-connections between muscle groups.
          </p>
        </div>
      `;
    }

    const avgPercentage = (eyeMouthSynkinesis.percentage + mouthEyeSynkinesis.percentage) / 2;
    let interpretationColor = '#ffc107';
    let interpretationBackground = '#fff3cd';
    let clinicalSignificance = '';

    if (avgPercentage > 35) {
      interpretationColor = '#dc3545';
      interpretationBackground = '#f8d7da';
      clinicalSignificance = 'Severe synkinesis requiring immediate intervention. Consider botulinum toxin therapy and intensive neuromuscular retraining.';
    } else if (avgPercentage > 25) {
      interpretationColor = '#fd7e14';
      interpretationBackground = '#fff3cd';
      clinicalSignificance = 'Moderate synkinesis that may benefit from targeted therapy. Neuromuscular retraining and selective botulinum toxin treatment recommended.';
    } else {
      interpretationColor = '#ffc107';
      interpretationBackground = '#fff3cd';
      clinicalSignificance = 'Mild synkinesis detected. Monitor progression and consider neuromuscular retraining exercises to prevent worsening.';
    }

    return `
      <div style="
        background: ${interpretationBackground};
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid ${interpretationColor};
        margin-bottom: 20px;
      ">
        <p style="margin: 0 0 15px 0; color: #2c3e50; line-height: 1.6;">
          Synkinesis patterns have been detected, indicating aberrant facial nerve regeneration. This occurs when nerve fibers reconnect to unintended muscle groups during the healing process after facial nerve injury.
        </p>
        <div style="
          background: white;
          padding: 15px;
          border-radius: 6px;
          margin-top: 15px;
        ">
          <div style="font-weight: 600; color: #2c3e50; margin-bottom: 10px;">
            Clinical Significance:
          </div>
          <div style="color: #2c3e50;">
            ${clinicalSignificance}
          </div>
        </div>
      </div>

      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <div>
          <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 1em;">
            Treatment Options
          </h4>
          <ul style="margin: 0; padding-left: 20px; color: #7f8c8d; line-height: 1.6;">
            <li>Neuromuscular retraining therapy</li>
            <li>Selective botulinum toxin injections</li>
            <li>Facial massage and exercises</li>
            <li>Biofeedback training</li>
          </ul>
        </div>
        <div>
          <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 1em;">
            Follow-up Care
          </h4>
          <ul style="margin: 0; padding-left: 20px; color: #7f8c8d; line-height: 1.6;">
            <li>Regular monitoring every 3-6 months</li>
            <li>Functional assessment tracking</li>
            <li>Patient education and support</li>
            <li>Adjustment of treatment plan as needed</li>
          </ul>
        </div>
      </div>
    `;
  }

  /**
   * Get recommendation background color based on severity
   */
  private getSynkinesisRecommendationBackground(severity: string): string {
    switch (severity.toLowerCase()) {
      case 'mild':
        return '#fff3cd';
      case 'moderate':
        return '#fff3cd';
      case 'severe':
        return '#f8d7da';
      default:
        return '#f8f9fa';
    }
  }

  /**
   * Get clinical recommendation based on synkinesis severity
   */
  private getSynkinesisRecommendation(severity: string): string {
    switch (severity.toLowerCase()) {
      case 'mild':
        return 'Monitor progression with regular follow-up. Consider neuromuscular retraining exercises.';
      case 'moderate':
        return 'Neuromuscular retraining therapy recommended. Evaluate for selective botulinum toxin treatment.';
      case 'severe':
        return 'Immediate intervention required. Botulinum toxin therapy and intensive rehabilitation recommended.';
      default:
        return 'Consult with facial nerve specialist for personalized treatment plan.';
    }
  }
  private generateScoreCard(title: string, value: number, maxValue: number): string { return ''; }
  private getSeverityColor(severity: string): string {
    switch (severity.toLowerCase()) {
      case 'mild':
        return '#f39c12'; // Orange
      case 'moderate':
        return '#e67e22'; // Dark orange
      case 'severe':
        return '#e74c3c'; // Red
      case 'critical':
        return '#c0392b'; // Dark red
      default:
        return '#7f8c8d'; // Gray
    }
  }
  private getHBGradeDescription(grade: number): string { return ''; }
  private generateComparisonActionCard(title: string, comparison: any): string { return ''; }
  private generateSeverityDistributionCard(severity: string, count: number): string { return ''; }

  /**
   * Generate detailed metric card showing individual measurements
   */
  private generateDetailedMetricCard(title: string, metrics: any): string {
    let content = '';
    let overallScore = 0;
    let overallStatus = '';
    let statusColor = '#7f8c8d';

    switch (title) {
      case 'Eyebrow Analysis':
        const leftEyebrow = metrics.leftEyebrowElevation || 0;
        const rightEyebrow = metrics.rightEyebrowElevation || 0;
        const eyebrowAsymmetry = metrics.eyebrowAsymmetry || 0;
        overallScore = metrics.eyebrowSymmetry || 0;

        content = `
          <div style="margin-bottom: 15px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>Left Eyebrow Elevation:</span>
              <span style="font-weight: bold;">${leftEyebrow.toFixed(2)}°</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>Right Eyebrow Elevation:</span>
              <span style="font-weight: bold;">${rightEyebrow.toFixed(2)}°</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>Asymmetry:</span>
              <span style="font-weight: bold; color: ${this.getSeverityColor(this.getSeverityFromPercentage(eyebrowAsymmetry))}">${eyebrowAsymmetry.toFixed(1)}%</span>
            </div>
          </div>
        `;
        break;

      case 'Eye Analysis':
        const leftEye = metrics.leftEyeClosure || 0;
        const rightEye = metrics.rightEyeClosure || 0;
        const eyeAsymmetry = metrics.eyeAsymmetry || 0;
        overallScore = metrics.eyeSymmetry || 0;

        content = `
          <div style="margin-bottom: 15px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>Left Eye Closure:</span>
              <span style="font-weight: bold;">${leftEye.toFixed(1)}%</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>Right Eye Closure:</span>
              <span style="font-weight: bold;">${rightEye.toFixed(1)}%</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>Asymmetry:</span>
              <span style="font-weight: bold; color: ${this.getSeverityColor(this.getSeverityFromPercentage(eyeAsymmetry))}">${eyeAsymmetry.toFixed(1)}%</span>
            </div>
          </div>
        `;
        break;

      case 'Mouth Analysis':
        const leftMouth = metrics.leftMouthMovement || 0;
        const rightMouth = metrics.rightMouthMovement || 0;
        const mouthAsymmetry = metrics.mouthAsymmetry || 0;
        const commissureDroop = metrics.commissureDroop || 0;
        
        // Enhanced measurements with horizontal and vertical distances
        const leftHorizontalDistance = metrics.leftHorizontalDistance || 0;
        const rightHorizontalDistance = metrics.rightHorizontalDistance || 0;
        const leftVerticalDistance = metrics.leftVerticalDistance || 0;
        const rightVerticalDistance = metrics.rightVerticalDistance || 0;
        const horizontalAsymmetry = metrics.horizontalAsymmetry || 0;
        const verticalAsymmetry = metrics.verticalAsymmetry || 0;
        
        overallScore = metrics.mouthSymmetry || 0;

        content = `
          <div style="margin-bottom: 15px;">
            <!-- Basic Movement Measurements -->
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>Left Corner Movement:</span>
              <span style="font-weight: bold;">${leftMouth.toFixed(2)}mm</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>Right Corner Movement:</span>
              <span style="font-weight: bold;">${rightMouth.toFixed(2)}mm</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>Overall Asymmetry:</span>
              <span style="font-weight: bold; color: ${this.getSeverityColor(this.getSeverityFromPercentage(mouthAsymmetry))}">${mouthAsymmetry.toFixed(1)}%</span>
            </div>
            
            <!-- Horizontal Distance Measurements -->
            <div style="border-top: 1px solid #e1e8ed; padding-top: 8px; margin-top: 12px; margin-bottom: 8px;">
              <div style="font-weight: 600; color: #2c3e50; margin-bottom: 8px; font-size: 0.95em;">
                📏 Horizontal Distances (to tilted reference line)
              </div>
              <div style="display: flex; justify-content: space-between; margin-bottom: 6px; padding-left: 10px;">
                <span style="color: #6c757d;">Left Corner:</span>
                <span style="font-weight: bold;">${leftHorizontalDistance.toFixed(2)}px</span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-bottom: 6px; padding-left: 10px;">
                <span style="color: #6c757d;">Right Corner:</span>
                <span style="font-weight: bold;">${rightHorizontalDistance.toFixed(2)}px</span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding-left: 10px;">
                <span style="color: #6c757d;">Horizontal Asymmetry:</span>
                <span style="font-weight: bold; color: ${this.getSeverityColor(this.getSeverityFromPercentage(horizontalAsymmetry))}">${horizontalAsymmetry.toFixed(1)}%</span>
              </div>
            </div>
            
            <!-- Vertical Distance Measurements -->
            <div style="border-top: 1px solid #e1e8ed; padding-top: 8px; margin-bottom: 8px;">
              <div style="font-weight: 600; color: #2c3e50; margin-bottom: 8px; font-size: 0.95em;">
                📐 Vertical Distances (to vertical reference line)
              </div>
              <div style="display: flex; justify-content: space-between; margin-bottom: 6px; padding-left: 10px;">
                <span style="color: #6c757d;">Left Corner:</span>
                <span style="font-weight: bold;">${leftVerticalDistance.toFixed(2)}px</span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-bottom: 6px; padding-left: 10px;">
                <span style="color: #6c757d;">Right Corner:</span>
                <span style="font-weight: bold;">${rightVerticalDistance.toFixed(2)}px</span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding-left: 10px;">
                <span style="color: #6c757d;">Vertical Asymmetry:</span>
                <span style="font-weight: bold; color: ${this.getSeverityColor(this.getSeverityFromPercentage(verticalAsymmetry))}">${verticalAsymmetry.toFixed(1)}%</span>
              </div>
            </div>
            
            ${commissureDroop > 0 ? `
            <div style="border-top: 1px solid #e1e8ed; padding-top: 8px;">
              <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                <span>Commissure Droop:</span>
                <span style="font-weight: bold; color: ${commissureDroop > 5 ? '#e74c3c' : '#f39c12'}">${commissureDroop.toFixed(1)}%</span>
              </div>
            </div>
            ` : ''}
          </div>
        `;
        break;


    }

    overallStatus = overallScore >= 85 ? 'Excellent' : overallScore >= 70 ? 'Good' : 'Needs Attention';
    statusColor = overallScore >= 85 ? '#27ae60' : overallScore >= 70 ? '#f39c12' : '#e74c3c';

    return `
      <div style="
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-left: 4px solid ${statusColor};
      ">
        <h4 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 1.1em;">${title}</h4>
        ${content}
        <div style="border-top: 1px solid #e1e8ed; padding-top: 15px;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-weight: 600;">Overall Score:</span>
            <div style="text-align: right;">
              <div style="font-size: 1.5em; font-weight: bold; color: ${statusColor};">
                ${overallScore.toFixed(1)}%
              </div>
              <div style="color: #7f8c8d; font-size: 0.9em;">
                ${overallStatus}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Generate synkinesis analysis section with detailed results
   */
  private generateSynkinesisAnalysisSection(results: any): string {
    // Check for synkinesis data from different sources
    let synkinesisData = null;
    let hasSynkinesis = false;
    let synkinesisResults: any[] = [];

    // Try to get synkinesis data from clinical results first
    if (results.clinicalResults && results.clinicalResults.synkinesis_analysis) {
      synkinesisData = results.clinicalResults.synkinesis_analysis;
      hasSynkinesis = synkinesisData.eye_mouth_synkinesis.present || synkinesisData.mouth_eye_synkinesis.present;
    }

    // Fallback to asymmetries data which includes synkinesis detection
    if (!hasSynkinesis && results.asymmetries) {
      synkinesisResults = results.asymmetries.filter((asymmetry: any) =>
        asymmetry.feature && asymmetry.feature.toLowerCase().includes('synkinesis')
      );
      hasSynkinesis = synkinesisResults.length > 0;
    }

    if (!hasSynkinesis) {
      return this.generateNoSynkinesisDetectedCard();
    }

    if (synkinesisData) {
      return this.generateClinicalSynkinesisDisplay(synkinesisData);
    } else {
      return this.generateBasicSynkinesisDisplay(synkinesisResults);
    }
  }

  /**
   * Generate display when no synkinesis is detected
   */
  private generateNoSynkinesisDetectedCard(): string {
    return `
      <div style="
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-left: 6px solid #27ae60;
        text-align: center;
      ">
        <div style="
          font-size: 3em;
          color: #27ae60;
          margin-bottom: 20px;
        ">
          ✓
        </div>
        <h3 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 1.3em;">
          No Synkinesis Detected
        </h3>
        <p style="margin: 0; color: #7f8c8d; line-height: 1.6;">
          No involuntary facial movements were detected during the examination. This indicates good facial nerve recovery without abnormal cross-innervation patterns.
        </p>
        <div style="
          background: #f8f9fa;
          padding: 15px;
          border-radius: 8px;
          margin-top: 20px;
        ">
          <div style="font-size: 0.9em; color: #7f8c8d; margin-bottom: 5px;">
            Clinical Significance
          </div>
          <div style="color: #27ae60; font-weight: 600;">
            Excellent prognosis for facial function recovery
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Generate clinical synkinesis display from detailed analysis
   */
  private generateClinicalSynkinesisDisplay(synkinesisData: any): string {
    const eyeMouthSynkinesis = synkinesisData.eye_mouth_synkinesis;
    const mouthEyeSynkinesis = synkinesisData.mouth_eye_synkinesis;

    return `
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
        <!-- Eye-Mouth Synkinesis -->
        ${this.generateSynkinesisCard(
          'Eye-Mouth Synkinesis',
          'Involuntary mouth movement during eye closure',
          eyeMouthSynkinesis,
          '👁️ → 👄'
        )}

        <!-- Mouth-Eye Synkinesis -->
        ${this.generateSynkinesisCard(
          'Mouth-Eye Synkinesis',
          'Involuntary eye closure during smile',
          mouthEyeSynkinesis,
          '👄 → 👁️'
        )}
      </div>

      <!-- Clinical Interpretation -->
      <div style="
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        margin-top: 20px;
      ">
        <h3 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 1.3em;">
          Synkinesis Clinical Interpretation
        </h3>
        ${this.generateSynkinesisClinicalInterpretation(eyeMouthSynkinesis, mouthEyeSynkinesis)}
      </div>
    `;
  }

  /**
   * Generate basic synkinesis display from asymmetries data
   */
  private generateBasicSynkinesisDisplay(synkinesisResults: any[]): string {
    const cards = synkinesisResults.map(synkinesis => {
      const isEyeMouth = synkinesis.feature.toLowerCase().includes('eye-mouth');
      const icon = isEyeMouth ? '👁️ → 👄' : '👄 → 👁️';
      const description = isEyeMouth ?
        'Involuntary mouth movement during eye closure' :
        'Involuntary eye closure during smile';

      // Extract percentage from measurement
      const percentageMatch = synkinesis.measurement.match(/(\d+\.?\d*)%/);
      const percentage = percentageMatch ? parseFloat(percentageMatch[1]) : 0;

      const mockSynkinesisData = {
        present: true,
        severity: synkinesis.severity,
        percentage: percentage
      };

      return this.generateSynkinesisCard(
        synkinesis.feature,
        description,
        mockSynkinesisData,
        icon
      );
    }).join('');

    return `
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-bottom: 30px;">
        ${cards}
      </div>

      <!-- Basic Clinical Interpretation -->
      <div style="
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        margin-top: 20px;
      ">
        <h3 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 1.3em;">
          Synkinesis Clinical Interpretation
        </h3>
        <div style="
          background: #fff3cd;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #ffc107;
        ">
          <p style="margin: 0; color: #2c3e50; line-height: 1.6;">
            Synkinesis has been detected, indicating abnormal facial nerve regeneration patterns. This suggests that nerve fibers have reconnected to unintended muscle groups during the healing process. While this shows nerve recovery, it may require specialized treatment to improve facial function and reduce involuntary movements.
          </p>
        </div>
        <div style="margin-top: 20px;">
          <h4 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 1.1em;">
            Recommended Actions
          </h4>
          <ul style="margin: 0; padding-left: 20px; color: #7f8c8d; line-height: 1.6;">
            <li>Consider neuromuscular retraining therapy</li>
            <li>Evaluate for botulinum toxin treatment if severe</li>
            <li>Regular follow-up to monitor progression</li>
            <li>Patient education on synkinesis management</li>
          </ul>
        </div>
      </div>
    `;
  }

  /**
   * Generate Overall Facial Symmetry Score display
   */
  private generateOverallSymmetryScoreDisplay(metrics: any): string {
    // Add mobile-specific CSS override
    const mobileCSS = `
      <style>
        /* Mobile-specific override for overall symmetry score */
        @media (max-width: 768px) {
          .overall-symmetry-container {
            display: flex !important;
            flex-direction: column !important;
            gap: 20px !important;
          }

          .score-display-card,
          .clinical-interpretation-card {
            width: 100% !important;
            margin: 0 !important;
            padding: 20px !important;
            box-sizing: border-box !important;
          }

          .score-display-card h3 {
            font-size: 1.2em !important;
          }

          .score-display-card div[style*="font-size: 4em"] {
            font-size: 2.5em !important;
          }
        }
      </style>
    `;
    const eyebrowSymmetry = metrics.eyebrowSymmetry || 0;
    const eyeSymmetry = metrics.eyeSymmetry || 0;
    const mouthSymmetry = metrics.mouthSymmetry || 0;

    // FIXED: Standardized weighting system consistent across application
    const overallScore = (
      (eyeSymmetry * 0.40) +      // 40% weight for eye (most clinically important)
      (eyebrowSymmetry * 0.30) +  // 30% weight for eyebrow
      (mouthSymmetry * 0.30)      // 30% weight for mouth (standardized from 20% to 30%)
    );

    // Check for synkinesis impact (if available)
    const hasSynkinesis = this.checkForSynkinesis(metrics);
    const synkinesisReduction = hasSynkinesis ? 10 : 0; // Reduce score by 10 points if synkinesis detected
    const finalScore = Math.max(0, overallScore - synkinesisReduction);

    // Determine score color and status
    const scoreColor = finalScore >= 85 ? '#27ae60' : finalScore >= 70 ? '#f39c12' : '#e74c3c';
    const scoreStatus = finalScore >= 85 ? 'Excellent' : finalScore >= 70 ? 'Good' : 'Needs Attention';

    return `
      ${mobileCSS}
      <div class="overall-symmetry-container" style="
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
      ">
        <!-- Primary Score Display -->
        <div class="score-display-card" style="
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          border-left: 6px solid ${scoreColor};
          text-align: center;
        ">
          <h3 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 1.3em;">
            Overall Symmetry Score
          </h3>
          <div style="
            font-size: 4em;
            font-weight: bold;
            color: ${scoreColor};
            margin: 20px 0;
            line-height: 1;
          ">
            ${finalScore.toFixed(1)}
          </div>
          <div style="
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
          ">
            ${scoreStatus}
          </div>
          <div style="
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
          ">
            <div style="color: #6c757d; font-size: 0.9em; margin-bottom: 8px;">
              Score Range: 0-100
            </div>
            <div style="color: #6c757d; font-size: 0.9em;">
              ${hasSynkinesis ? `Adjusted for synkinesis (-${synkinesisReduction} points)` : 'No synkinesis detected'}
            </div>
          </div>
        </div>

        <!-- Clinical Interpretation -->
        <div class="clinical-interpretation-card" style="
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        ">
          <h3 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 1.3em;">
            Clinical Interpretation
          </h3>
          <div style="
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid ${scoreColor};
            margin-bottom: 20px;
          ">
            <p style="margin: 0; color: #2c3e50; line-height: 1.6;">
              ${this.getOverallScoreInterpretation(finalScore)}
            </p>
          </div>

          <!-- Calculation Breakdown -->
          <div style="margin-top: 20px;">
            <h4 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 1.1em;">
              Calculation Method
            </h4>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-size: 0.9em; color: #6c757d;">
              <div style="margin-bottom: 8px;">• Eye symmetry (40%): ${eyeSymmetry.toFixed(1)}%</div>
              <div style="margin-bottom: 8px;">• Eyebrow symmetry (30%): ${eyebrowSymmetry.toFixed(1)}%</div>
              <div style="margin-bottom: 8px;">• Mouth symmetry (30%): ${mouthSymmetry.toFixed(1)}%</div>
              ${hasSynkinesis ? `<div style="margin-bottom: 8px;">• Synkinesis adjustment: -${synkinesisReduction} points</div>` : ''}
              <div style="border-top: 1px solid #dee2e6; padding-top: 8px; margin-top: 8px; font-weight: 600;">
                Final Score: ${finalScore.toFixed(1)}%
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Explanatory Text -->
      <div style="
        background: #e8f4fd;
        border: 1px solid #bee5eb;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
      ">
        <h4 style="margin: 0 0 10px 0; color: #0c5460; font-size: 1.1em;">
          About This Score
        </h4>
        <p style="margin: 0; color: #0c5460; line-height: 1.6; font-size: 0.95em;">
          This overall score is calculated based on eye symmetry (40%), eyebrow symmetry (30%), mouth symmetry (30%),
          with reductions applied if synkinesis (abnormal muscle coordination) is detected. The weighting reflects
          the clinical importance of each facial region in assessing facial nerve function.
        </p>
      </div>
    `;
  }

  /**
   * Check for synkinesis in the metrics data
   */
  private checkForSynkinesis(metrics: any): boolean {
    // Check if synkinesis data is available in various possible formats
    if (metrics.synkinesisDetected !== undefined) {
      return metrics.synkinesisDetected;
    }
    if (metrics.synkinesis) {
      return metrics.synkinesis.detected || false;
    }
    if (metrics.synkinesisResults && Array.isArray(metrics.synkinesisResults)) {
      return metrics.synkinesisResults.length > 0;
    }
    // Default to false if no synkinesis data available
    return false;
  }

  /**
   * Get clinical interpretation based on overall score
   */
  private getOverallScoreInterpretation(score: number): string {
    if (score >= 90) {
      return 'Excellent facial symmetry with normal function. No significant asymmetry detected across all facial regions.';
    } else if (score >= 80) {
      return 'Good facial symmetry with minimal asymmetry. Minor variations may be present but do not significantly affect facial function.';
    } else if (score >= 70) {
      return 'Moderate facial symmetry with some noticeable asymmetry. May benefit from monitoring and potential therapeutic intervention.';
    } else if (score >= 60) {
      return 'Fair facial symmetry with moderate asymmetry present. Therapeutic intervention may be beneficial to improve function.';
    } else {
      return 'Poor facial symmetry with significant asymmetry. Comprehensive evaluation and treatment planning recommended.';
    }
  }

  /**
   * Generate enhanced House-Brackmann display with full clinical context
   * NOTE: This method is kept for backward compatibility but no longer used in the main display
   */
  private generateEnhancedHouseBrackmannDisplay(metrics: any): string {
    const grade = metrics.housebrackmannGrade;
    const eyebrowAsymmetry = metrics.eyebrowAsymmetry || 0;
    const eyeAsymmetry = metrics.eyeAsymmetry || 0;
    const mouthAsymmetry = metrics.mouthAsymmetry || 0;

    // Calculate facial asymmetry index (weighted average)
    const facialAsymmetryIndex = (
      (eyebrowAsymmetry * 0.30) +  // 30% weight for eyebrow
      (eyeAsymmetry * 0.40) +      // 40% weight for eye (most critical)
      (mouthAsymmetry * 0.30)      // 30% weight for mouth
    );

    const gradeColor = this.getHouseBrackmannGradeColor(grade);
    const gradeDescription = this.getHouseBrackmannDescription(grade);
    const clinicalInterpretation = this.getHouseBrackmannClinicalInterpretation(grade);

    return `
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
        <!-- Primary Grade Display -->
        <div style="
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          border-left: 6px solid ${gradeColor};
          text-align: center;
        ">
          <h3 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 1.3em;">
            House-Brackmann Grade
          </h3>
          <div style="
            font-size: 4em;
            font-weight: bold;
            color: ${gradeColor};
            margin: 20px 0;
            line-height: 1;
          ">
            ${this.convertToRomanNumeral(grade)}
          </div>
          <div style="
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
          ">
            ${gradeDescription}
          </div>
          <div style="
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
          ">
            <div style="font-size: 0.9em; color: #7f8c8d; margin-bottom: 5px;">
              Facial Asymmetry Index
            </div>
            <div style="font-size: 1.5em; font-weight: bold; color: ${gradeColor};">
              ${facialAsymmetryIndex.toFixed(1)}%
            </div>
          </div>
        </div>

        <!-- Clinical Interpretation -->
        <div style="
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        ">
          <h3 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 1.3em;">
            Clinical Interpretation
          </h3>
          <div style="
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid ${gradeColor};
          ">
            <p style="margin: 0; color: #2c3e50; line-height: 1.6;">
              ${clinicalInterpretation}
            </p>
          </div>

          <!-- Asymmetry Breakdown -->
          <div style="margin-top: 20px;">
            <h4 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 1.1em;">
              Regional Asymmetry Contributions
            </h4>
            <div style="display: flex; flex-direction: column; gap: 10px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="color: #7f8c8d;">Eyebrow (30% weight):</span>
                <span style="font-weight: bold; color: ${this.getSeverityColor(this.getSeverityFromPercentage(eyebrowAsymmetry))};">
                  ${eyebrowAsymmetry.toFixed(1)}%
                </span>
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="color: #7f8c8d;">Eye (40% weight):</span>
                <span style="font-weight: bold; color: ${this.getSeverityColor(this.getSeverityFromPercentage(eyeAsymmetry))};">
                  ${eyeAsymmetry.toFixed(1)}%
                </span>
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="color: #7f8c8d;">Mouth (30% weight):</span>
                <span style="font-weight: bold; color: ${this.getSeverityColor(this.getSeverityFromPercentage(mouthAsymmetry))};">
                  ${mouthAsymmetry.toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- House-Brackmann Scale Reference -->
      <div style="
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        margin-top: 20px;
      ">
        <h3 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 1.3em;">
          House-Brackmann Grading Scale Reference
        </h3>
        ${this.generateHouseBrackmannScaleTable(grade)}
      </div>
    `;
  }

  /**
   * Get House-Brackmann grade color
   */
  private getHouseBrackmannGradeColor(grade: number): string {
    switch (grade) {
      case 1:
        return '#27ae60'; // Green - Normal
      case 2:
        return '#f39c12'; // Orange - Mild
      case 3:
        return '#e67e22'; // Dark orange - Moderate
      case 4:
        return '#e74c3c'; // Red - Moderately severe
      case 5:
        return '#c0392b'; // Dark red - Severe
      case 6:
        return '#8e44ad'; // Purple - Total paralysis
      default:
        return '#7f8c8d'; // Gray - Unknown
    }
  }

  /**
   * Get House-Brackmann clinical interpretation
   */
  private getHouseBrackmannClinicalInterpretation(grade: number): string {
    switch (grade) {
      case 1:
        return 'Excellent facial function with normal symmetry. No facial weakness detected. Patient shows normal facial expressions and muscle tone on both sides of the face.';
      case 2:
        return 'Mild facial weakness that is barely noticeable during normal conversation. Slight asymmetry may be visible during close examination or with forced facial expressions.';
      case 3:
        return 'Moderate facial weakness with obvious asymmetry that does not significantly affect appearance. Patient may have difficulty with fine facial movements and expressions.';
      case 4:
        return 'Moderately severe facial weakness with obvious asymmetry that may affect facial appearance. Significant difficulty with facial expressions and possible functional impairment.';
      case 5:
        return 'Severe facial weakness with minimal facial movement. Only slight, barely perceptible motion present. Significant functional and cosmetic impairment.';
      case 6:
        return 'Complete facial paralysis with no detectable facial movement. Total loss of facial function requiring immediate medical attention and comprehensive rehabilitation.';
      default:
        return 'Unable to determine clinical interpretation. Please consult with a healthcare professional for proper assessment.';
    }
  }

  /**
   * Convert number to Roman numeral
   */
  private convertToRomanNumeral(num: number): string {
    const romanNumerals = ['I', 'II', 'III', 'IV', 'V', 'VI'];
    return romanNumerals[num - 1] || num.toString();
  }

  /**
   * Generate House-Brackmann scale reference table
   */
  private generateHouseBrackmannScaleTable(currentGrade: number): string {
    const grades = [
      { grade: 1, roman: 'I', description: 'Normal facial function', range: '<5%' },
      { grade: 2, roman: 'II', description: 'Mild dysfunction', range: '5-15%' },
      { grade: 3, roman: 'III', description: 'Moderate dysfunction', range: '15-30%' },
      { grade: 4, roman: 'IV', description: 'Moderately severe dysfunction', range: '30-50%' },
      { grade: 5, roman: 'V', description: 'Severe dysfunction', range: '50-75%' },
      { grade: 6, roman: 'VI', description: 'Total paralysis', range: '>75%' }
    ];

    const tableRows = grades.map(gradeInfo => {
      const isCurrentGrade = gradeInfo.grade === currentGrade;
      const backgroundColor = isCurrentGrade ? '#e8f5e8' : 'white';
      const fontWeight = isCurrentGrade ? 'bold' : 'normal';
      const borderLeft = isCurrentGrade ? `4px solid ${this.getHouseBrackmannGradeColor(gradeInfo.grade)}` : 'none';

      return `
        <tr style="
          background: ${backgroundColor};
          border-left: ${borderLeft};
          font-weight: ${fontWeight};
        ">
          <td style="padding: 12px; text-align: center; color: ${this.getHouseBrackmannGradeColor(gradeInfo.grade)}; font-weight: bold; font-size: 1.1em;">
            ${gradeInfo.roman}
          </td>
          <td style="padding: 12px;">
            ${gradeInfo.description}
          </td>
          <td style="padding: 12px; text-align: center; font-family: monospace;">
            ${gradeInfo.range}
          </td>
        </tr>
      `;
    }).join('');

    return `
      <div style="overflow-x: auto;">
        <table style="
          width: 100%;
          border-collapse: collapse;
          background: white;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        ">
          <thead>
            <tr style="background: #f8f9fa;">
              <th style="padding: 15px; text-align: center; font-weight: 600; color: #2c3e50;">Grade</th>
              <th style="padding: 15px; text-align: left; font-weight: 600; color: #2c3e50;">Description</th>
              <th style="padding: 15px; text-align: center; font-weight: 600; color: #2c3e50;">Asymmetry Range</th>
            </tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
      </div>
    `;
  }

  /**
   * Get House-Brackmann grade description
   */
  private getHouseBrackmannDescription(grade: number): string {
    switch (grade) {
      case 1:
        return 'Normal facial function';
      case 2:
        return 'Mild dysfunction';
      case 3:
        return 'Moderate dysfunction';
      case 4:
        return 'Moderately severe dysfunction';
      case 5:
        return 'Severe dysfunction';
      case 6:
        return 'Total paralysis';
      default:
        return 'Grade not determined';
    }
  }

  /**
   * Convert asymmetry percentage to severity level
   * Updated thresholds based on normal baseline data analysis
   */
  private getSeverityFromPercentage(percentage: number): string {
    if (percentage < 10) return 'Normal';        // Increased from 5% - normal faces can have up to 10% asymmetry
    if (percentage < 20) return 'Mild';          // Increased from 15% - allows for natural variation
    if (percentage < 35) return 'Moderate';      // Increased from 30% - more realistic clinical thresholds
    if (percentage < 55) return 'Moderately Severe'; // Increased from 50% - better clinical correlation
    if (percentage < 80) return 'Severe';        // Increased from 75% - reserves severe for truly impaired
    return 'Total Paralysis';
  }
  private generateMetricCard(title: string, value: number): string {
    const percentage = Math.round(value * 100) / 100; // Round to 2 decimal places
    const status = percentage >= 85 ? 'Excellent' : percentage >= 70 ? 'Good' : 'Needs Attention';
    const statusColor = percentage >= 85 ? '#27ae60' : percentage >= 70 ? '#f39c12' : '#e74c3c';

    return `
      <div style="
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-left: 4px solid ${statusColor};
      ">
        <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 1.1em;">${title}</h4>
        <p style="margin: 0; font-size: 2em; font-weight: bold; color: ${statusColor};">
          ${percentage}%
        </p>
        <p style="margin: 5px 0 0 0; color: #7f8c8d; font-size: 0.9em;">
          ${status}
        </p>
      </div>
    `;
  }

  private generateAsymmetriesTable(asymmetries: any[]): string {
    if (!asymmetries || asymmetries.length === 0) {
      return `
        <div style="
          text-align: center;
          padding: 40px;
          color: #7f8c8d;
          font-style: italic;
        ">
          No significant asymmetries detected
        </div>
      `;
    }

    const tableRows = asymmetries.map(asymmetry => {
      const severityColor = this.getSeverityColor(asymmetry.severity);
      return `
        <tr style="border-bottom: 1px solid #e1e8ed;">
          <td style="padding: 12px; font-weight: 500;">${asymmetry.feature}</td>
          <td style="padding: 12px;">
            <span style="
              background: ${severityColor};
              color: white;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 0.85em;
              font-weight: 500;
            ">
              ${asymmetry.severity}
            </span>
          </td>
          <td style="padding: 12px; font-family: monospace;">${asymmetry.measurement}</td>
          <td style="padding: 12px; color: #7f8c8d; font-size: 0.9em;">${asymmetry.recommendation}</td>
        </tr>
      `;
    }).join('');

    return `
      <div style="overflow-x: auto;">
        <table style="
          width: 100%;
          border-collapse: collapse;
          background: white;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        ">
          <thead>
            <tr style="background: #f8f9fa;">
              <th style="padding: 15px; text-align: left; font-weight: 600; color: #2c3e50;">Feature</th>
              <th style="padding: 15px; text-align: left; font-weight: 600; color: #2c3e50;">Severity</th>
              <th style="padding: 15px; text-align: left; font-weight: 600; color: #2c3e50;">Measurement</th>
              <th style="padding: 15px; text-align: left; font-weight: 600; color: #2c3e50;">Recommendation</th>
            </tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
      </div>
    `;
  }
  private setupResultsEventListeners(): void {
    console.log('ResultsView: Setting up event listeners...');

    // Export PDF button (replacing CSV and Markdown)
    const pdfButton = document.getElementById('exportPdfBtn');
    const printButton = document.getElementById('printResultsBtn');
    const backButton = document.getElementById('backToHome');

    console.log('ResultsView: Button elements found:', {
      pdfButton: !!pdfButton,
      printButton: !!printButton,
      backButton: !!backButton
    });

    if (pdfButton) {
      console.log('ResultsView: Adding PDF button listener');
      pdfButton.addEventListener('click', () => {
        console.log('Export PDF button clicked from ResultsView');
        this.exportToPDF();
      });
    } else {
      console.warn('ResultsView: PDF button not found with ID: exportPdfBtn');
    }

    if (printButton) {
      console.log('ResultsView: Adding Print button listener');
      printButton.addEventListener('click', () => {
        console.log('Print button clicked from ResultsView');
        this.printResults();
      });
    } else {
      console.warn('ResultsView: Print button not found with ID: printResultsBtn');
    }

    if (backButton) {
      console.log('ResultsView: Adding Back button listener');
      backButton.addEventListener('click', () => {
        console.log('Back to home button clicked from ResultsView');
        // Reload the page to go back to home
        window.location.reload();
      });
    } else {
      console.warn('ResultsView: Back button not found with ID: backToHome');
    }
  }
  exportToPDF(): void {
    console.log('Exporting to PDF...');
    const results = this.examResults;
    this.generatePDFReport(results).catch(error => {
      console.error('PDF generation failed:', error);
      alert('Failed to generate PDF report. Please try again.');
    });
  }

  /**
   * Load jsPDF libraries from CDN for browser compatibility
   */
  private async loadJsPDFLibraries(): Promise<void> {
    console.log('Checking jsPDF availability...');

    // Check if jsPDF is already loaded
    if (this.isJsPDFAvailable()) {
      console.log('jsPDF already loaded and available');
      return;
    }

    console.log('Loading jsPDF libraries from CDN...');

    try {
      // Load jsPDF core library
      console.log('Loading jsPDF core library...');
      await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js');

      // Wait a moment for the library to initialize
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify jsPDF loaded correctly
      if (!this.isJsPDFAvailable()) {
        throw new Error('jsPDF core library failed to load properly');
      }
      console.log('jsPDF core library loaded successfully');

      // Load jsPDF-AutoTable plugin
      console.log('Loading jsPDF-AutoTable plugin...');
      await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.8.0/jspdf.plugin.autotable.min.js');

      // Wait for plugin to initialize
      await new Promise(resolve => setTimeout(resolve, 100));

      console.log('jsPDF libraries loaded and verified successfully');

      // Final verification
      const win = window as any;
      console.log('Available jsPDF objects:', {
        'window.jsPDF': typeof win.jsPDF,
        'window.jspdf': typeof win.jspdf,
        'autoTable available': typeof win.autoTable !== 'undefined'
      });

    } catch (error: any) {
      console.error('Failed to load jsPDF libraries:', error);
      throw new Error(`Failed to load PDF libraries: ${error?.message || 'Unknown error'}`);
    }
  }

  /**
   * Load a script dynamically
   */
  private loadScript(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if script is already loaded
      const existingScript = document.querySelector(`script[src="${src}"]`);
      if (existingScript) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = src;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
      document.head.appendChild(script);
    });
  }

  /**
   * Check if jsPDF is properly loaded and available
   */
  private isJsPDFAvailable(): boolean {
    const win = window as any;

    // Check multiple possible locations for jsPDF
    const jsPDFConstructor = win.jsPDF || win.window?.jsPDF || (win.jspdf && win.jspdf.jsPDF);

    if (!jsPDFConstructor) {
      console.error('jsPDF constructor not found in global scope');
      return false;
    }

    // Verify it's a constructor function
    if (typeof jsPDFConstructor !== 'function') {
      console.error('jsPDF is not a constructor function');
      return false;
    }

    console.log('jsPDF constructor verified and available');
    return true;
  }

  /**
   * Get the jsPDF constructor safely
   */
  private getJsPDFConstructor(): any {
    const win = window as any;

    // Try different possible locations for jsPDF
    let jsPDFConstructor = win.jsPDF;

    if (!jsPDFConstructor && win.jspdf) {
      jsPDFConstructor = win.jspdf.jsPDF;
    }

    if (!jsPDFConstructor && win.window?.jsPDF) {
      jsPDFConstructor = win.window.jsPDF;
    }

    if (!jsPDFConstructor) {
      throw new Error('jsPDF constructor not found. Library may not be loaded properly.');
    }

    return jsPDFConstructor;
  }

  /**
   * Generate comprehensive PDF clinical report
   */
  private async generatePDFReport(results: any): Promise<void> {
    // Load jsPDF from CDN for browser compatibility
    await this.loadJsPDFLibraries();

    // Verify jsPDF is properly loaded
    if (!this.isJsPDFAvailable()) {
      throw new Error('jsPDF library failed to load. Please check your internet connection and try again.');
    }

    const patient = results.patientInfo;
    const metrics = results.symmetryMetrics;
    const versionMetadata = VersionManager.getExportMetadata();

    // Calculate overall score
    const eyebrowSymmetry = metrics.eyebrowSymmetry || 0;
    const eyeSymmetry = metrics.eyeSymmetry || 0;
    const mouthSymmetry = metrics.mouthSymmetry || 0;
    // FIXED: Consistent weighting with rest of application
    const overallScore = (eyeSymmetry * 0.40) + (eyebrowSymmetry * 0.30) + (mouthSymmetry * 0.30);
    const hasSynkinesis = this.checkForSynkinesis(metrics);
    const finalScore = Math.max(0, overallScore - (hasSynkinesis ? 10 : 0));

    // Create PDF document using properly accessed jsPDF constructor
    const jsPDFConstructor = this.getJsPDFConstructor();
    const doc = new jsPDFConstructor('portrait', 'mm', 'a4');

    console.log('PDF document created successfully');
    let yPosition = 20;
    const margin = 20;

    // Header
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('Facial Symmetry Analysis', margin, yPosition);
    doc.setFontSize(14);
    doc.setFont('helvetica', 'normal');
    doc.text('Clinical Research Report', margin, yPosition + 8);

    yPosition += 15;

    // Add technical metadata to PDF properties for compliance (not visible in report)
    this.addTechnicalMetadataToPDF(doc, versionMetadata);

    // Patient Information Section
    this.addPatientInformationSection(doc, patient, results, yPosition, margin);
    yPosition = (doc as any).lastAutoTable.finalY + 15;

    // Clinical Measurements Section
    this.addClinicalMeasurementsSection(doc, finalScore, hasSynkinesis, eyebrowSymmetry, eyeSymmetry, mouthSymmetry, yPosition, margin);
    yPosition = (doc as any).lastAutoTable.finalY + 15;

    // Detailed Measurements Section
    this.addDetailedMeasurementsSection(doc, metrics, yPosition, margin);
    yPosition = (doc as any).lastAutoTable.finalY + 15;

    // Add simplified version footer (matching web interface)
    this.addSimplifiedVersionFooter(doc, versionMetadata, margin);

    // Save the PDF
    const filename = `facial-symmetry-report-${patient.id || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(filename);
    console.log(`PDF report saved as: ${filename}`);
  }

  /**
   * Add patient information section to PDF
   */
  private addPatientInformationSection(doc: any, patient: any, results: any, yPosition: number, margin: number): void {
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Patient Information', margin, yPosition);
    yPosition += 10;

    const patientData = [
      ['Patient ID', patient.id || 'N/A'],
      ['Name', patient.name || 'N/A'],
      ['Age', `${patient.age || 'N/A'} years`],
      ['Exam Date', new Date(results.timestamp).toLocaleDateString()],
      ['Exam Time', new Date(results.timestamp).toLocaleTimeString()]
    ];

    (doc as any).autoTable({
      startY: yPosition,
      head: [['Field', 'Value']],
      body: patientData,
      theme: 'grid',
      headStyles: { fillColor: [52, 58, 64] },
      margin: { left: margin, right: margin }
    });
  }

  /**
   * Add clinical measurements section to PDF
   */
  private addClinicalMeasurementsSection(doc: any, finalScore: number, hasSynkinesis: boolean,
                                       eyebrowSymmetry: number, eyeSymmetry: number, mouthSymmetry: number,
                                       yPosition: number, margin: number): void {
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Clinical Measurements', margin, yPosition);
    yPosition += 10;

    // Overall score with color coding
    const scoreColor = finalScore >= 85 ? [40, 167, 69] : finalScore >= 70 ? [255, 193, 7] : [220, 53, 69];
    const scoreStatus = finalScore >= 85 ? 'Excellent' : finalScore >= 70 ? 'Good' : 'Needs Attention';

    const clinicalData = [
      ['Overall Facial Symmetry Score', `${finalScore.toFixed(1)}%`],
      ['Clinical Status', scoreStatus],
      ['Synkinesis Detected', hasSynkinesis ? 'Yes ⚠️' : 'No ✅'],
      ['', ''], // Separator
      ['Eyebrow Symmetry', `${eyebrowSymmetry.toFixed(1)}%`],
      ['Eye Symmetry', `${eyeSymmetry.toFixed(1)}%`],
      ['Mouth Symmetry', `${mouthSymmetry.toFixed(1)}%`]
    ];

    (doc as any).autoTable({
      startY: yPosition,
      head: [['Measurement', 'Value']],
      body: clinicalData,
      theme: 'grid',
      headStyles: { fillColor: [52, 58, 64] },
      margin: { left: margin, right: margin },
      didParseCell: function(data: any) {
        if (data.row.index === 0 && data.column.index === 1) {
          data.cell.styles.fillColor = scoreColor;
          data.cell.styles.textColor = [255, 255, 255];
          data.cell.styles.fontStyle = 'bold';
        }
      }
    });
  }

  /**
   * Add detailed measurements section to PDF
   */
  private addDetailedMeasurementsSection(doc: any, metrics: any, yPosition: number, margin: number): void {
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Detailed Measurements', margin, yPosition);
    yPosition += 10;

    const detailedData = [
      ['Left Eyebrow Elevation', `${(metrics.leftEyebrowElevation || 0).toFixed(2)}°`],
      ['Right Eyebrow Elevation', `${(metrics.rightEyebrowElevation || 0).toFixed(2)}°`],
      ['Eyebrow Asymmetry', `${(metrics.eyebrowAsymmetry || 0).toFixed(1)}%`],
      ['', ''], // Separator
      ['Left Eye Closure', `${(metrics.leftEyeClosure || 0).toFixed(1)}%`],
      ['Right Eye Closure', `${(metrics.rightEyeClosure || 0).toFixed(1)}%`],
      ['Eye Asymmetry', `${(metrics.eyeAsymmetry || 0).toFixed(1)}%`],
      ['', ''], // Separator
      ['Left Mouth Movement', `${(metrics.leftMouthMovement || 0).toFixed(2)} mm`],
      ['Right Mouth Movement', `${(metrics.rightMouthMovement || 0).toFixed(2)} mm`],
      ['Mouth Asymmetry', `${(metrics.mouthAsymmetry || 0).toFixed(1)}%`]
    ];

    (doc as any).autoTable({
      startY: yPosition,
      head: [['Measurement', 'Value']],
      body: detailedData,
      theme: 'grid',
      headStyles: { fillColor: [52, 58, 64] },
      margin: { left: margin, right: margin }
    });
  }

  /**
   * Add technical metadata to PDF properties for compliance (not visible in report)
   */
  private addTechnicalMetadataToPDF(doc: any, versionMetadata: any): void {
    // Add comprehensive metadata to PDF properties for regulatory compliance
    // This data is preserved for traceability but not displayed in the report
    doc.setProperties({
      title: 'Facial Symmetry Analysis - Clinical Research Report',
      subject: 'Clinical facial symmetry assessment and analysis',
      author: `Facial Symmetry Analysis Application ${versionMetadata.applicationVersion}`,
      keywords: 'facial symmetry, clinical analysis, medical assessment, research',
      creator: `Facial Symmetry Analysis Application ${versionMetadata.applicationVersion}`,
      producer: `Clinical Research System v${versionMetadata.applicationVersion}`,

      // Custom metadata for clinical compliance
      'Application-Version': versionMetadata.applicationVersion,
      'Algorithm-Version': versionMetadata.algorithmVersion,
      'Clinical-Validation-Status': versionMetadata.clinicalValidationStatus,
      'FDA-Status': versionMetadata.regulatoryStatus.fdaStatus,
      'HIPAA-Compliant': versionMetadata.regulatoryStatus.hipaaCompliant.toString(),
      'ISO-13485-Certified': versionMetadata.regulatoryStatus.iso13485.toString(),
      'Clinical-Validation-Completed': versionMetadata.regulatoryStatus.clinicalValidation.toString(),
      'Algorithm-Checksum': versionMetadata.checksums.algorithm,
      'Data-Format-Checksum': versionMetadata.checksums.dataFormat,
      'Backward-Compatible-Versions': versionMetadata.compatibility.backwardCompatible.join(', '),
      'Migration-Required': versionMetadata.compatibility.migrationRequired.toString(),
      'Export-Timestamp': versionMetadata.exportTimestamp,
      'Release-Date': versionMetadata.releaseDate || 'Not specified'
    });

    console.log('Technical metadata added to PDF properties for compliance');
  }

  /**
   * Add simplified version footer to PDF (matching web interface)
   */
  private addSimplifiedVersionFooter(doc: any, versionMetadata: any, margin: number): void {
    const pageHeight = doc.internal.pageSize.getHeight();

    // Add clinical disclaimer
    doc.setFontSize(8);
    doc.setFont('helvetica', 'italic');
    doc.setTextColor(100, 100, 100);
    doc.text('This report is generated by a clinical research application. Verify clinical validation status before use in clinical decision-making.',
             margin, pageHeight - 20);

    // Add simplified version information (matching web interface)
    doc.setFontSize(9);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(120, 120, 120);

    const releaseDate = versionMetadata.releaseDate || new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const versionText = `Facial Symmetry Analysis Application • Version ${versionMetadata.applicationVersion} • Released: ${releaseDate}`;
    const pageWidth = doc.internal.pageSize.getWidth();
    const textWidth = doc.getTextWidth(versionText);
    const centerX = (pageWidth - textWidth) / 2;

    doc.text(versionText, centerX, pageHeight - 10);

    console.log('Simplified version footer added to PDF');
  }
  printResults(): void { window.print(); }
  // CSV/Markdown generation moved to ResultsExportUtils
  downloadFile(content: string, filename: string, mimeType: string): void {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}
