// Presentation: Patient Form Component with Assessment Mode Selection
export type AssessmentMode = 'live' | 'images';

export interface PatientData {
  id: string;
  name: string;
  age: string;
  assessmentMode: AssessmentMode;
}

export class PatientForm {
  private formElement: HTMLFormElement;
  private modeSelectionElement: HTMLElement;
  private onSubmitCallback?: (data: PatientData) => void;
  private selectedMode: AssessmentMode | null = null;

  constructor(formElementId: string) {
    console.log('[DEBUG] PatientForm constructor called with ID:', formElementId);
    this.formElement = document.getElementById(formElementId) as HTMLFormElement;
    this.modeSelectionElement = document.getElementById('modeSelection') as HTMLElement;

    if (!this.formElement) {
      console.error('[DEBUG] Form element not found with ID:', formElementId);
      throw new Error(`Form element with id '${formElementId}' not found`);
    }

    if (!this.modeSelectionElement) {
      console.error('[DEBUG] Mode selection element not found');
      throw new Error(`Mode selection element not found`);
    }

    console.log('[DEBUG] Form element found:', this.formElement);
    console.log('[DEBUG] Mode selection element found:', this.modeSelectionElement);
    this.setupEventListeners();
    this.setupModeSelection();
  }

  private setupEventListeners(): void {
    console.log('[DEBUG] Setting up event listeners for PatientForm');
    this.formElement.addEventListener('submit', (e) => {
      console.log('[DEBUG] Form submit event triggered');
      e.preventDefault();
      this.handleSubmit();
    });
    console.log('[DEBUG] Event listeners set up successfully');
  }

  private setupModeSelection(): void {
    console.log('[DEBUG] Setting up mode selection');

    // Make mode selection functions globally available with proper binding
    (window as any).selectAssessmentMode = (mode: AssessmentMode) => {
      console.log('[DEBUG] Global selectAssessmentMode called with mode:', mode);
      console.log('[DEBUG] PatientForm instance exists:', !!this);
      console.log('[DEBUG] modeSelectionElement exists:', !!this.modeSelectionElement);
      console.log('[DEBUG] formElement exists:', !!this.formElement);

      try {
        this.selectAssessmentMode(mode);
      } catch (error) {
        console.error('[DEBUG] Error in selectAssessmentMode:', error);
      }
    };

    (window as any).goBackToModeSelection = () => {
      console.log('[DEBUG] Global goBackToModeSelection called');
      try {
        this.goBackToModeSelection();
      } catch (error) {
        console.error('[DEBUG] Error in goBackToModeSelection:', error);
      }
    };

    console.log('[DEBUG] Global functions set up:', {
      selectAssessmentMode: typeof (window as any).selectAssessmentMode,
      goBackToModeSelection: typeof (window as any).goBackToModeSelection
    });

    // Verify DOM elements are accessible
    console.log('[DEBUG] DOM elements check:', {
      modeSelectionElement: !!this.modeSelectionElement,
      formElement: !!this.formElement,
      modeCards: document.querySelectorAll('.mode-card').length
    });
  }

  private selectAssessmentMode(mode: AssessmentMode): void {
    console.log('[DEBUG] Assessment mode selected:', mode);
    console.log('[DEBUG] Current modeSelectionElement:', this.modeSelectionElement);
    console.log('[DEBUG] Current formElement:', this.formElement);

    this.selectedMode = mode;

    // Update UI to show selected mode
    const modeCards = document.querySelectorAll('.mode-card');
    console.log('[DEBUG] Found mode cards:', modeCards.length);
    modeCards.forEach(card => {
      card.classList.remove('selected');
      if (card.getAttribute('data-mode') === mode) {
        card.classList.add('selected');
        console.log('[DEBUG] Selected mode card:', card);
      }
    });

    // Hide mode selection and show patient form
    console.log('[DEBUG] Hiding mode selection, showing patient form');
    console.log('[DEBUG] modeSelectionElement before hide:', this.modeSelectionElement.style.display);
    console.log('[DEBUG] formElement before show:', this.formElement.style.display);

    this.modeSelectionElement.style.display = 'none';
    this.formElement.style.display = 'flex';

    console.log('[DEBUG] modeSelectionElement after hide:', this.modeSelectionElement.style.display);
    console.log('[DEBUG] formElement after show:', this.formElement.style.display);

    // Update checklist and button text based on mode
    this.updateFormForMode(mode);
    console.log('[DEBUG] Mode selection complete');
  }

  private updateFormForMode(mode: AssessmentMode): void {
    const liveChecklist = document.getElementById('liveAssessmentChecklist');
    const imageChecklist = document.getElementById('imageAssessmentChecklist');
    const startBtnText = document.getElementById('startBtnText');

    if (mode === 'live') {
      if (liveChecklist) liveChecklist.style.display = 'block';
      if (imageChecklist) imageChecklist.style.display = 'none';
      if (startBtnText) startBtnText.textContent = 'Begin Live Assessment';
    } else {
      if (liveChecklist) liveChecklist.style.display = 'none';
      if (imageChecklist) imageChecklist.style.display = 'block';
      if (startBtnText) startBtnText.textContent = 'Begin Image Assessment';
    }
  }

  private goBackToModeSelection(): void {
    console.log('[DEBUG] Going back to mode selection');
    this.selectedMode = null;

    // Clear mode selection
    const modeCards = document.querySelectorAll('.mode-card');
    modeCards.forEach(card => card.classList.remove('selected'));

    // Show mode selection and hide patient form
    this.modeSelectionElement.style.display = 'flex';
    this.formElement.style.display = 'none';
  }

  private handleSubmit(): void {
    console.log('[DEBUG] handleSubmit called');
    const formData = this.extractFormData();
    console.log('[DEBUG] Form data extracted:', formData);

    if (this.validateFormData(formData)) {
      console.log('[DEBUG] Form data is valid, calling onSubmitCallback');
      if (this.onSubmitCallback) {
        this.onSubmitCallback(formData);
      } else {
        console.error('[DEBUG] No onSubmitCallback set!');
      }
    } else {
      console.log('[DEBUG] Form data validation failed');
    }
  }

  private extractFormData(): PatientData {
    const idInput = document.getElementById('pid') as HTMLInputElement;
    const nameInput = document.getElementById('pname') as HTMLInputElement;
    const ageInput = document.getElementById('page') as HTMLInputElement;

    // Try to determine the selected mode from UI if selectedMode is null
    let assessmentMode = this.selectedMode;
    if (!assessmentMode) {
      const selectedCard = document.querySelector('.mode-card.selected');
      if (selectedCard) {
        assessmentMode = selectedCard.getAttribute('data-mode') as AssessmentMode;
        console.log('[DEBUG] Extracted mode from UI:', assessmentMode);
      }
    }

    const formData = {
      id: idInput?.value || '',
      name: nameInput?.value || '',
      age: ageInput?.value || '',
      assessmentMode: assessmentMode || 'live' // Default to live if not selected
    };

    console.log('[DEBUG] Extracted form data:', formData);
    return formData;
  }

  private validateFormData(data: PatientData): boolean {
    console.log('[DEBUG] validateFormData called');
    console.log('[DEBUG] this.selectedMode:', this.selectedMode);
    console.log('[DEBUG] data.assessmentMode:', data.assessmentMode);
    console.log('[DEBUG] Form data:', data);

    // Check if mode is selected either in instance or in data
    if (!this.selectedMode && !data.assessmentMode) {
      console.log('[DEBUG] No mode selected - showing alert');
      alert('Please select an assessment mode first');
      this.goBackToModeSelection();
      return false;
    }

    // If selectedMode is null but data has assessmentMode, sync them
    if (!this.selectedMode && data.assessmentMode) {
      console.log('[DEBUG] Syncing selectedMode from data:', data.assessmentMode);
      this.selectedMode = data.assessmentMode;
    }

    if (!data.id.trim()) {
      alert('Patient ID is required');
      return false;
    }

    if (!data.name.trim()) {
      alert('Patient name is required');
      return false;
    }

    if (!data.age.trim()) {
      alert('Patient age is required');
      return false;
    }

    console.log('[DEBUG] Form validation passed');
    return true;
  }

  onSubmit(callback: (data: PatientData) => void): void {
    console.log('[DEBUG] onSubmit callback set:', !!callback);
    this.onSubmitCallback = callback;
  }

  show(): void {
    console.log('[DEBUG] PatientForm.show() called, selectedMode:', this.selectedMode);

    // Check if elements still exist in DOM
    if (!this.modeSelectionElement || !this.formElement) {
      console.log('[DEBUG] DOM elements missing, throwing error');
      throw new Error('DOM elements no longer exist, PatientForm needs reinitialization');
    }

    // If no mode is selected, show mode selection
    if (!this.selectedMode) {
      console.log('[DEBUG] No mode selected, showing mode selection');
      this.modeSelectionElement.style.display = 'flex';
      this.formElement.style.display = 'none';

      // Clear any previous selections
      const modeCards = document.querySelectorAll('.mode-card');
      modeCards.forEach(card => card.classList.remove('selected'));
    } else {
      console.log('[DEBUG] Mode already selected, showing form directly');
      // If mode is already selected, show the form directly
      this.modeSelectionElement.style.display = 'none';
      this.formElement.style.display = 'flex';

      // Restore the selected mode card
      const modeCards = document.querySelectorAll('.mode-card');
      modeCards.forEach(card => {
        card.classList.remove('selected');
        if (card.getAttribute('data-mode') === this.selectedMode) {
          card.classList.add('selected');
        }
      });

      // Update form for the selected mode
      this.updateFormForMode(this.selectedMode);
    }
  }

  hide(): void {
    // Check if elements still exist in DOM
    if (!this.modeSelectionElement || !this.formElement) {
      console.log('DOM elements no longer exist, skipping hide operation');
      return;
    }

    this.modeSelectionElement.style.display = 'none';
    this.formElement.style.display = 'none';
  }

  reset(): void {
    this.formElement.reset();
    this.selectedMode = null;

    // Clear mode selections
    const modeCards = document.querySelectorAll('.mode-card');
    modeCards.forEach(card => card.classList.remove('selected'));

    // Show mode selection, hide form
    this.modeSelectionElement.style.display = 'flex';
    this.formElement.style.display = 'none';
  }
}
