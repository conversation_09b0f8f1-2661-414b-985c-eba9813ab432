// Infrastructure: Camera Repository Implementation
import { ICameraRepository, FaceMeshResults } from '../../domain/repositories/ICameraRepository.js';

declare global {
  interface Window {
    FaceMesh: any;
    Camera: any;
  }
}

export class CameraRepository implements ICameraRepository {
  private faceMesh: any = null;
  private camera: any = null;
  private resultsCallback: ((results: FaceMeshResults) => void) | null = null;
  private currentFacingMode: 'user' | 'environment' = 'user';
  private availableCameras: MediaDeviceInfo[] = [];
  private currentVideoElement: HTMLVideoElement | null = null;

  async startCamera(videoElement: HTMLVideoElement): Promise<void> {
    try {
      console.log('=== CameraRepository: Starting camera ===');
      console.log('Video element:', videoElement);
      console.log('Video element parent:', videoElement.parentElement);
      console.log('Video element computed style:', window.getComputedStyle(videoElement));

      this.currentVideoElement = videoElement;

      // Check camera permissions first
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia is not supported in this browser');
      }

      // Enumerate available cameras
      await this.enumerateCameras();

      console.log('Requesting camera permissions...');
      // Get user media with specific constraints
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 480 },
          height: { ideal: 360 },
          facingMode: this.currentFacingMode
        }
      });

      console.log('Camera permission granted, stream obtained:', stream);
      console.log('Stream tracks:', stream.getTracks());

      videoElement.srcObject = stream;
      console.log('Video stream set successfully');
      console.log('Stream:', stream);
      console.log('Video element after setting stream:', videoElement);

      // Wait for video to load and ensure it's visible
      await new Promise((resolve) => {
        const timeout = setTimeout(() => {
          console.log('Video load timeout, continuing anyway');
          resolve(true);
        }, 3000);

        videoElement.onloadedmetadata = () => {
          clearTimeout(timeout);
          console.log('Video metadata loaded');
          console.log('Video dimensions:', videoElement.videoWidth, 'x', videoElement.videoHeight);

          videoElement.play().then(() => {
            console.log('Video playing');

            // Force video to be visible with aggressive styling
            videoElement.style.display = 'block';
            videoElement.style.visibility = 'visible';
            videoElement.style.opacity = '1';
            videoElement.style.zIndex = '1';
            videoElement.style.position = 'relative';

            // Log final video state
            console.log('Video final state:', {
              display: videoElement.style.display,
              visibility: videoElement.style.visibility,
              opacity: videoElement.style.opacity,
              srcObject: !!videoElement.srcObject,
              videoWidth: videoElement.videoWidth,
              videoHeight: videoElement.videoHeight,
              readyState: videoElement.readyState,
              paused: videoElement.paused
            });

            resolve(true);
          }).catch(err => {
            console.error('Error playing video:', err);
            resolve(true);
          });
        };

        // Fallback: if metadata doesn't load, try to play anyway
        setTimeout(() => {
          if (videoElement.readyState === 0) {
            console.log('Forcing video play attempt');
            videoElement.play().catch(err => {
              console.error('Forced play failed:', err);
            });
          }
        }, 1000);
      });

      // Initialize MediaPipe FaceMesh (assuming it's loaded globally)
      if (window.FaceMesh) {
        console.log('MediaPipe FaceMesh found, initializing...');

        // Wait a moment for MediaPipe to fully load
        await new Promise(resolve => setTimeout(resolve, 500));

        // Initialize FaceMesh with proper CDN file paths
        try {
          this.faceMesh = new window.FaceMesh({
            locateFile: (file: string) => {
              // Use the specific version CDN path to ensure files are found
              const url = `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh@0.4.1633559619/${file}`;
              console.log('MediaPipe loading file:', url);
              return url;
            }
          });
        } catch (error) {
          console.error('Error creating FaceMesh with versioned CDN, trying latest:', error);
          // Fallback to latest version
          this.faceMesh = new window.FaceMesh({
            locateFile: (file: string) => {
              const url = `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${file}`;
              console.log('MediaPipe loading file (fallback):', url);
              return url;
            }
          });
        }

        // Set up a simple initialization check
        console.log('FaceMesh created, setting up configuration...');

        this.faceMesh.setOptions({
          maxNumFaces: 1,
          refineLandmarks: true,
          minDetectionConfidence: 0.5,
          minTrackingConfidence: 0.5
        });

        // Set up results callback first (if one was already registered)
        if (this.resultsCallback) {
          this.faceMesh.onResults(this.resultsCallback);
          console.log('Results callback re-registered');
        }

        this.camera = new window.Camera(videoElement, {
          onFrame: async () => {
            try {
              if (this.faceMesh && videoElement.readyState >= 2) {
                await this.faceMesh.send({ image: videoElement });
              }
            } catch (error) {
              console.error('Error sending frame to FaceMesh:', error);
            }
          },
          width: 480,
          height: 360
        });

        // Start the camera
        this.camera.start();
        console.log('Camera started successfully');
      } else {
        console.error('MediaPipe FaceMesh not found. Make sure the script is loaded.');
        throw new Error('MediaPipe FaceMesh not available');
      }
    } catch (error) {
      console.error('Camera start error:', error);
      throw new Error(`Failed to start camera: ${error}`);
    }
  }

  stopCamera(): void {
    console.log('Stopping camera...');

    // Stop MediaPipe camera
    if (this.camera) {
      this.camera.stop();
      this.camera = null;
      console.log('MediaPipe camera stopped');
    }

    // Close FaceMesh
    if (this.faceMesh) {
      this.faceMesh.close();
      this.faceMesh = null;
      console.log('FaceMesh closed');
    }

    // Stop all video streams
    const videoElements = document.querySelectorAll('video');
    videoElements.forEach(video => {
      if (video.srcObject) {
        const stream = video.srcObject as MediaStream;
        stream.getTracks().forEach(track => {
          track.stop();
          console.log('Video track stopped:', track.kind);
        });
        video.srcObject = null;
        console.log('Video stream cleared');
      }
    });

    console.log('Camera stopped completely');
  }

  onResults(callback: (results: FaceMeshResults) => void): void {
    this.resultsCallback = callback;
    if (this.faceMesh) {
      this.faceMesh.onResults(callback);
      console.log('Results callback registered with FaceMesh');
    } else {
      console.log('Results callback stored, will be registered when FaceMesh is ready');
    }
  }

  // Camera switching functionality
  async enumerateCameras(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      this.availableCameras = devices.filter(device => device.kind === 'videoinput');
      console.log('Available cameras:', this.availableCameras.length, this.availableCameras);
      return this.availableCameras;
    } catch (error) {
      console.error('Error enumerating cameras:', error);
      return [];
    }
  }

  getAvailableCameras(): MediaDeviceInfo[] {
    return this.availableCameras;
  }

  hasMultipleCameras(): boolean {
    return this.availableCameras.length > 1;
  }

  getCurrentFacingMode(): 'user' | 'environment' {
    return this.currentFacingMode;
  }

  async switchCamera(): Promise<void> {
    if (!this.hasMultipleCameras() || !this.currentVideoElement) {
      console.log('Cannot switch camera: not enough cameras or no video element');
      return;
    }

    try {
      console.log('Switching camera from', this.currentFacingMode);

      // Toggle facing mode
      this.currentFacingMode = this.currentFacingMode === 'user' ? 'environment' : 'user';

      // Stop current camera
      this.stopCamera();

      // Wait a moment for cleanup
      await new Promise(resolve => setTimeout(resolve, 500));

      // Restart camera with new facing mode
      await this.startCamera(this.currentVideoElement);

      console.log('Camera switched to', this.currentFacingMode);
    } catch (error) {
      console.error('Error switching camera:', error);
      // Revert facing mode on error
      this.currentFacingMode = this.currentFacingMode === 'user' ? 'environment' : 'user';
      throw new Error('Failed to switch camera');
    }
  }
}
