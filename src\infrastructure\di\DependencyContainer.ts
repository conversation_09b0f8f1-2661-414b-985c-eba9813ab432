// Infrastructure: Dependency Injection Container
import { MetricCalculationService } from '../../domain/services/MetricCalculationService.js';
import { IExamRepository } from '../../domain/repositories/IExamRepository.js';
import { ICameraRepository } from '../../domain/repositories/ICameraRepository.js';
import { ExamRepository } from '../repositories/ExamRepository.js';
import { CameraRepository } from '../repositories/CameraRepository.js';
import { StartExamUseCase } from '../../application/use-cases/StartExamUseCase.js';
import { ProcessFacialDataUseCase } from '../../application/use-cases/ProcessFacialDataUseCase.js';
import { ExportResultsUseCase } from '../../application/use-cases/ExportResultsUseCase.js';
import { ExamOrchestrator } from '../../application/services/ExamOrchestrator.js';
import { ExamActionsConfig } from '../config/ExamActionsConfig.js';

export class DependencyContainer {
  private static instance: DependencyContainer;

  // Repositories
  private _examRepository!: IExamRepository;
  private _cameraRepository!: ICameraRepository;

  // Services
  private _metricCalculationService!: MetricCalculationService;

  // Use Cases
  private _startExamUseCase!: StartExamUseCase;
  private _processFacialDataUseCase!: ProcessFacialDataUseCase;
  private _exportResultsUseCase!: ExportResultsUseCase;

  // Application Services
  private _examOrchestrator!: ExamOrchestrator;

  private constructor() {
    this.initializeDependencies();
  }

  static getInstance(): DependencyContainer {
    if (!DependencyContainer.instance) {
      DependencyContainer.instance = new DependencyContainer();
    }
    return DependencyContainer.instance;
  }

  private initializeDependencies(): void {
    // Initialize repositories
    this._examRepository = new ExamRepository();
    this._cameraRepository = new CameraRepository();

    // Initialize services
    this._metricCalculationService = new MetricCalculationService();

    // Initialize use cases
    const examActions = ExamActionsConfig.getDefaultActions();
    this._startExamUseCase = new StartExamUseCase(
      this._examRepository,
      this._cameraRepository,
      examActions
    );

    this._processFacialDataUseCase = new ProcessFacialDataUseCase(
      this._metricCalculationService,
      this._examRepository
    );

    this._exportResultsUseCase = new ExportResultsUseCase(this._examRepository);

    // Initialize application services
    this._examOrchestrator = new ExamOrchestrator(
      this._startExamUseCase,
      this._processFacialDataUseCase,
      this._exportResultsUseCase
    );
  }

  // Getters for dependencies
  get examOrchestrator(): ExamOrchestrator {
    return this._examOrchestrator;
  }

  get cameraRepository(): ICameraRepository {
    return this._cameraRepository;
  }

  get examRepository(): IExamRepository {
    return this._examRepository;
  }
}
