<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Export and Print Functionality</title>
</head>
<body>
    <h1>Export and Print Functionality Test</h1>
    <div id="results-container"></div>

    <script type="module">
        // Mock examination results data
        const mockResults = {
            timestamp: Date.now(),
            patientInfo: {
                id: 'TEST001',
                name: 'Test Patient',
                age: 35
            },
            overallScore: 87.5,
            symmetryMetrics: {
                eyebrowSymmetry: 91.2,
                eyeSymmetry: 94.0,
                mouthSymmetry: 100.0,
                leftEyebrowElevation: 16.35,
                rightEyebrowElevation: 14.59,
                eyebrowAsymmetry: 8.8,
                leftEyeClosure: 65.9,
                rightEyeClosure: 78.0,
                eyeAsymmetry: 9.0,
                leftMouthMovement: 19.63,
                rightMouthMovement: 19.46,
                mouthAsymmetry: 0.0,
                synkinesisDetected: false,
                synkinesisResults: []
            }
        };

        // Import and test ResultsView
        import('./dist/presentation/components/ResultsView.js').then(module => {
            const { ResultsView } = module;
            
            console.log('Creating ResultsView with mock data...');
            const resultsView = new ResultsView(mockResults);
            
            // Generate and insert HTML
            const html = resultsView.generateResultsHTML();
            document.getElementById('results-container').innerHTML = html;
            
            // Set up event listeners
            resultsView.setupEventListeners();
            
            console.log('ResultsView loaded successfully!');
            console.log('Test the export and print buttons to verify functionality.');
            
        }).catch(error => {
            console.error('Failed to load ResultsView:', error);
            document.getElementById('results-container').innerHTML = `
                <div style="color: red; padding: 20px;">
                    <h2>Error Loading ResultsView</h2>
                    <p>Failed to load the ResultsView component. Check the console for details.</p>
                    <pre>${error.message}</pre>
                </div>
            `;
        });
    </script>
</body>
</html>
