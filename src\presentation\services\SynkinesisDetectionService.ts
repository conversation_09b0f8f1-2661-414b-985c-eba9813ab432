/**
 * Advanced Synkinesis Detection Service using MediaPipe FaceMesh
 * 
 * Implements clinical-grade synkinesis detection with temporal event logic,
 * blink filtering, and false positive mitigation as per medical literature.
 * 
 * Key Features:
 * - Oral-ocular synkinesis detection (mouth movement triggers eye closure)
 * - Oculo-oral synkinesis detection (eye closure triggers mouth movement)
 * - Blink filtering (duration, symmetry, trigger dependency)
 * - Temporal event logic with configurable windows
 * - Clinical threshold calibration
 */

interface SynkinesisEvent {
  type: 'oral-ocular' | 'oculo-oral';
  side: 'left' | 'right' | 'bilateral';
  timestamp: number;
  severity: 'mild' | 'moderate' | 'severe';
  metrics: {
    eyeApertureChange: number;
    mouthMovementChange: number;
    duration: number;
    confidence: number;
  };
}

interface EyeMetrics {
  leftEAR: number;
  rightEAR: number;
  leftAperture: number;
  rightAperture: number;
}

interface MouthMetrics {
  width: number;
  lipHeight: number;
  leftCornerMovement: number;
  rightCornerMovement: number;
}

interface TemporalEvent {
  type: 'smile' | 'eye_closure_left' | 'eye_closure_right';
  timestamp: number;
  startValue: number;
  peakValue: number;
  duration: number;
}

export class SynkinesisDetectionService {
  // Clinical thresholds based on literature
  private readonly NORMAL_BLINK_DURATION = 300; // ms (280-300ms per literature)
  private readonly MIN_SYNKINESIS_DURATION = 200; // ms
  private readonly TEMPORAL_WINDOW = 300; // ms for event correlation
  private readonly EAR_OPEN_BASELINE = 0.35; // Normal open eye EAR
  private readonly EAR_CLOSE_THRESHOLD = 0.15; // Eye considered closed
  private readonly EAR_BLINK_THRESHOLD = 0.20; // Blink detection threshold
  
  // Movement thresholds (normalized to face size)
  private readonly SMILE_TRIGGER_THRESHOLD = 0.15; // 15% mouth width increase
  private readonly EYE_DROP_THRESHOLD = 0.40; // 40% aperture reduction
  private readonly MOUTH_MOVEMENT_THRESHOLD = 0.10; // 10% mouth movement during eye closure
  
  // State tracking
  private baselineMetrics: { eye: EyeMetrics; mouth: MouthMetrics } | null = null;
  private previousMetrics: { eye: EyeMetrics; mouth: MouthMetrics } | null = null;
  private activeEvents: TemporalEvent[] = [];
  private detectedSynkinesis: SynkinesisEvent[] = [];
  private frameHistory: Array<{ timestamp: number; eye: EyeMetrics; mouth: MouthMetrics }> = [];
  
  // Face normalization factor (interpupillary distance)
  private faceNormalizationFactor: number = 1;

  constructor() {
    console.log('🔬 Advanced Synkinesis Detection Service initialized');
  }

  /**
   * Set baseline measurements from neutral face
   */
  setBaseline(landmarks: any[]): void {
    if (!landmarks || landmarks.length < 468) {
      console.warn('Insufficient landmarks for synkinesis baseline');
      return;
    }

    const eyeMetrics = this.calculateEyeMetrics(landmarks);
    const mouthMetrics = this.calculateMouthMetrics(landmarks);
    this.faceNormalizationFactor = this.calculateNormalizationFactor(landmarks);

    this.baselineMetrics = { eye: eyeMetrics, mouth: mouthMetrics };
    
    console.log('🔬 Synkinesis baseline established:', {
      leftEAR: eyeMetrics.leftEAR.toFixed(3),
      rightEAR: eyeMetrics.rightEAR.toFixed(3),
      mouthWidth: mouthMetrics.width.toFixed(3),
      normalizationFactor: this.faceNormalizationFactor.toFixed(3)
    });
  }

  /**
   * Process current frame for synkinesis detection
   */
  processFrame(landmarks: any[], timestamp: number): SynkinesisEvent[] {
    if (!landmarks || landmarks.length < 468 || !this.baselineMetrics) {
      return [];
    }

    const currentEyeMetrics = this.calculateEyeMetrics(landmarks);
    const currentMouthMetrics = this.calculateMouthMetrics(landmarks);

    // Store frame in history (keep last 30 frames for analysis)
    this.frameHistory.push({ timestamp, eye: currentEyeMetrics, mouth: currentMouthMetrics });
    if (this.frameHistory.length > 30) {
      this.frameHistory.shift();
    }

    // Detect events and check for synkinesis
    const newEvents: SynkinesisEvent[] = [];

    if (this.previousMetrics) {
      // Detect smile events (oral-ocular synkinesis)
      const smileEvents = this.detectSmileEvents(currentMouthMetrics, timestamp);
      const oralOcularSynkinesis = this.checkOralOcularSynkinesis(currentEyeMetrics, timestamp);
      newEvents.push(...oralOcularSynkinesis);

      // Detect eye closure events (oculo-oral synkinesis)
      const eyeClosureEvents = this.detectEyeClosureEvents(currentEyeMetrics, timestamp);
      const oculoOralSynkinesis = this.checkOculoOralSynkinesis(currentMouthMetrics, timestamp);
      newEvents.push(...oculoOralSynkinesis);
    }

    // Update previous metrics
    this.previousMetrics = { eye: currentEyeMetrics, mouth: currentMouthMetrics };

    // Clean up old events (remove events older than temporal window)
    this.cleanupOldEvents(timestamp);

    // Store detected synkinesis
    this.detectedSynkinesis.push(...newEvents);

    return newEvents;
  }

  /**
   * Calculate Eye Aspect Ratio (EAR) and aperture metrics
   */
  private calculateEyeMetrics(landmarks: any[]): EyeMetrics {
    // Left eye landmarks (MediaPipe indices)
    const leftEyeVertical = [
      landmarks[386], landmarks[374], // Primary vertical points
      landmarks[385], landmarks[380]  // Secondary vertical points
    ];
    const leftEyeHorizontal = [landmarks[33], landmarks[133]];

    // Right eye landmarks
    const rightEyeVertical = [
      landmarks[159], landmarks[145], // Primary vertical points
      landmarks[158], landmarks[153]  // Secondary vertical points
    ];
    const rightEyeHorizontal = [landmarks[362], landmarks[263]];

    const leftEAR = this.calculateEAR(leftEyeVertical, leftEyeHorizontal);
    const rightEAR = this.calculateEAR(rightEyeVertical, rightEyeHorizontal);

    // Calculate aperture as normalized vertical distance
    const leftAperture = this.calculateAperture(leftEyeVertical) / this.faceNormalizationFactor;
    const rightAperture = this.calculateAperture(rightEyeVertical) / this.faceNormalizationFactor;

    return { leftEAR, rightEAR, leftAperture, rightAperture };
  }

  /**
   * Calculate mouth movement metrics
   */
  private calculateMouthMetrics(landmarks: any[]): MouthMetrics {
    const leftCorner = landmarks[61];
    const rightCorner = landmarks[291];
    const upperLip = landmarks[13];
    const lowerLip = landmarks[14];

    if (!leftCorner || !rightCorner || !upperLip || !lowerLip) {
      return { width: 0, lipHeight: 0, leftCornerMovement: 0, rightCornerMovement: 0 };
    }

    // Mouth width (corner to corner distance)
    const width = Math.sqrt(
      Math.pow(rightCorner.x - leftCorner.x, 2) +
      Math.pow(rightCorner.y - leftCorner.y, 2)
    ) / this.faceNormalizationFactor;

    // Lip height (vertical separation)
    const lipHeight = Math.abs(upperLip.y - lowerLip.y) / this.faceNormalizationFactor;

    // Individual corner movements (relative to baseline)
    let leftCornerMovement = 0;
    let rightCornerMovement = 0;

    if (this.baselineMetrics) {
      const baselineLeftCorner = this.baselineMetrics.mouth.leftCornerMovement;
      const baselineRightCorner = this.baselineMetrics.mouth.rightCornerMovement;
      
      leftCornerMovement = Math.sqrt(
        Math.pow(leftCorner.x - baselineLeftCorner, 2) +
        Math.pow(leftCorner.y - baselineLeftCorner, 2)
      ) / this.faceNormalizationFactor;

      rightCornerMovement = Math.sqrt(
        Math.pow(rightCorner.x - baselineRightCorner, 2) +
        Math.pow(rightCorner.y - baselineRightCorner, 2)
      ) / this.faceNormalizationFactor;
    }

    return { width, lipHeight, leftCornerMovement, rightCornerMovement };
  }

  /**
   * Calculate Eye Aspect Ratio using 6-point model
   */
  private calculateEAR(verticalPoints: any[], horizontalPoints: any[]): number {
    if (verticalPoints.length < 4 || horizontalPoints.length < 2) {
      return 0;
    }

    // Vertical distances
    const v1 = Math.abs(verticalPoints[0].y - verticalPoints[1].y);
    const v2 = Math.abs(verticalPoints[2].y - verticalPoints[3].y);

    // Horizontal distance
    const h = Math.abs(horizontalPoints[1].x - horizontalPoints[0].x);

    if (h === 0) return 0;

    // EAR formula: (|p2-p6| + |p3-p5|) / (2 * |p1-p4|)
    return (v1 + v2) / (2 * h);
  }

  /**
   * Calculate eye aperture (simple vertical distance)
   */
  private calculateAperture(verticalPoints: any[]): number {
    if (verticalPoints.length < 2) return 0;
    return Math.abs(verticalPoints[0].y - verticalPoints[1].y);
  }

  /**
   * Calculate face normalization factor (interpupillary distance)
   */
  private calculateNormalizationFactor(landmarks: any[]): number {
    const leftPupil = landmarks[33];
    const rightPupil = landmarks[263];

    if (!leftPupil || !rightPupil) return 1;

    return Math.sqrt(
      Math.pow(rightPupil.x - leftPupil.x, 2) +
      Math.pow(rightPupil.y - leftPupil.y, 2)
    );
  }

  /**
   * Detect smile events for oral-ocular synkinesis
   */
  private detectSmileEvents(currentMouth: MouthMetrics, timestamp: number): TemporalEvent[] {
    if (!this.baselineMetrics || !this.previousMetrics) return [];

    const baselineWidth = this.baselineMetrics.mouth.width;
    const previousWidth = this.previousMetrics.mouth.width;
    const currentWidth = currentMouth.width;

    // Detect smile onset (mouth width increase beyond threshold)
    const widthIncrease = (currentWidth - baselineWidth) / baselineWidth;
    const widthVelocity = currentWidth - previousWidth;

    if (widthIncrease > this.SMILE_TRIGGER_THRESHOLD && widthVelocity > 0) {
      console.log(`🔬 Smile event detected: ${(widthIncrease * 100).toFixed(1)}% width increase`);

      return [{
        type: 'smile',
        timestamp,
        startValue: baselineWidth,
        peakValue: currentWidth,
        duration: 0 // Will be updated as event continues
      }];
    }

    return [];
  }

  /**
   * Detect eye closure events for oculo-oral synkinesis
   */
  private detectEyeClosureEvents(currentEye: EyeMetrics, timestamp: number): TemporalEvent[] {
    if (!this.baselineMetrics || !this.previousMetrics) return [];

    const events: TemporalEvent[] = [];

    // Check left eye closure
    const leftEARDrop = this.baselineMetrics.eye.leftEAR - currentEye.leftEAR;
    const leftEARVelocity = this.previousMetrics.eye.leftEAR - currentEye.leftEAR;

    if (currentEye.leftEAR < this.EAR_CLOSE_THRESHOLD &&
        leftEARDrop > this.EYE_DROP_THRESHOLD * this.baselineMetrics.eye.leftEAR &&
        leftEARVelocity > 0) {

      console.log(`🔬 Left eye closure event: EAR ${currentEye.leftEAR.toFixed(3)} (drop: ${leftEARDrop.toFixed(3)})`);

      events.push({
        type: 'eye_closure_left',
        timestamp,
        startValue: this.baselineMetrics.eye.leftEAR,
        peakValue: currentEye.leftEAR,
        duration: 0
      });
    }

    // Check right eye closure
    const rightEARDrop = this.baselineMetrics.eye.rightEAR - currentEye.rightEAR;
    const rightEARVelocity = this.previousMetrics.eye.rightEAR - currentEye.rightEAR;

    if (currentEye.rightEAR < this.EAR_CLOSE_THRESHOLD &&
        rightEARDrop > this.EYE_DROP_THRESHOLD * this.baselineMetrics.eye.rightEAR &&
        rightEARVelocity > 0) {

      console.log(`🔬 Right eye closure event: EAR ${currentEye.rightEAR.toFixed(3)} (drop: ${rightEARDrop.toFixed(3)})`);

      events.push({
        type: 'eye_closure_right',
        timestamp,
        startValue: this.baselineMetrics.eye.rightEAR,
        peakValue: currentEye.rightEAR,
        duration: 0
      });
    }

    return events;
  }

  /**
   * Check for oral-ocular synkinesis (smile triggers eye closure)
   */
  private checkOralOcularSynkinesis(currentEye: EyeMetrics, timestamp: number): SynkinesisEvent[] {
    const synkinesisEvents: SynkinesisEvent[] = [];

    // Look for recent smile events within temporal window
    const recentSmileEvents = this.activeEvents.filter(event =>
      event.type === 'smile' &&
      (timestamp - event.timestamp) <= this.TEMPORAL_WINDOW
    );

    if (recentSmileEvents.length === 0) return [];

    // Check for eye closure during smile
    if (this.baselineMetrics && this.previousMetrics) {
      // Left eye synkinesis
      const leftEARDrop = this.baselineMetrics.eye.leftEAR - currentEye.leftEAR;
      const leftEARDropPercent = leftEARDrop / this.baselineMetrics.eye.leftEAR;

      if (leftEARDropPercent > this.EYE_DROP_THRESHOLD &&
          currentEye.leftEAR < this.EAR_CLOSE_THRESHOLD) {

        // Apply blink filtering
        if (this.isNotNormalBlink(currentEye, 'left', timestamp)) {
          const severity = this.calculateSynkinesisSeverity(leftEARDropPercent);

          console.log(`🔬 ⚠️ ORAL-OCULAR SYNKINESIS DETECTED (Left): ${(leftEARDropPercent * 100).toFixed(1)}% eye closure during smile`);

          synkinesisEvents.push({
            type: 'oral-ocular',
            side: 'left',
            timestamp,
            severity,
            metrics: {
              eyeApertureChange: leftEARDropPercent,
              mouthMovementChange: recentSmileEvents[0].peakValue - recentSmileEvents[0].startValue,
              duration: timestamp - recentSmileEvents[0].timestamp,
              confidence: this.calculateConfidence(leftEARDropPercent, 'oral-ocular')
            }
          });
        }
      }

      // Right eye synkinesis
      const rightEARDrop = this.baselineMetrics.eye.rightEAR - currentEye.rightEAR;
      const rightEARDropPercent = rightEARDrop / this.baselineMetrics.eye.rightEAR;

      if (rightEARDropPercent > this.EYE_DROP_THRESHOLD &&
          currentEye.rightEAR < this.EAR_CLOSE_THRESHOLD) {

        // Apply blink filtering
        if (this.isNotNormalBlink(currentEye, 'right', timestamp)) {
          const severity = this.calculateSynkinesisSeverity(rightEARDropPercent);

          console.log(`🔬 ⚠️ ORAL-OCULAR SYNKINESIS DETECTED (Right): ${(rightEARDropPercent * 100).toFixed(1)}% eye closure during smile`);

          synkinesisEvents.push({
            type: 'oral-ocular',
            side: 'right',
            timestamp,
            severity,
            metrics: {
              eyeApertureChange: rightEARDropPercent,
              mouthMovementChange: recentSmileEvents[0].peakValue - recentSmileEvents[0].startValue,
              duration: timestamp - recentSmileEvents[0].timestamp,
              confidence: this.calculateConfidence(rightEARDropPercent, 'oral-ocular')
            }
          });
        }
      }
    }

    return synkinesisEvents;
  }

  /**
   * Check for oculo-oral synkinesis (eye closure triggers mouth movement)
   */
  private checkOculoOralSynkinesis(currentMouth: MouthMetrics, timestamp: number): SynkinesisEvent[] {
    const synkinesisEvents: SynkinesisEvent[] = [];

    // Look for recent eye closure events within temporal window
    const recentEyeEvents = this.activeEvents.filter(event =>
      (event.type === 'eye_closure_left' || event.type === 'eye_closure_right') &&
      (timestamp - event.timestamp) <= this.TEMPORAL_WINDOW
    );

    if (recentEyeEvents.length === 0) return [];

    // Check for mouth movement during eye closure
    if (this.baselineMetrics && this.previousMetrics) {
      const mouthWidthChange = Math.abs(currentMouth.width - this.baselineMetrics.mouth.width);
      const mouthWidthChangePercent = mouthWidthChange / this.baselineMetrics.mouth.width;

      if (mouthWidthChangePercent > this.MOUTH_MOVEMENT_THRESHOLD) {
        // Determine which side based on the eye event
        const side = recentEyeEvents[0].type === 'eye_closure_left' ? 'left' : 'right';
        const severity = this.calculateSynkinesisSeverity(mouthWidthChangePercent);

        console.log(`🔬 ⚠️ OCULO-ORAL SYNKINESIS DETECTED (${side}): ${(mouthWidthChangePercent * 100).toFixed(1)}% mouth movement during eye closure`);

        synkinesisEvents.push({
          type: 'oculo-oral',
          side,
          timestamp,
          severity,
          metrics: {
            eyeApertureChange: recentEyeEvents[0].startValue - recentEyeEvents[0].peakValue,
            mouthMovementChange: mouthWidthChangePercent,
            duration: timestamp - recentEyeEvents[0].timestamp,
            confidence: this.calculateConfidence(mouthWidthChangePercent, 'oculo-oral')
          }
        });
      }
    }

    return synkinesisEvents;
  }

  /**
   * Advanced blink filtering to distinguish synkinesis from normal blinks
   */
  private isNotNormalBlink(currentEye: EyeMetrics, side: 'left' | 'right', timestamp: number): boolean {
    // Duration filter: Check if closure persists longer than normal blink
    const closureDuration = this.getClosureDuration(side, timestamp);
    if (closureDuration > 0 && closureDuration < this.MIN_SYNKINESIS_DURATION) {
      console.log(`🔬 Filtered as normal blink: duration ${closureDuration}ms < ${this.MIN_SYNKINESIS_DURATION}ms`);
      return false;
    }

    // Symmetry check: Normal blinks affect both eyes similarly
    const leftEAR = currentEye.leftEAR;
    const rightEAR = currentEye.rightEAR;
    const earDifference = Math.abs(leftEAR - rightEAR);
    const avgEAR = (leftEAR + rightEAR) / 2;

    if (avgEAR > 0 && earDifference / avgEAR < 0.3) {
      // Both eyes closing similarly - likely a blink
      console.log(`🔬 Filtered as bilateral blink: EAR difference ${(earDifference / avgEAR * 100).toFixed(1)}% < 30%`);
      return false;
    }

    // Trigger dependency: Only consider as synkinesis if triggered by another action
    const hasRecentTrigger = this.activeEvents.some(event =>
      event.type === 'smile' &&
      (timestamp - event.timestamp) <= this.TEMPORAL_WINDOW
    );

    if (!hasRecentTrigger) {
      console.log(`🔬 Filtered as isolated eye closure: no recent trigger action`);
      return false;
    }

    // Speed profile check: Synkinetic closures often have different dynamics
    const closureSpeed = this.getClosureSpeed(side, timestamp);
    if (closureSpeed > 0 && closureSpeed < 0.001) { // Very fast closure (likely blink)
      console.log(`🔬 Filtered as fast blink: closure speed ${closureSpeed.toFixed(6)} too fast`);
      return false;
    }

    console.log(`🔬 ✅ Passed blink filtering: duration=${closureDuration}ms, asymmetric=${(earDifference / avgEAR * 100).toFixed(1)}%, triggered=true`);
    return true;
  }

  /**
   * Calculate closure duration for blink filtering
   */
  private getClosureDuration(side: 'left' | 'right', currentTimestamp: number): number {
    const recentFrames = this.frameHistory.slice(-10); // Last 10 frames
    if (recentFrames.length < 2) return 0;

    const earThreshold = this.EAR_BLINK_THRESHOLD;
    let closureStart = -1;

    for (let i = 0; i < recentFrames.length; i++) {
      const frame = recentFrames[i];
      const ear = side === 'left' ? frame.eye.leftEAR : frame.eye.rightEAR;

      if (ear < earThreshold && closureStart === -1) {
        closureStart = frame.timestamp;
      } else if (ear >= earThreshold && closureStart !== -1) {
        return currentTimestamp - closureStart;
      }
    }

    // If still closed, return duration so far
    return closureStart !== -1 ? currentTimestamp - closureStart : 0;
  }

  /**
   * Calculate closure speed for blink filtering
   */
  private getClosureSpeed(side: 'left' | 'right', currentTimestamp: number): number {
    const recentFrames = this.frameHistory.slice(-5); // Last 5 frames
    if (recentFrames.length < 2) return 0;

    const earValues = recentFrames.map(frame =>
      side === 'left' ? frame.eye.leftEAR : frame.eye.rightEAR
    );

    // Calculate average rate of change
    let totalChange = 0;
    let totalTime = 0;

    for (let i = 1; i < recentFrames.length; i++) {
      const earChange = Math.abs(earValues[i] - earValues[i-1]);
      const timeChange = recentFrames[i].timestamp - recentFrames[i-1].timestamp;

      if (timeChange > 0) {
        totalChange += earChange;
        totalTime += timeChange;
      }
    }

    return totalTime > 0 ? totalChange / totalTime : 0;
  }

  /**
   * Calculate synkinesis severity based on movement magnitude
   */
  private calculateSynkinesisSeverity(changePercent: number): 'mild' | 'moderate' | 'severe' {
    if (changePercent > 0.70) return 'severe';   // >70% change
    if (changePercent > 0.50) return 'moderate'; // 50-70% change
    return 'mild';                               // 40-50% change
  }

  /**
   * Calculate confidence score for synkinesis detection
   */
  private calculateConfidence(changePercent: number, type: 'oral-ocular' | 'oculo-oral'): number {
    // Base confidence on magnitude of change
    let confidence = Math.min(changePercent * 2, 1.0); // Scale to 0-1

    // Adjust based on type (oral-ocular is generally more reliable)
    if (type === 'oral-ocular') {
      confidence *= 1.1; // Boost oral-ocular confidence
    }

    // Cap at 1.0 and ensure minimum of 0.1
    return Math.max(0.1, Math.min(1.0, confidence));
  }

  /**
   * Clean up old events outside temporal window
   */
  private cleanupOldEvents(currentTimestamp: number): void {
    this.activeEvents = this.activeEvents.filter(event =>
      (currentTimestamp - event.timestamp) <= this.TEMPORAL_WINDOW * 2
    );
  }

  /**
   * Get all detected synkinesis events
   */
  getDetectedSynkinesis(): SynkinesisEvent[] {
    return [...this.detectedSynkinesis];
  }

  /**
   * Get synkinesis summary for clinical reporting
   */
  getSynkinesisSummary(): {
    totalEvents: number;
    oralOcularEvents: number;
    oculoOralEvents: number;
    severityDistribution: { mild: number; moderate: number; severe: number };
    affectedSides: { left: number; right: number; bilateral: number };
  } {
    const events = this.detectedSynkinesis;

    return {
      totalEvents: events.length,
      oralOcularEvents: events.filter(e => e.type === 'oral-ocular').length,
      oculoOralEvents: events.filter(e => e.type === 'oculo-oral').length,
      severityDistribution: {
        mild: events.filter(e => e.severity === 'mild').length,
        moderate: events.filter(e => e.severity === 'moderate').length,
        severe: events.filter(e => e.severity === 'severe').length
      },
      affectedSides: {
        left: events.filter(e => e.side === 'left').length,
        right: events.filter(e => e.side === 'right').length,
        bilateral: events.filter(e => e.side === 'bilateral').length
      }
    };
  }

  /**
   * Reset detection state for new examination
   */
  reset(): void {
    this.baselineMetrics = null;
    this.previousMetrics = null;
    this.activeEvents = [];
    this.detectedSynkinesis = [];
    this.frameHistory = [];
    this.faceNormalizationFactor = 1;

    console.log('🔬 Synkinesis detection service reset');
  }
}
