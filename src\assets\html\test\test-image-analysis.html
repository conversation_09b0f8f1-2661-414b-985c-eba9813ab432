<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Analysis Test - Facial Symmetry</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .image-preview {
            display: flex;
            gap: 20px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .image-container {
            text-align: center;
        }
        .image-container img {
            max-width: 200px;
            max-height: 200px;
            border: 2px solid #ddd;
            border-radius: 4px;
        }
        .image-container label {
            display: block;
            margin-top: 5px;
            font-weight: bold;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-results-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .test-results-table th,
        .test-results-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .test-results-table th {
            background: #f2f2f2;
            font-weight: bold;
        }
        .consistency-test {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Image Analysis Test Suite</h1>
        <p>Test facial symmetry analysis with various image inputs and validate consistency.</p>

        <!-- Test Image Upload Section -->
        <div class="test-section">
            <h2>📁 Test Image Upload</h2>
            <div class="test-controls">
                <input type="file" id="neutralImage" accept="image/*" multiple>
                <label for="neutralImage">Upload Neutral Image</label>
                
                <input type="file" id="smileImage" accept="image/*" multiple>
                <label for="smileImage">Upload Smile Image</label>
                
                <button onclick="runSingleAnalysis()">Run Single Analysis</button>
                <button onclick="runConsistencyTest()">Run Consistency Test (5x)</button>
                <button onclick="clearResults()">Clear Results</button>
            </div>
            
            <div class="image-preview" id="imagePreview"></div>
            <div id="uploadStatus" class="status" style="display: none;"></div>
        </div>

        <!-- Analysis Results Section -->
        <div class="test-section">
            <h2>📊 Analysis Results</h2>
            <div id="analysisResults" class="results">No analysis performed yet...</div>
        </div>

        <!-- Consistency Test Results -->
        <div class="test-section">
            <h2>🔄 Consistency Test Results</h2>
            <div class="consistency-test">
                <p><strong>Purpose:</strong> Run the same analysis multiple times to check for consistent results.</p>
                <p><strong>Expected:</strong> Identical values for the same input images.</p>
            </div>
            <table class="test-results-table" id="consistencyTable" style="display: none;">
                <thead>
                    <tr>
                        <th>Run #</th>
                        <th>Left Movement (mm)</th>
                        <th>Right Movement (mm)</th>
                        <th>Asymmetry (%)</th>
                        <th>Severity</th>
                        <th>Analysis Method</th>
                    </tr>
                </thead>
                <tbody id="consistencyTableBody">
                </tbody>
            </table>
            <div id="consistencyAnalysis" class="results" style="display: none;"></div>
        </div>

        <!-- Landmark Validation Section -->
        <div class="test-section">
            <h2>🎯 Landmark Validation</h2>
            <div class="test-controls">
                <button onclick="validateLandmarks()">Validate Required Landmarks</button>
                <button onclick="showLandmarkDetails()">Show Landmark Details</button>
            </div>
            <div id="landmarkResults" class="results">Click "Validate Required Landmarks" to check landmark detection...</div>
        </div>

        <!-- Debug Information Section -->
        <div class="test-section">
            <h2>🔍 Debug Information</h2>
            <div class="test-controls">
                <button onclick="showDebugInfo()">Show Debug Info</button>
                <button onclick="exportTestResults()">Export Test Results</button>
            </div>
            <div id="debugInfo" class="results">Click "Show Debug Info" to see detailed analysis information...</div>
        </div>
    </div>

    <!-- MediaPipe FaceMesh -->
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/control_utils/control_utils.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils/drawing_utils.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/face_mesh.js" crossorigin="anonymous"></script>

    <script>
        // Global variables for test data
        let neutralImageData = null;  // Store image data instead of landmarks
        let smileImageData = null;    // Store image data instead of landmarks
        let faceMesh = null;
        let testResults = [];
        let currentExtractionResults = null;

        // Initialize MediaPipe FaceMesh
        async function initializeFaceMesh() {
            faceMesh = new FaceMesh({
                locateFile: (file) => {
                    return `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${file}`;
                }
            });

            faceMesh.setOptions({
                maxNumFaces: 1,
                refineLandmarks: true,
                minDetectionConfidence: 0.5,
                minTrackingConfidence: 0.5
            });

            faceMesh.onResults(onFaceMeshResults);
            showStatus('MediaPipe FaceMesh initialized successfully', 'success');
        }

        // Handle FaceMesh results
        function onFaceMeshResults(results) {
            if (results.multiFaceLandmarks && results.multiFaceLandmarks.length > 0) {
                const landmarks = results.multiFaceLandmarks[0];

                // Store landmarks in current extraction results
                currentExtractionResults = landmarks;
                console.log(`Extracted ${landmarks.length} landmarks from image`);
            } else {
                currentExtractionResults = null;
                console.warn('No face detected in image');
            }
        }

        // Extract landmarks from image data (fresh extraction every time)
        async function extractLandmarksFromImageData(imageDataUrl, description = '') {
            return new Promise((resolve, reject) => {
                const img = new Image();

                img.onload = async () => {
                    try {
                        console.log(`Extracting landmarks from ${description} image...`);

                        // Reset results
                        currentExtractionResults = null;

                        // Send image to FaceMesh
                        await faceMesh.send({ image: img });

                        // Wait for processing (same timing as real ImageAnalysisController)
                        await new Promise(resolve => setTimeout(resolve, 1500));

                        if (currentExtractionResults) {
                            console.log(`Successfully extracted ${currentExtractionResults.length} landmarks from ${description} image`);
                            resolve(currentExtractionResults);
                        } else {
                            reject(new Error(`Failed to extract landmarks from ${description} image`));
                        }
                    } catch (error) {
                        console.error(`Error extracting landmarks from ${description} image:`, error);
                        reject(error);
                    }
                };

                img.onerror = () => {
                    reject(new Error(`Failed to load ${description} image`));
                };

                img.src = imageDataUrl;
            });
        }

        // Handle file upload and preview
        function handleFileUpload(input, type) {
            const file = input.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const dataUrl = e.target.result;

                // Store image data for later processing
                if (type === 'neutral') {
                    neutralImageData = dataUrl;
                    showStatus('Neutral image uploaded successfully', 'success');
                } else if (type === 'smile') {
                    smileImageData = dataUrl;
                    showStatus('Smile image uploaded successfully', 'success');
                }

                // Show preview
                updateImagePreview(type, dataUrl);
            };
            reader.readAsDataURL(file);
        }

        // Update image preview
        function updateImagePreview(type, src) {
            const preview = document.getElementById('imagePreview');
            let container = document.getElementById(`${type}Preview`);
            
            if (!container) {
                container = document.createElement('div');
                container.id = `${type}Preview`;
                container.className = 'image-container';
                preview.appendChild(container);
            }
            
            container.innerHTML = `
                <img src="${src}" alt="${type} image">
                <label>${type.charAt(0).toUpperCase() + type.slice(1)} Image</label>
            `;
        }

        // Show status message
        function showStatus(message, type = 'info') {
            const status = document.getElementById('uploadStatus');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeFaceMesh();
            
            // Set up file input handlers
            document.getElementById('neutralImage').addEventListener('change', function() {
                handleFileUpload(this, 'neutral');
            });
            
            document.getElementById('smileImage').addEventListener('change', function() {
                handleFileUpload(this, 'smile');
            });
        });

        // Import analysis functions
        async function loadAnalysisModule() {
            try {
                const module = await import('/src/presentation/controllers/ImageAnalysisHelpers.js');
                return module;
            } catch (error) {
                console.error('Failed to load analysis module:', error);
                showStatus('Failed to load analysis module', 'error');
                return null;
            }
        }

        // Run single analysis
        async function runSingleAnalysis() {
            if (!neutralImageData || !smileImageData) {
                showStatus('Please upload both neutral and smile images first', 'error');
                return;
            }

            showStatus('Running single analysis (extracting fresh landmarks)...', 'info');

            try {
                const analysisModule = await loadAnalysisModule();
                if (!analysisModule) return;

                // Extract fresh landmarks from images (same as real ImageAnalysisController)
                showStatus('Extracting landmarks from neutral image...', 'info');
                const neutralLandmarks = await extractLandmarksFromImageData(neutralImageData, 'neutral');

                showStatus('Extracting landmarks from smile image...', 'info');
                const smileLandmarks = await extractLandmarksFromImageData(smileImageData, 'smile');

                showStatus('Running analysis with fresh landmarks...', 'info');

                // Run mouth analysis using the exact same function as ImageAnalysisController
                const result = analysisModule.calculateEnhancedMouthAnalysis(neutralLandmarks, smileLandmarks);

                // Display results
                const resultsText = `
=== SINGLE ANALYSIS RESULTS ===
Timestamp: ${new Date().toISOString()}
Landmark Extraction: FRESH (like real analysis)

Mouth Analysis:
  Left Corner Movement: ${result.leftMovement?.toFixed(2) || 'N/A'}mm
  Right Corner Movement: ${result.rightMovement?.toFixed(2) || 'N/A'}mm
  Asymmetry Index: ${result.asymmetryIndex?.toFixed(4) || 'N/A'} (${(result.asymmetryIndex * 100)?.toFixed(1) || 'N/A'}%)
  Severity: ${result.severity || 'N/A'}
  Affected Side: ${result.affectedSide || 'N/A'}
  Analysis Method: ${result.analysisMethod || 'N/A'}

Landmark Counts:
  Neutral: ${neutralLandmarks.length} landmarks
  Smile: ${smileLandmarks.length} landmarks

Raw Data:
${JSON.stringify(result, null, 2)}
                `;

                document.getElementById('analysisResults').textContent = resultsText;
                showStatus('Analysis completed successfully', 'success');

                // Store result for consistency testing
                testResults.push({
                    timestamp: new Date().toISOString(),
                    result: result,
                    landmarkCounts: {
                        neutral: neutralLandmarks.length,
                        smile: smileLandmarks.length
                    }
                });

            } catch (error) {
                console.error('Analysis error:', error);
                showStatus(`Analysis failed: ${error.message}`, 'error');
                document.getElementById('analysisResults').textContent = `Error: ${error.message}`;
            }
        }

        // Run consistency test (multiple iterations)
        async function runConsistencyTest() {
            if (!neutralImageData || !smileImageData) {
                showStatus('Please upload both neutral and smile images first', 'error');
                return;
            }

            showStatus('Running consistency test (5 iterations with fresh landmark extraction)...', 'info');

            try {
                const analysisModule = await loadAnalysisModule();
                if (!analysisModule) return;

                const iterations = 5;
                const results = [];

                // Run analysis multiple times with fresh landmark extraction each time
                for (let i = 0; i < iterations; i++) {
                    showStatus(`Consistency test: Run ${i + 1}/${iterations} - Extracting landmarks...`, 'info');

                    // Extract fresh landmarks for each iteration (like real analysis)
                    const neutralLandmarks = await extractLandmarksFromImageData(neutralImageData, `neutral-run${i + 1}`);
                    const smileLandmarks = await extractLandmarksFromImageData(smileImageData, `smile-run${i + 1}`);

                    // Run analysis with fresh landmarks
                    const result = analysisModule.calculateEnhancedMouthAnalysis(neutralLandmarks, smileLandmarks);

                    results.push({
                        run: i + 1,
                        leftMovement: result.leftMovement,
                        rightMovement: result.rightMovement,
                        asymmetryIndex: result.asymmetryIndex,
                        asymmetryPercentage: result.asymmetryIndex * 100,
                        severity: result.severity,
                        analysisMethod: result.analysisMethod,
                        landmarkCounts: {
                            neutral: neutralLandmarks.length,
                            smile: smileLandmarks.length
                        }
                    });

                    console.log(`Run ${i + 1} completed: Left=${result.leftMovement?.toFixed(4)}, Right=${result.rightMovement?.toFixed(4)}, Asymmetry=${(result.asymmetryIndex * 100)?.toFixed(2)}%`);
                }

                // Display results in table
                displayConsistencyResults(results);
                analyzeConsistency(results);

                showStatus('Consistency test completed - Check if results vary like real analysis!', 'success');

            } catch (error) {
                console.error('Consistency test error:', error);
                showStatus(`Consistency test failed: ${error.message}`, 'error');
            }
        }

        // Display consistency test results in table
        function displayConsistencyResults(results) {
            const table = document.getElementById('consistencyTable');
            const tbody = document.getElementById('consistencyTableBody');

            tbody.innerHTML = '';

            results.forEach(result => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${result.run}</td>
                    <td>${result.leftMovement?.toFixed(2) || 'N/A'}</td>
                    <td>${result.rightMovement?.toFixed(2) || 'N/A'}</td>
                    <td>${result.asymmetryPercentage?.toFixed(1) || 'N/A'}%</td>
                    <td>${result.severity || 'N/A'}</td>
                    <td>${result.analysisMethod || 'N/A'}</td>
                `;
            });

            table.style.display = 'table';
        }

        // Analyze consistency of results
        function analyzeConsistency(results) {
            const leftMovements = results.map(r => r.leftMovement).filter(v => v !== null && v !== undefined);
            const rightMovements = results.map(r => r.rightMovement).filter(v => v !== null && v !== undefined);
            const asymmetries = results.map(r => r.asymmetryPercentage).filter(v => v !== null && v !== undefined);

            const stats = {
                leftMovement: calculateStats(leftMovements),
                rightMovement: calculateStats(rightMovements),
                asymmetryPercentage: calculateStats(asymmetries)
            };

            const analysisText = `
=== CONSISTENCY ANALYSIS ===
Total Runs: ${results.length}

Left Movement Statistics:
  Min: ${stats.leftMovement.min?.toFixed(4) || 'N/A'}mm
  Max: ${stats.leftMovement.max?.toFixed(4) || 'N/A'}mm
  Mean: ${stats.leftMovement.mean?.toFixed(4) || 'N/A'}mm
  Std Dev: ${stats.leftMovement.stdDev?.toFixed(4) || 'N/A'}mm
  Variance: ${stats.leftMovement.variance?.toFixed(6) || 'N/A'}

Right Movement Statistics:
  Min: ${stats.rightMovement.min?.toFixed(4) || 'N/A'}mm
  Max: ${stats.rightMovement.max?.toFixed(4) || 'N/A'}mm
  Mean: ${stats.rightMovement.mean?.toFixed(4) || 'N/A'}mm
  Std Dev: ${stats.rightMovement.stdDev?.toFixed(4) || 'N/A'}mm
  Variance: ${stats.rightMovement.variance?.toFixed(6) || 'N/A'}

Asymmetry Percentage Statistics:
  Min: ${stats.asymmetryPercentage.min?.toFixed(2) || 'N/A'}%
  Max: ${stats.asymmetryPercentage.max?.toFixed(2) || 'N/A'}%
  Mean: ${stats.asymmetryPercentage.mean?.toFixed(2) || 'N/A'}%
  Std Dev: ${stats.asymmetryPercentage.stdDev?.toFixed(4) || 'N/A'}%
  Variance: ${stats.asymmetryPercentage.variance?.toFixed(6) || 'N/A'}

Consistency Assessment:
${assessConsistency(stats)}
            `;

            document.getElementById('consistencyAnalysis').textContent = analysisText;
            document.getElementById('consistencyAnalysis').style.display = 'block';
        }

        // Calculate statistics for an array of values
        function calculateStats(values) {
            if (values.length === 0) return { min: null, max: null, mean: null, stdDev: null, variance: null };

            const min = Math.min(...values);
            const max = Math.max(...values);
            const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
            const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
            const stdDev = Math.sqrt(variance);

            return { min, max, mean, stdDev, variance };
        }

        // Assess consistency based on statistics
        function assessConsistency(stats) {
            const assessments = [];

            // Check left movement consistency
            if (stats.leftMovement.stdDev !== null) {
                if (stats.leftMovement.stdDev < 0.01) {
                    assessments.push('✅ Left movement: EXCELLENT consistency (σ < 0.01mm)');
                } else if (stats.leftMovement.stdDev < 0.1) {
                    assessments.push('✅ Left movement: GOOD consistency (σ < 0.1mm)');
                } else {
                    assessments.push('❌ Left movement: POOR consistency (σ ≥ 0.1mm)');
                }
            }

            // Check right movement consistency
            if (stats.rightMovement.stdDev !== null) {
                if (stats.rightMovement.stdDev < 0.01) {
                    assessments.push('✅ Right movement: EXCELLENT consistency (σ < 0.01mm)');
                } else if (stats.rightMovement.stdDev < 0.1) {
                    assessments.push('✅ Right movement: GOOD consistency (σ < 0.1mm)');
                } else {
                    assessments.push('❌ Right movement: POOR consistency (σ ≥ 0.1mm)');
                }
            }

            // Check asymmetry consistency
            if (stats.asymmetryPercentage.stdDev !== null) {
                if (stats.asymmetryPercentage.stdDev < 0.1) {
                    assessments.push('✅ Asymmetry: EXCELLENT consistency (σ < 0.1%)');
                } else if (stats.asymmetryPercentage.stdDev < 1.0) {
                    assessments.push('✅ Asymmetry: GOOD consistency (σ < 1.0%)');
                } else {
                    assessments.push('❌ Asymmetry: POOR consistency (σ ≥ 1.0%)');
                }
            }

            return assessments.join('\n');
        }

        async function validateLandmarks() {
            if (!neutralImageData || !smileImageData) {
                showStatus('Please upload both neutral and smile images first', 'error');
                return;
            }

            showStatus('Validating landmarks (extracting fresh landmarks)...', 'info');

            try {
                // Extract fresh landmarks for validation
                const neutralLandmarks = await extractLandmarksFromImageData(neutralImageData, 'neutral-validation');
                const smileLandmarks = await extractLandmarksFromImageData(smileImageData, 'smile-validation');

                const requiredLandmarks = [61, 291, 133, 362, 152, 468, 473];
                const neutralValidation = validateLandmarkSet(neutralLandmarks, 'Neutral', requiredLandmarks);
                const smileValidation = validateLandmarkSet(smileLandmarks, 'Smile', requiredLandmarks);

                const validationText = `
=== LANDMARK VALIDATION ===
Landmark Extraction: FRESH (like real analysis)

${neutralValidation}

${smileValidation}

Required Landmarks:
  61  - Left mouth corner
  291 - Right mouth corner
  133 - Left inner eye corner
  362 - Right inner eye corner
  152 - Chin center
  468 - Left pupil center
  473 - Right pupil center
                `;

                document.getElementById('landmarkResults').textContent = validationText;
                showStatus('Landmark validation completed', 'success');

            } catch (error) {
                console.error('Landmark validation error:', error);
                showStatus(`Landmark validation failed: ${error.message}`, 'error');
                document.getElementById('landmarkResults').textContent = `Error: ${error.message}`;
            }
        }

        function validateLandmarkSet(landmarks, name, requiredLandmarks) {
            const results = [];
            results.push(`${name} Image Landmarks (${landmarks.length} total):`);

            requiredLandmarks.forEach(idx => {
                const landmark = landmarks[idx];
                if (landmark && typeof landmark.x === 'number' && typeof landmark.y === 'number') {
                    results.push(`  ✅ Landmark ${idx}: (${landmark.x.toFixed(4)}, ${landmark.y.toFixed(4)})`);
                } else {
                    results.push(`  ❌ Landmark ${idx}: MISSING or INVALID`);
                }
            });

            return results.join('\n');
        }

        async function showLandmarkDetails() {
            if (!neutralImageData || !smileImageData) {
                showStatus('Please upload both neutral and smile images first', 'error');
                return;
            }

            showStatus('Extracting landmarks for detailed view...', 'info');

            try {
                // Extract fresh landmarks for details
                const neutralLandmarks = await extractLandmarksFromImageData(neutralImageData, 'neutral-details');
                const smileLandmarks = await extractLandmarksFromImageData(smileImageData, 'smile-details');

                const detailsText = `
=== DETAILED LANDMARK COORDINATES ===
Landmark Extraction: FRESH (like real analysis)

Neutral Image (${neutralLandmarks.length} landmarks):
${JSON.stringify(neutralLandmarks.slice(0, 10), null, 2)}
... (showing first 10 landmarks)

Smile Image (${smileLandmarks.length} landmarks):
${JSON.stringify(smileLandmarks.slice(0, 10), null, 2)}
... (showing first 10 landmarks)

Key Landmarks for Analysis:
Neutral - Landmark 61 (left mouth): ${JSON.stringify(neutralLandmarks[61], null, 2)}
Neutral - Landmark 291 (right mouth): ${JSON.stringify(neutralLandmarks[291], null, 2)}
Smile - Landmark 61 (left mouth): ${JSON.stringify(smileLandmarks[61], null, 2)}
Smile - Landmark 291 (right mouth): ${JSON.stringify(smileLandmarks[291], null, 2)}
                `;

                document.getElementById('landmarkResults').textContent = detailsText;
                showStatus('Landmark details extracted', 'success');

            } catch (error) {
                console.error('Landmark details error:', error);
                showStatus(`Failed to extract landmark details: ${error.message}`, 'error');
                document.getElementById('landmarkResults').textContent = `Error: ${error.message}`;
            }
        }

        function showDebugInfo() {
            const debugText = `
=== DEBUG INFORMATION ===
Timestamp: ${new Date().toISOString()}

MediaPipe Status:
  FaceMesh Initialized: ${faceMesh ? 'Yes' : 'No'}

Image Data:
  Neutral Image: ${neutralImageData ? 'Uploaded' : 'Not uploaded'}
  Smile Image: ${smileImageData ? 'Uploaded' : 'Not uploaded'}

Landmark Extraction Mode:
  Method: FRESH extraction on every test (like real analysis)
  Timing: 1500ms wait per extraction (matches ImageAnalysisController)

Test Results History:
  Total Tests Run: ${testResults.length}

Recent Test Results:
${testResults.slice(-3).map((test, i) => `
  Test ${testResults.length - 2 + i}:
    Left Movement: ${test.result.leftMovement?.toFixed(4) || 'N/A'}mm
    Right Movement: ${test.result.rightMovement?.toFixed(4) || 'N/A'}mm
    Asymmetry: ${(test.result.asymmetryIndex * 100)?.toFixed(2) || 'N/A'}%
`).join('')}

Browser Information:
  User Agent: ${navigator.userAgent}
  Platform: ${navigator.platform}
  Language: ${navigator.language}
            `;

            document.getElementById('debugInfo').textContent = debugText;
        }

        function exportTestResults() {
            if (testResults.length === 0) {
                showStatus('No test results to export', 'error');
                return;
            }

            const exportData = {
                timestamp: new Date().toISOString(),
                testResults: testResults,
                browserInfo: {
                    userAgent: navigator.userAgent,
                    platform: navigator.platform,
                    language: navigator.language
                }
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `image-analysis-test-results-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showStatus('Test results exported successfully', 'success');
        }

        function clearResults() {
            document.getElementById('analysisResults').textContent = 'No analysis performed yet...';
            document.getElementById('landmarkResults').textContent = 'Click "Validate Required Landmarks" to check landmark detection...';
            document.getElementById('debugInfo').textContent = 'Click "Show Debug Info" to see detailed analysis information...';
            document.getElementById('consistencyTable').style.display = 'none';
            document.getElementById('consistencyAnalysis').style.display = 'none';
            testResults = [];
        }
    </script>
</body>
</html>
