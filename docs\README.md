# Facial Symmetry Analysis Application

A professional web-based facial symmetry analysis tool built with TypeScript, Clean Architecture, and MediaPipe face detection.

## 🚀 Quick Start

### Development
```bash
npm install
npm start
```
Visit http://localhost:3000

### Production
```bash
npm install
npm run serve
```

## 📋 Available Scripts

| Command | Description |
|---------|-------------|
| `npm start` | Start development server with TypeScript |
| `npm run serve` | **Build and run production server** |
| `npm run build` | Build both client and server |
| `npm run clean` | Remove build directories |
| `npm run rebuild-and-run` | Clean build and run production |

See [SCRIPTS_DOCUMENTATION.md](SCRIPTS_DOCUMENTATION.md) for complete script reference.

## 🏗️ Architecture

This application follows **Clean Architecture** principles:

- **Domain Layer**: Core business logic (entities, value objects, services)
- **Application Layer**: Use cases and orchestration
- **Infrastructure Layer**: External concerns (camera, storage, config)
- **Presentation Layer**: UI controllers and components

See [CLEAN_ARCHITECTURE.md](CLEAN_ARCHITECTURE.md) for detailed architecture documentation.

## ✨ Features

### Enhanced Visualization
- **Face circle overlay** with automatic detection
- **Color-coded landmarks** for different facial features
- **Real-time metric highlighting** based on current exam action
- **Professional UI** with green-themed styling

### Facial Analysis
- **6 exam actions**: Neutral face, eyebrow raise, eye close, smile, lip pucker, cheek puff
- **5 symmetry metrics**: Forehead, eye gap, smile, lip, nose
- **Scoring system**: 0=Normal, 1=Mild, 2=Moderate, 3=Moderately Severe, 4=Severe
- **Export options**: CSV and Markdown formats

### Technical Features
- **TypeScript** for type safety
- **Clean Architecture** for maintainability
- **MediaPipe** for accurate face detection
- **Express.js** server for static file serving
- **Modular design** with dependency injection

## 🎯 Usage

1. **Start the application** using one of the scripts above
2. **Fill patient information** (ID, name, age)
3. **Allow camera access** when prompted
4. **Follow exam instructions** for each action
5. **View real-time feedback** with enhanced landmarks
6. **Export results** when exam is complete

## 📁 Project Structure

```
src/
├── domain/           # Core business logic
├── application/      # Use cases & orchestration  
├── infrastructure/   # External concerns
└── presentation/     # UI & user interactions
```

## 🔧 Development

### Prerequisites
- Node.js 18+
- NPM 9+
- Modern web browser with camera support

### Setup
```bash
git clone <repository>
cd web_browser
npm install
```

### Development Workflow
```bash
npm start              # Start development server
# Make changes to TypeScript files
# Server automatically recompiles
```

### Production Build
```bash
npm run build         # Build TypeScript to JavaScript
npm run start:prod    # Run production server
```

## 📚 Documentation

- [SCRIPTS_DOCUMENTATION.md](SCRIPTS_DOCUMENTATION.md) - Complete NPM scripts reference
- [CLEAN_ARCHITECTURE.md](CLEAN_ARCHITECTURE.md) - Architecture overview
- [VISUALIZATION_FEATURES.md](VISUALIZATION_FEATURES.md) - Enhanced visualization features
- [CLEANUP_SUMMARY.md](CLEANUP_SUMMARY.md) - Recent improvements and cleanup

## 🛠️ Troubleshooting

### Camera Issues
- Ensure camera permissions are granted
- Check browser console for MediaPipe loading errors
- Try different browsers (Chrome recommended)

### Build Issues
```bash
npm run clean         # Clean build directories
npm run rebuild       # Fresh build
```

### Port Conflicts
```bash
PORT=3001 npm start   # Use different port
```

## 📄 License

ISC License

## 🤝 Contributing

1. Follow Clean Architecture principles
2. Use TypeScript for all new code
3. Add appropriate documentation
4. Test changes thoroughly

---

**Built with ❤️ using TypeScript, Clean Architecture, and MediaPipe**
