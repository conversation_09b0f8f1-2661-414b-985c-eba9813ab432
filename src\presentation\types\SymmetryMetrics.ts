import { EyebrowAnalysisResult, EyeAnalysisResult, MouthAnalysisResult, AsymmetryResult } from '../../shared/types/index.js';

export interface EnhancedSymmetryMetrics {
  eyebrowAnalysis: EyebrowAnalysisResult;
  eyeAnalysis: EyeAnalysisResult;
  mouthAnalysis: MouthAnalysisResult;
  overallScore: number;
  asymmetries: AsymmetryResult[];
  dataQuality: {
    eyebrow: number;
    eye: number;
    mouth: number;
  };
}

export interface RealTimeSymmetryMetrics {
  eyebrowMovement: {
    left: number;
    right: number;
    asymmetry: number;
  };
  eyeClosure: {
    left: number;
    right: number;
    asymmetry: number;
  };
  mouthMovement: {
    left: number;
    right: number;
    asymmetry: number;
    horizontalDistances: {
      left: number;
      right: number;
    };
    verticalDistances: {
      left: number;
      right: number;
    };
  };
  overallAsymmetry: number;
  qualityScore: number;
}

export interface SynkinesisResult {
  present: boolean;
  severity: string;
  type: string;
  description: string;
  eyeApertureChange?: number;
  mouthMovementChange?: number;
  events: Array<{
    timestamp: number;
    severity: string;
    measurement: number;
  }>;
}
